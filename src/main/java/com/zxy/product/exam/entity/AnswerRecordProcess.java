package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.AnswerRecordProcessEntity;

public class AnswerRecordProcess extends AnswerRecordProcessEntity {

    private static final long serialVersionUID = 1772775226402839887L;

    public static final Integer HANDLE_FLAG_INSERT = 0;
    public static final Integer HANDLE_FLAG_UPDATE = 1;

    private Integer handleFlag;

    private QuestionCopy questionCopy;

    public Integer getHandleFlag() {
        return handleFlag;
    }

    public void setHandleFlag(Integer handleFlag) {
        this.handleFlag = handleFlag;
    }

    public QuestionCopy getQuestionCopy() {
        return questionCopy;
    }

    public void setQuestionCopy(QuestionCopy questionCopy) {
        this.questionCopy = questionCopy;
    }
}
