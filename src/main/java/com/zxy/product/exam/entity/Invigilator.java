package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.InvigilatorEntity;

import java.util.ArrayList;
import java.util.List;


public class Invigilator extends InvigilatorEntity  {

    private static final long serialVersionUID = 5676849906579432743L;

    public static final String URI = "exam/invigilator";

    public static final String CLOUD_URI = "exam/cloud-invigilator";

    /** 监考分页列表查询类型：1：监考管理独立菜单 2：考试管理-监考配置 */
    public static final Integer FETCH_TYPE_MANAGEMENT = 1;
    public static final Integer FETCH_TYPE_CONFIG = 2;

    /** 导入模板 */
    // 限制条数
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;
    // sheet 导入模板
    public static final String TEMPLATE_INDEX = "序号";
    public static final String TEMPLATE_MEMBER_NAME = "员工编号(必填)";
    public static final String TEMPLATE_EXAM_BATCH_NAME = "批次(必填)";
    public static final String TEMPLATE_ORGANIZATION_CODE = "监考组织编码(必填，如有多个组织，组织间用英文逗号隔开)";
    public static final Integer TEMPLATE_MEMBER_NAME_COLUMN = 0;
    public static final Integer TEMPLATE_EXAM_BATCH_NAME_COLUMN = 1;
    public static final Integer TEMPLATE_ORGANIZATION_CODE_COLUMN = 2;

    public static final Integer TEMPLATE_MEMBER_NAME_EXAM_COLUMN = 0;
    public static final Integer TEMPLATE_ORGANIZATION_CODE_EXAM_COLUMN = 1;
    // sheet 考试
    public static final String TEMPLATE_EXAM_EXAM_ID = "考试id";
    public static final String TEMPLATE_EXAM_EXAM_NAME = "考试名称";
    // sheet 组织
    public static final String TEMPLATE_ORGANIZATION_ORGANIZATION_NAME = "部门";
    public static final String TEMPLATE_ORGANIZATION_ORGANIZATION_CODE = "部门编码";
    public static final String TEMPLATE_ORGANIZATION_ORGANIZATION_DEPTH = "层级";
    // 组织编码多个组织分隔符
    public static final String IMPORT_SPLIT_STR = ",";

    public static final int CACHE_TIME = 60 * 5; // 导入Excel读取结果缓存时间:5min
    public static final String CACHE_KEY_INSERT_LIST = "correctInsertList";
    public static final String CACHE_KEY_UPDATE_LIST = "correctUpdateList";
    public static final String CACHE_KEY_ERROR_LIST = "errorList";
    public static final String CACHE_KEY_EXAM_LIST = "exams";
    public static final String CACHE_KEY_ORGANIZATION_LIST = "organizations";

    // 是否监考整场考试：0否 1是
    public static final Integer IS_GRANT_ALL_NO = 0;
    public static final Integer IS_GRANT_ALL_YES = 1;

    public static final String GRANT_ALL_TEXT = "全部人员";
    public static final String GRANT_NO_TEXT = "无";
    public static final String NORMAL_EXAM_BATCH = "NORMAL_EXAM_BATCH"; //单场考试导入监考老师时普通考试没有批次，用来支持共用导入方法


    private Exam exam;
    private Member member;
    private String invigilatorOrgStr;
    private List<InvigilatorGrant> invigilatorGrants;
    private int row; // 行数，导入时辅助
    private String organizationCodeStr;
    private List<RowError> errors = new ArrayList<>();
    private List<String> organizationCodes;
    private List<String> organizationIds;
    private Boolean hasExists = false; // 是否已经存在，导入时辅助字段
    private String examBatch;// 批次，导入时候辅助字段

    private Integer count;

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Exam getExam() {
        return exam;
    }

    public void setExam(Exam exam) {
        this.exam = exam;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public String getInvigilatorOrgStr() {
        return invigilatorOrgStr;
    }

    public void setInvigilatorOrgStr(String invigilatorOrgStr) {
        this.invigilatorOrgStr = invigilatorOrgStr;
    }

    public void setInvigilatorGrants(List<InvigilatorGrant> invigilatorGrants) {
        this.invigilatorGrants = invigilatorGrants;
    }

    public List<InvigilatorGrant> getInvigilatorGrants() {
        return invigilatorGrants;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public List<String> getOrganizationIds() {
        return organizationIds;
    }

    public void setOrganizationIds(List<String> organizationIds) {
        this.organizationIds = organizationIds;
    }

    public Boolean getHasExists() {
        return hasExists;
    }

    public void setHasExists(Boolean hasExists) {
        this.hasExists = hasExists;
    }

    public List<String> getOrganizationCodes() {
        return organizationCodes;
    }

    public void setOrganizationCodes(List<String> organizationCodes) {
        this.organizationCodes = organizationCodes;
    }

    public String getExamBatch() {
        return examBatch;
    }

    public void setExamBatch(String examBatch) {
        this.examBatch = examBatch;
    }

    public String getOrganizationCodeStr() {
        return organizationCodeStr;
    }

    public void setOrganizationCodeStr(String organizationCodeStr) {
        this.organizationCodeStr = organizationCodeStr;
    }
}
