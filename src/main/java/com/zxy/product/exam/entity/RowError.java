package com.zxy.product.exam.entity;

import java.io.Serializable;

public class RowError implements Serializable {
    /**
    *
    */
    private static final long serialVersionUID = -6673084227883126461L;
    private Integer row;
    private Integer column;
    private Integer code;

    public RowError(int row, int column, Integer code) {
        this.row = row;
        this.column = column;
        this.code = code;
    }

    public RowError() {
        super();
    }

    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public Integer getColumn() {
        return column;
    }

    public void setColumn(Integer column) {
        this.column = column;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
