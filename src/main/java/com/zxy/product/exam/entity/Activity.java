package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ActivityEntity;

public class Activity extends ActivityEntity{

    private static final long serialVersionUID = 2241509839295200970L;

    /** 培训班级 */
    public static final int TYPE_TRAIN_CLASS = 1;
    /** 直播 */
    public static final int TYPE_LIVE = 2;
    /** 考试 */
    public static final int TYPE_EXAM = 3;
    /** 调研活动 */
    public static final int TYPE_RESEARCH_ACTIVITY = 4;
    /** mooc */
    public static final int TYPE_MOOC = 5;
    /** 咪咕直播 */
    public static final int TYPE_MIGU_LIVE = 6;

    /** 活动首页 查询状态  1： 正在进行 */
    public static final Integer ACTIVITY_HOME_STATUS_RUNNING = 1;
    /** 活动首页 查询状态  2： 即将开始 */
    public static final Integer ACTIVITY_HOME_STATUS_NOT_START = 2;
    /** 活动首页 查询状态  3： 往期回顾 */
    public static final Integer ACTIVITY_HOME_STATUS_FINISH = 3;

    /** 活动 适用终端  0： 全部（PC&APP）*/
    public static final Integer CLIENT_ALL = 0;
    /** 活动 适用终端 1：PC */
    public static final Integer CLIENT_PC = 1;
    /** 活动 适用终端 2：APP */
    public static final Integer CLIENT_APP = 2;

    /** 是否推荐到活动首页 1：是 */
    public static final Integer IS_RECOMMEND_YES = 1;
    /** 是否推荐到活动首页  0： 否*/
    public static final Integer IS_RECOMMEND_NO = 0;

    /** 状态：未开始*/
    public static final Integer STATUS_UNSTART = 0;
    /** 状态：报名中*/
    public static final Integer STATUS_SIGNUPING = 1;
    /** 状态：进行中*/
    public static final Integer STATUS_STARTING = 2;
    /** 状态：已完成*/
    public static final Integer STATUS_FINISHED = 3;

    /** 最大推荐数 */
    public static final Integer RECOMMEN_LIMIT = 8;
    /** 是否开放，例如：培训班是否开放报名 */
    public static final Integer IS_OPEN_YES = 1;

    /** 是否开放，例如：培训班是否开放报名 */
    public static final Integer IS_OPEN_NO = 0;

    /**
     * 权限URI
     */
    public static final String URI = "operation/activity-recommend";

    private Organization organization;
    private String  coverId;// 封面

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public String getCoverId() {
        return coverId;
    }

    public void setCoverId(String coverId) {
        this.coverId = coverId;
    }
}
