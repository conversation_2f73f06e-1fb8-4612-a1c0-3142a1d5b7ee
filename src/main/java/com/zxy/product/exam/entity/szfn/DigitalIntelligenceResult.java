package com.zxy.product.exam.entity.szfn;

import com.zxy.product.exam.dto.CourseInfoDto;
import com.zxy.product.exam.jooq.tables.pojos.DigitalIntelligenceResultEntity;

import java.util.List;

public class DigitalIntelligenceResult extends DigitalIntelligenceResultEntity {
    private static final long serialVersionUID = 1L;


    public static final Integer VIEW_YES = 1;

    private List<CourseInfoDto> courseInfos;

    public List<CourseInfoDto> getCourseInfos() {
        return courseInfos;
    }

    public void setCourseInfos(List<CourseInfoDto> courseInfos) {
        this.courseInfos = courseInfos;
    }
}
