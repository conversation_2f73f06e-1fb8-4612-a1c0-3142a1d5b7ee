package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.GridAuditEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GridAudit extends GridAuditEntity {
    private static final long serialVersionUID = 1986665017094359900L;

    /**
     * 被修改，0否
     */
    public static final Integer NO_UPDATE = 0;
    /**
     * 被修改，1是
     */
    public static final Integer UPDATED = 1;
    /**修改次数上限*/
    public static final Integer MODIFICATION_LIMIT=6;
    /**修改次数耗尽*/
    public static final Integer MODIFICATIONS_EXHAUSTED=0;

    public static final String TRUE = "是";
    public static final String FALSE = "否";
    public static final Integer YES = 1;
    public static final Integer NO = 0;

    private Member member;
    private Integer index;
    private GridLevel gridLevel;
    private int row; // 行数，导入时辅助
    private List<RowError> errors = new ArrayList<>();
    private String professionalKnowledge4String;
    private String keyCapabilities4String;
    private String organizationalFeedback4String;
    private String level4String;
    private String pass4String;
    private String authTime4String;



    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }


    public GridLevel getGridLevel() {
        return gridLevel;
    }

    public void setGridLevel(GridLevel gridLevel) {
        this.gridLevel = gridLevel;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }


    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public String getAuthTime4String() {
        return authTime4String;
    }

    public void setAuthTime4String(String authTime4String) {
        this.authTime4String = authTime4String;
    }

    public String getProfessionalKnowledge4String() {
        return professionalKnowledge4String;
    }

    public void setProfessionalKnowledge4String(String professionalKnowledge4String) {
        this.professionalKnowledge4String = professionalKnowledge4String;
    }

    public String getKeyCapabilities4String() {
        return keyCapabilities4String;
    }

    public void setKeyCapabilities4String(String keyCapabilities4String) {
        this.keyCapabilities4String = keyCapabilities4String;
    }

    public String getOrganizationalFeedback4String() {
        return organizationalFeedback4String;
    }

    public void setOrganizationalFeedback4String(String organizationalFeedback4String) {
        this.organizationalFeedback4String = organizationalFeedback4String;
    }

    public String getLevel4String() {
        return level4String;
    }

    public void setLevel4String(String level4String) {
        this.level4String = level4String;
    }

    public String getPass4String() {
        return pass4String;
    }

    public void setPass4String(String pass4String) {
        this.pass4String = pass4String;
    }
}
