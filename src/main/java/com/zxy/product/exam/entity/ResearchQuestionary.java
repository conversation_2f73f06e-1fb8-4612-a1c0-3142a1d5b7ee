package com.zxy.product.exam.entity;

import java.util.List;

import com.zxy.product.exam.dto.ResearchAnswerRecordMap;
import com.zxy.product.exam.jooq.tables.pojos.ResearchQuestionaryEntity;

public class ResearchQuestionary extends ResearchQuestionaryEntity{

	/**
	 *
	 */
	private static final long serialVersionUID = -1502403407055982804L;

	public static final String URI = "exam/research-activity";

	// 缓存时间
	public static final int CACHE_TIME = 60 * 60 * 24 * 2;

	/** 1调研活动  */
	public static final Integer TYPE_RESEARCH_ACTIVITY = 1;
	/** 2调研问卷 */
	public static final Integer TYPE_RESEARCH_QUESTIONARY = 2;
	/** 3评估问卷 */
	public static final Integer TYPE_EVALUATE_QUESTIONARY = 3;

	/** 1未发布 */
	public static final Integer STATUS_NO_PUBLISH = 1;
	/** 2未开始 */
	public static final Integer STATUS_NO_START = 2;
	/** 3进行中 */
	public static final Integer STATUS_PROGRESS = 3;// 对于评估问卷和调研问卷没有未开始和已结束的状态，已发布的状态也为3
	/** 4已结束 */
	public static final Integer STATUS_OVER = 4;
	/** 5已撤销 */
	public static final Integer STATUS_CANCEL = 5;
	/** 6发布中 */
	public static final Integer STATUS_PUBLISHING = 6;


	/** 复制 名称 后缀 */
	public static final String COPY_PREX = "[复制]";//(copy)

	/** 调研 题库ID */
	public static final String RESEARCH_QUESTIONARY_QUESTION_DEPOT_ID = "RESEARCH_QUESTIONARY_QUESTION_DEPOT";

	/** 推送个人中心 1 是*/
	public static final Integer PERSON_CENTER_PUSH = 1;

	/** 是否匿名-1是  */
    public static final Integer IS_ANONYMITY_Y = 1;
    /** 是否匿名-0否 */
    public static final Integer IS_ANONYMITY_N = 0;

	/** 允许查看统计 1是*/
	public static final Integer PERMIT_VIEW_COUNT = 1;
	/** 允许查看统计 0否*/
	public static final Integer PERMIT_VIEW_COUNT_NO = 0;

	/** 答题方式：1 一页所有题 */
    public static final Integer ANSWER_RULE_MUTIPLE = 1;

	/** 导入问卷模板 */
	public static final String TEMPLATE_INDEX = "序号";

	public static final String TEMPLATE_QUESTION_CONTENT = "试题题干(必填)";

	public static final String TEMPLATE_QUESTION_TYPE = "试题类型(必填)";

	public static final String TEMPLATE_QUESTION_OPTION = "选项（用'|'隔开）";

	public static final String TEMPLATE_QUESTION_SCORE = "分数（用'|'隔开）";

	public static final String TEMPLATE_DIMENSION_NAME = "维度名称";

	public static final String TEMPLATE_DIMENSION_CODE = "维度编码";
	/** 活动-调研活动、调研问卷、评估问卷 */
	public static final Integer SOURCE_TYPE_ACTIVITY = 1;
	/** 课程  */
	public static final Integer SOURCE_TYPE_COURSE = 2;
	/** 专题 */
	public static final Integer SOURCE_TYPE_SUJECT = 3;
	/** 班级 */
	public static final Integer SOURCE_TYPE_CLASS = 4;
	/** 直播 */
    public static final Integer SOURCE_TYPE_GENSEE = 5;


	public static final Integer RELATIVE_QUESRIONARYS_LIMIT_DEFAULT = 3; // 相关调研默认最多显示条数
	public static final Integer RELATIVE_MEMBERS_LIMIT_DEFAULT = 9;// 他们也参加调研默认最多显示条数
	public static final Integer SUMMARY_ANSWER_LIMIT_DEFAULT = 11;// 问答题默认查询多少条

	private String publicClient;//自适应终端

	private String organizationName;//归属部门名称

	private Organization organization;

	private Member publishMember;

	private Organization publishOrganization;

	private List<Dimension> dimensions;

	private List<AudienceItem> audienceItems;

	private List<BusinessTopic> topics;

	private List<ResearchAnswerRecordMap> researchAnswerRecordMaps;

	private ResearchRecord researchRecord;

	private Member creator;

	/**
	 * 是否推送学习计划 1是，0否
	 */
	private Integer pushLearningPlan;

	/**
	 * 提醒时间
	 */
	private Long remindTime;

	/**
	 * 提醒类型  1:短信  2:站内信  3:邮件 4:oa 逗号分隔
	 */
	private String remindType;

	/**
	 * 评估平均分
	 */
	private Long averageScore;


	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Member getPublishMember() {
		return publishMember;
	}

	public void setPublishMember(Member publishMember) {
		this.publishMember = publishMember;
	}

	public Organization getPublishOrganization() {
		return publishOrganization;
	}

	public void setPublishOrganization(Organization publishOrganization) {
		this.publishOrganization = publishOrganization;
	}

	public List<Dimension> getDimensions() {
		return dimensions;
	}

	public void setDimensions(List<Dimension> dimensions) {
		this.dimensions = dimensions;
	}

	public List<AudienceItem> getAudienceItems() {
		return audienceItems;
	}

	public void setAudienceItems(List<AudienceItem> audienceItems) {
		this.audienceItems = audienceItems;
	}

	public List<ResearchAnswerRecordMap> getResearchAnswerRecordMaps() {
		return researchAnswerRecordMaps;
	}

	public void setResearchAnswerRecordMaps(List<ResearchAnswerRecordMap> researchAnswerRecordMaps) {
		this.researchAnswerRecordMaps = researchAnswerRecordMaps;
	}

	public List<BusinessTopic> getTopics() {
		return topics;
	}

	public void setTopics(List<BusinessTopic> topics) {
		this.topics = topics;
	}

	public ResearchRecord getResearchRecord() {
		return researchRecord;
	}

	public void setResearchRecord(ResearchRecord researchRecord) {
		this.researchRecord = researchRecord;
	}

	public Member getCreator() {
		return creator;
	}

	public void setCreator(Member creator) {
		this.creator = creator;
	}

	public Long getAverageScore() {
		return averageScore;
	}

	public void setAverageScore(Long averageScore) {
		this.averageScore = averageScore;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getPublicClient() {
		return publicClient;
	}

	public void setPublicClient(String publicClient) {
		this.publicClient = publicClient;
	}

	public Integer getPushLearningPlan() {
		return pushLearningPlan;
	}

	public void setPushLearningPlan(Integer pushLearningPlan) {
		this.pushLearningPlan = pushLearningPlan;
	}

	public Long getRemindTime() {
		return remindTime;
	}

	public void setRemindTime(Long remindTime) {
		this.remindTime = remindTime;
	}

	public String getRemindType() {
		return remindType;
	}

	public void setRemindType(String remindType) {
		this.remindType = remindType;
	}
}
