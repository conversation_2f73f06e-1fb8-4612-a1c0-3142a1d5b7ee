package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.PersonalTemplateEntity;
/**
 * 认证考试模板表
 * <AUTHOR>
 *
 */
public class PersonalTemplate extends PersonalTemplateEntity{


	private static final long serialVersionUID = 735128981389433128L;

	private String idcard;
	private String mail;
	private String phone;
	private String orgName;

	public String getIdcard() {
		return idcard;
	}
	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	//专业实体
    private Profession peProfession;
	public Profession getPeProfession() {
		return peProfession;
	}
	public void setPeProfession(Profession peProfession) {
		this.peProfession = peProfession;
	}
	//设备实体
	private EquipmentType equipmentType;
	public EquipmentType getEquipmentType() {
		return equipmentType;
	}
	public void setEquipmentType(EquipmentType equipmentType) {
		this.equipmentType = equipmentType;
	}



}
