package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.CertificateRecordEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class CertificateRecord extends CertificateRecordEntity{

    private static final long serialVersionUID = 2666151327919743476L;

    /**
     * 定制化强基证书id
     */
    public static final String QJ_CERTIFICATE = "qjCertificate";

    public static final String URI = "exam/certificate-record";
    public static final String CLOUD_URI = "exam/cloud-certificate-record";

    public static final String CERTIFACATE_TYPE_EXAM = "001";

    public static final String CERTIFICATE_TYPE_GRID = "004";

    public static final String CERTIFACATE_NETWORK_TEMPLATE_ID = "4";

    public static final String CERTIFACATE_Cloud_TEMPLATE_ID = "cloudCertificate";

    /** 导入问卷模板 */
    public static final String TEMPLATE_INDEX = "序号";
    public static final String TEMPLATE_MEMBER_NAME = "员工编号(必填)";
    public static final String TEMPLATE_PROFESSION = "专业(必填)";
    public static final String TEMPLATE_CLOUD_PROFESSION = "专业(必填，初级为初级专业)";
    public static final String TEMPLATE_SUB_PROFESSION = "子专业(必填)";
    public static final String TEMPLATE_SCORE = "考试分数(必填，60-100)";
    public static final String TEMPLATE_EQUIPMENT_TYPE = "设备型号(必填)";
    public static final String TEMPLATE_LEVEL = "等级(必填)";
//    public static final String TEMPLATE_SCORE = "成绩(必填)";
    public static final String TEMPLATE_REASON = "发放原因(非必填)";
    public static final String TEMPLATE_PROFESSION_ID = "专业id";
    public static final String TEMPLATE_PROFESSION_NAME = "专业名称";
    public static final String TEMPLATE_SUB_PROFESSION_ID = "子专业id";
    public static final String TEMPLATE_SUB_PROFESSION_NAME = "子专业名称";
    public static final String TEMPLATE_EQUIPMENT_ID = "设备id";
    public static final String TEMPLATE_EQUIPMENT_NAME = "设备名称";
    public static final String TEMPLATE_LEVEL_NAME = "等级名称";
    public static final Integer TEMPLATE_DATA_LIMIT = 5000;
    public static final Integer TEMPLATE_DATA_SCORE_DEFAULT = 89;

    public static final Integer TEMPLATE_MEMBER_NAME_COLUMN = 0;
    public static final Integer TEMPLATE_PROFESSION_COLUMN = 1;
    public static final Integer TEMPLATE_SUB_PROFESSION_COLUMN= 2;
    public static final Integer TEMPLATE_EQUIPMENT_TYPE_COLUMN = 3;
    public static final Integer TEMPLATE_LEVEL_COLUMN = 4;
//    public static final Integer TEMPLATE_SCORE_COLUMN = 5;
    public static final Integer TEMPLATE_REASON_COLUMN = 5;

    public static final String LEVEL_NAME_KEY = "levelName";

    /**
     * 政企id
     */
    public static final String CLOUD_ID = "147435546";


    // 成绩级别，0不及格 1及格 2良好 3优秀
    public static final Integer SCORE_LEVEL_NOT_PASS = 0;
    public static final Integer SCORE_LEVEL_PASS = 1;
    public static final Integer SCORE_LEVEL_GOOD = 2;
    public static final Integer SCORE_LEVEL_EXCELLENT = 3;
    // 获取方式，0自考 1手动发放
    public static final Integer ACCESS_TYPE_EXAM = 0;
    public static final Integer ACCESS_TYPE_IMPORT = 1;
    // 是否最新的证书，0否 1是
    public static final Integer IS_CURRENT_NO = 0;
    public static final Integer IS_CURRENT_YES = 1;

    // 是否为云改考试证书，0否 1是
    public static final Integer IS_CLOUD_NO = 0;
    public static final Integer IS_CLOUD_YES = 1;

    // 是否为网格考试证书，0否 1是
    public static final Integer IS_GRID_NO = 0;
    public static final Integer IS_GRID_YES = 1;

    // 导入证书成绩分数规则：90分以上（含）优秀，70（含）-90良好，60（含）-70及格，小于60不及格
    public static final Integer SCORE_EXCELLENT = 90;
    public static final Integer SCORE_GOOD = 70;
    public static final Integer SCORE_PASS = 60;

    // 通过状态：通过状态，0不通过 1通过
    public static final Integer PASS_STATUS_NOT_PASS = 0;
    public static final Integer PASS_STATUS_PASS = 1;

    private Member member;
    private Profession profession; // 专业
    private Profession subProfession; // 子专业
    private ProfessionLevel professionLevel; // 等级
    private EquipmentType equipmentType;// 设备
    private Exam exam; //考试
    private int row; // 行数，导入时辅助
    private List<RowError> errors = new ArrayList<>();
    private Boolean hasExists = false; // 是否已经存在，导入时辅助字段

    private CloudProfession cloudProfession; // 专业
    private CloudLevel cloudLevel; // 等级

    private GridLevel gridLevel; // 网格长等级

    private String textScore; // 导入文件中的分数

    public String getTextScore() {
        return textScore;
    }

    public void setTextScore(String textScore) {
        this.textScore = textScore;
    }

    public CloudProfession getCloudProfession() {
        return cloudProfession;
    }

    public void setCloudProfession(CloudProfession cloudProfession) {
        this.cloudProfession = cloudProfession;
    }

    public CloudLevel getCloudLevel() {
        return cloudLevel;
    }

    public void setCloudLevel(CloudLevel cloudLevel) {
        this.cloudLevel = cloudLevel;
    }

    public Exam getExam() {
		return exam;
	}

	public void setExam(Exam exam) {
		this.exam = exam;
	}

	public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Profession getProfession() {
        return profession;
    }

    public void setProfession(Profession profession) {
        this.profession = profession;
    }

    public Profession getSubProfession() {
        return subProfession;
    }

    public void setSubProfession(Profession subProfession) {
        this.subProfession = subProfession;
    }

    public ProfessionLevel getProfessionLevel() {
        return professionLevel;
    }

    public void setProfessionLevel(ProfessionLevel professionLevel) {
        this.professionLevel = professionLevel;
    }

    public EquipmentType getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(EquipmentType equipmentType) {
        this.equipmentType = equipmentType;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public Boolean getHasExists() {
        return hasExists;
    }

    public void setHasExists(Boolean hasExists) {
        this.hasExists = hasExists;
    }

    public GridLevel getGridLevel() { return gridLevel; }

    public void setGridLevel(GridLevel gridLevel) { this.gridLevel = gridLevel; }

    /**
     * 拼接证书名称通用方法
     * <ul>
     * <li>
     *     <b>认证考试</b>
     *      <ol>
     *          <li>系统自动发放的认证考试证书</li>
     *              1) 全部设置：显示：考试名称（专业+子专业+设备型号+等级）<br>
     *              2) 全部未设置的：考试名称<br>
     *              3) 部分设置：考试名称（专业+子专业+设备型号+等级)，设置哪个字段就显示哪个，比如只设置了等级，L2，显示：考试名称（L2）<br>
     *          <li>手工发放：专业+子专业+设备型号+等级</li>
     *      </ol>
     * </li>
     * <li><b>普通考试：只显示考试名称</b></li>
     * </ul>
     * @param examName 考试名称
     * @param professionName 专业名称
     * @param subProfessionName 子专业名称
     * @param equipmentTypeName 设备类型
     * @param levelName 等级名称
     * @param cloudLevelName
     * @param cloudProfessionName
     * @return 拼接之后的证书名称
     */
    public static String getCertificateRecordName(Optional<String> examName, Optional<String> professionName, Optional<String> subProfessionName,
                                                  Optional<String> equipmentTypeName, Optional<String> levelName,
                                                  Optional<String> cloudProfessionName, Optional<String> cloudLevelName) {
        // 专业-子专业-设备型号-等级，存在则拼接
        List<String> nameArray = new ArrayList<>();
        professionName.ifPresent(p -> nameArray.add(p));
        subProfessionName.ifPresent(p -> nameArray.add(p));
        equipmentTypeName.ifPresent(p -> nameArray.add(p));
        levelName.ifPresent(p -> nameArray.add(p));
        cloudProfessionName.ifPresent(p -> nameArray.add(p));
        cloudLevelName.ifPresent(p -> nameArray.add(p));
        String names = nameArray.stream().collect(Collectors.joining("-"));
        // 考试名称
        return examName.map(e -> {
            StringBuffer buffer = new StringBuffer();
            buffer.append(e);
            if (nameArray.size() > 0) {
                buffer.append("(").append(names).append(")");
            }
            return buffer.toString();
        }).orElse(names);
    }



}
