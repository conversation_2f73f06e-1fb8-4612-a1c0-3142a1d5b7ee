package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.QuestionErrorRateEntity;

public class QuestionErrorRate extends QuestionErrorRateEntity {

    /**
     *
     */
    private static final long serialVersionUID = 5347280478466897996L;

    /**
     * 答题总数量，初始值：1
     */
    public static final Integer INIT_ALL_COUNT = 1;

    /**
     * 答题错误数量，初始值：1
     */
    public static final Integer INIT_WRONG_COUNT_1 = 1;

    /**
     * 答题错误数量，初始值：0
     */
    public static final Integer INIT_WRONG_COUNT_0 = 0;

    public Integer errorRate;

    public Integer rightCount;


    public Integer getRightCount() {
        return rightCount;
    }

    public void setRightCount(Integer rightCount) {
        this.rightCount = rightCount;
    }

    public Integer getErrorRate() {
        return errorRate;
    }

    public void setErrorRate(Integer errorRate) {
        this.errorRate = errorRate;
    }

}
