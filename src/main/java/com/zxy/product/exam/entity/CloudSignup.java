package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.CloudSignupEntity;

public class CloudSignup extends CloudSignupEntity{

    /**
     *
     */
    private static final long serialVersionUID = 6260233707762455036L;

    public static final String CLOUD_URI = "exam/cloud-signup";

    public static final String TEMPLATE_INDEX = "序号";

    private Integer Index;

    private CloudProfession cloudProfession;

    private CloudLevel cloudLevel;

    private Member member;

    private Member auditMember;

    private Integer count;

    private Exam exam;

    private int myCount; // 我已报名的次数
    private int overSignupRule; // 是否超过报名次数限制


    public int getMyCount() {
        return myCount;
    }

    public void setMyCount(int myCount) {
        this.myCount = myCount;
    }

    public int getOverSignupRule() {
        return overSignupRule;
    }

    public void setOverSignupRule(int overSignupRule) {
        this.overSignupRule = overSignupRule;
    }

    public Exam getExam() {
        return exam;
    }

    public void setExam(Exam exam) {
        this.exam = exam;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Member getAuditMember() {
        return auditMember;
    }

    public void setAuditMember(Member auditMember) {
        this.auditMember = auditMember;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public CloudProfession getCloudProfession() {
        return cloudProfession;
    }

    public void setCloudProfession(CloudProfession cloudProfession) {
        this.cloudProfession = cloudProfession;
    }

    public CloudLevel getCloudLevel() {
        return cloudLevel;
    }

    public void setCloudLevel(CloudLevel cloudLevel) {
        this.cloudLevel = cloudLevel;
    }

    public Integer getIndex() {
        return Index;
    }

    public void setIndex(Integer index) {
        Index = index;
    }


}
