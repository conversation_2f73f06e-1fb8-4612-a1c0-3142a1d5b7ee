package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ExamNoticeEntity;

/**
 * <AUTHOR>
 *
 */
public class ExamNotice extends ExamNoticeEntity {

	private static final long serialVersionUID = -7089348227008818478L;

	/** 业务类型：1 考试 */
	public static final Integer BUSINESS_TYPE_EXAM = 1;
	/** 业务类型：2 调研 */
	public static final Integer BUSINESS_TYPE_RESEARCH = 2;

	/** 是否通知: 1 是*/
	public static final Integer NOTICE_YES = 1;
	/** 是否通知: 0 否*/
	public static final Integer NOTICE_NO = 0;

	/** 通知类型：0 发布通知用户 */
    public static final int TYPE_PUBLISH = 0;
    /** 通知类型： 1 撤销业务 */
    public static final int TYPE_CANCEL = 1;
    /** 通知类型：2 催办 */
    public static final int TYPE_URGE = 2;
    /** 通知类型：3 阅卷安排通知 */
    public static final int TYPE_MARKING_ARRANGE = 3;
    /** 通知类型： 4 评卷通知*/
    public static final int TYPE_MARK_PAPER = 4;
    /** 通知类型： 5 报名通知 */
    public static final int TYPE_SIGN_UP= 5;
    /** 通知类型： 6 报名审核通过 */
    public static final int TYPE_SIGN_UP_PASS = 6;
    /** 通知类型： 7 报名审核拒绝 */
    public static final int TYPE_SIGN_UP_REFUSE = 7;
    /** 通知类型： 8 答卷延时 */
    public static final int TYPE_DELAY = 8;
    /** 通知类型： 9 证书发放 */
    public static final int TYPE_CERTIFICATE_ISSUE = 9;
    /** 通知类型： 10 监考老师通知 */
    public static final int TYPE_INVIGILATOR_NOTICE = 10;
    /** 通知类型： 11 考试成绩通知 */
    public static final int TYPE_EXAM_SCORE = 11;

    /**
     * 接收者
     */
    private String receiverIds;

	public String getReceiverIds() {
		return receiverIds;
	}

	public void setReceiverIds(String receiverIds) {
		this.receiverIds = receiverIds;
	}


}
