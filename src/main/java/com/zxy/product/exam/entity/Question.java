package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.QuestionEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Question extends QuestionEntity {

    /**
     *
     */
    private static final long serialVersionUID = -6219737443597721603L;

    public static final String URI = "exam/question-depot";

    public static final Integer SINGLE_CHOOSE = 1;
    public static final Integer MULTIPLE_CHOOSE = 2;
    public static final Integer JDUGEMENT = 3;
    public static final Integer SENTENCE_COMPLETION = 4;
    public static final Integer QUESTION_ANWSER = 5;
    public static final Integer READING_COMPREHENSION = 6;
    public static final Integer LINE = 7;
    public static final Integer SORTING = 8;

    //导入模板 类型 英文字符串
    public static final String IMPORT_SINGLE_CHOOSE = "single";
    public static final String IMPORT_MULTIPLE_CHOOSE = "multiSelect";
    public static final String IMPORT_JDUGEMENT = "judge";
    public static final String IMPORT_SENTENCE_COMPLETION = "fill";
    public static final String IMPORT_QUESTION_ANWSER = "question";
    public static final String IMPORT_SORTING = "paixu";

    public static final Integer PUBLISH = 1;
    public static final Integer IS_NOT_PUBLISH = 0;
    /**
     * 草稿
     */
    public static final Integer ROUGH_COPY = 2;

    public static final Integer SUBJECTIVE_NO = 0; // 不是主观题
    public static final Integer SUBJECTIVE_YES = 1; // 主观题

    public static final Integer PERCENT = 100;
    public static final Integer ZERO_PERCENT = 0;
    public static final Integer TEN_THOUSAND = 10000;
    public static final Integer ONE_HUNDRED = 100;

    public static final String DIFFICULTY_LOW_STR = "low";
    public static final String DIFFICULTY_MIDDLE_STR = "middle";
    public static final String DIFFICULTY_HIGH_STR = "high";
    public static final String DIFFICULTY_LOW_CN = "低";
    public static final String DIFFICULTY_MIDDLE_CN  = "中";
    public static final String DIFFICULTY_HIGH_CN  = "高";

    public static final Integer DIFFICULTY_LOW = 3;
    public static final Integer DIFFICULTY_MIDDLE = 2;
    public static final Integer DIFFICULTY_HIGH = 1;

    public static final String RIGHT_STR = "right";
    public static final String WRONG_STR = "wrong";
    public static final String RIGHT_STR_CN = "正确";
    public static final String WRONG_STR_CN = "错误";
    public static final Integer RIGHT = 1;
    public static final Integer WRONG = 0;

    public static final String TEMPLATE_INDEX = "序号";
    public static final String TEMPLATE_QUESTION_TYPE = "试题类型"+"\r\n"
            + "(必填)";
    public static final String TEMPLATE_QUESTION_CONTENT = "试题题干(必填)";
    public static final String TEMPLATE_QUESTION_OPTION = "选项"+"\r\n"
            + "（用英文'|'隔开）";
    public static final String TEMPLATE_QUESTION_ANSWER = "答案(必填)"+"\r\n"
            + "（填空题各空的答案用英文'|'隔开，一个空的多个选项用'#'隔开";

    public static final String TEMPLATE_QUESTION_SCORE = "分数（必填）";
    public static final String TEMPLATE_QUESTION_DIFFCULTY = "难易度 (必填)";
    public static final String TEMPLATE_QUESTION_PARSING = "试题解析";
    public static final String TEMPLATE_QUESTION_DEPOT = "归属目录编码"+"\r\n"
            + "（必填）";
    public static final String TEMPLATE_QUESTION_ORGANIZATION = "归属部门编码"+"\r\n"
            + "（必填）";
    public static final String TEMPLATE_QUESTION_REMARK = "(备注)---试题类型编码(名称)：single(单选),multiSelect(多选),judge(判断),fill(填空题),"
												    		+ "question(问答题),paixu(排序)难易度编码(名称)：low(低),middle(中),high(高),"
												    		+ " 判断题答案：right（正确）wrong(错误)";



    public static final String TITLE_QUESTION_CONTENT_DEC = "试题内容";
    public static final String TITLE_QUESTION_ORGANIZTION_DEC = "归属部门";
    public static final String TITLE_QUESTION_DEPOT_DEC = "归属目录";
    public static final String TITLE_QUESTION_TYPE_DEC = "试题类型";
    public static final String TITLE_QUESTION_ERRORRATE_DEC = "易错率";
    public static final String TITLE_QUESTION_RECOVERY_DEC = "纠错";
    public static final String TITLE_QUESTION_DIFFCULTY_DEC = "难易度";


    public static final String EXPORT_TEMPLATE_INDEX = "序号";
    public static final String EXPORT_TEMPLATE_QUESTION_CREATE_TIME = "创建日期";
    public static final String EXPORT_TEMPLATE_QUESTION_TYPE = "试题类型";
    public static final String EXPORT_TEMPLATE_QUESTION_CONTENT = "试题题干";
    public static final String EXPORT_TEMPLATE_QUESTION_OPTION = "选项（用'|'隔开）";
    public static final String EXPORT_TEMPLATE_QUESTION_ANSWER = "答案"+"\r\n"
            + "（填空题各空的答案用英文'|'隔开，一个空的多个选项用'#'隔开";
    public static final String EXPORT_TEMPLATE_QUESTION_DIFFCULTY = "难易度";
//    public static final String EXPORT_TEMPLATE_QUESTION_SCORE = "分数";
    public static final String EXPORT_TEMPLATE_QUESTION_USE_TIMES = "使用次数";
    public static final String EXPORT_TEMPLATE_QUESTION_RIGHT_TIMES = "答对次数";
    public static final String EXPORT_TEMPLATE_QUESTION_WRONG_TIMES = "错误次数";
    public static final String EXPORT_TEMPLATE_QUESTION_DEPOT = "归属目录";
    public static final String EXPORT_TEMPLATE_QUESTION_ORGANIZATION = "归属部门";



	/**
	 * 导出模板
	 */
	public static final Integer EXPORT_TYPE_TEMPLATE = 1;
	/**
	 * 导出数据
	 */
	public static final Integer EXPORT_TYPE_DATA = 2;
	/**
	 * 导出错误数据
	 */
	public static final Integer EXPORT_TYPE_ERROR = 3;

	/**
	 * 导入模板下载类型-调研活动
	 */
	public static final Integer IMPORT_TEMPLET_TYPE_ACTIVITY = 1;
	/**
     * 导入模板下载类型-调研问卷
     */
	public static final Integer IMPORT_TEMPLET_TYPE_RESEARCH = 2;
	/**
     * 导入模板下载类型-评估问卷
     */
	public static final Integer IMPORT_TEMPLET_TYPE_EVALUATE = 3;

	public static final String SINGLE_DEC = "单选";
	public static final String MUTIPLE_DEC = "多选";
	public static final String JUDEGE_DEC = "判断";
	public static final String SENTENCE_COMPETITION_DEC = "填空";
	public static final String QUESTION_ANSWER_DEC = "问答";
	public static final String READING_COMPEHENSION_DEC = "阅读理解";
	public static final String SORTING_DEC = "排序";
	public static final String LINE_DEC = "连线";
	public static final String TEMPLATE_QUESTION_ERRORRATE = "易错率";
	public static final String TEMPLATE_QUESTION_RECOVERAMOUNT = "纠错数目";
	public static final String TEMPLATE_QUESTION_STATUS = "状态";
	public static final String TEMPLATE_QUESTION_CREATETIME = "创建时间";


	public static final String QUESTION_TYPE_DEC = "试题类型";
	public static final String QUESTION_DIFFCULTY_DEC = "难度";
	public static final String QUESTION_DEPOT_DEC = "题库";
	public static final String QUESTION_ORGANIZATION_DEC = "部门";
	public static final String QUESTION_CODE = "部门编码";

	//试题来源类型
	/**
	 * 考试来源
	 */
	public static final Integer EXAM_SOURCE_TYPE = 1;
	/**
	 * 调研活动
	 */
	public static final Integer RESEACH_ACTIVITY_SOURCE_TYPE = 2;
	/**
	 * 调研问卷
	 */
	public static final Integer RESEARCH_QUESTIONARY_SOURCE_TYPE = 3;
	/**
	 * 评估问卷
	 */
	public static final Integer EVALUATE_QUESTIONARY_SOURCE_TYPE = 4;


    private List<QuestionAttr> questionAttrs;
    private QuestionAttr questionAttr;

    private Organization organization;

    private QuestionDepot questionDepot;

    private List<QuestionRecovery> questionRecoverys;

    private List<Question> subs;

    private Dimension dimension;

    private ErrorReason reason;

    private AnswerRecord answerReocrd;

    private Integer result;

    private QuestionErrorRate questionErrorRate;


    public QuestionErrorRate getQuestionErrorRate() {
        return questionErrorRate;
    }

    public void setQuestionErrorRate(QuestionErrorRate questionErrorRate) {
        this.questionErrorRate = questionErrorRate;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public AnswerRecord getAnswerReocrd() {
        return answerReocrd;
    }

    public void setAnswerReocrd(AnswerRecord answerReocrd) {
        this.answerReocrd = answerReocrd;
    }

    /** 考试名称 */
    private String examName;

    /**
     * 调研问卷 维度ID
     */
    private String dimensionId;

    /**
     * 调研问卷 排序
     */
    private Integer order;

    private Integer Index;

    //问卷-试题-有效答题数
    private Integer answerCount;

    private Integer sequence;

    private String skillsElment;

    private String knowledge;

    private PaperClassQuestion paperClassQuestion;

    //难度百分比
    private Double difficultyPercent;
    //区分度百分比
    private Double differentPercent;

    private String questionCopyId;

    private String difficultyPercentStr;
    private String differentPercentStr;

    //试卷的试题是否是选择的，试卷的试题有两种方式，第一种是选择，第二种是临时添加
    private  Integer isFromSelected;


    public QuestionAttr getQuestionAttr() {
        return questionAttr;
    }

    public void setQuestionAttr(QuestionAttr questionAttr) {
        this.questionAttr = questionAttr;
    }

    public Integer getIsFromSelected() {
        return isFromSelected;
    }

    public void setIsFromSelected(Integer isFromSelected) {
        this.isFromSelected = isFromSelected;
    }

    public String getDifficultyPercentStr() {
		return difficultyPercentStr;
	}

	public void setDifficultyPercentStr(String difficultyPercentStr) {
		this.difficultyPercentStr = difficultyPercentStr;
	}

	public String getDifferentPercentStr() {
		return differentPercentStr;
	}

	public void setDifferentPercentStr(String differentPercentStr) {
		this.differentPercentStr = differentPercentStr;
	}

	public String getQuestionCopyId() {
        return questionCopyId;
    }

    public void setQuestionCopyId(String questionCopyId) {
        this.questionCopyId = questionCopyId;
    }

    public String getExamName() {
        return examName;
    }

    public void setExamName(String examName) {
        this.examName = examName;
    }

    public PaperClassQuestion getPaperClassQuestion() {
        return paperClassQuestion;
    }

    public void setPaperClassQuestion(PaperClassQuestion paperClassQuestion) {
        this.paperClassQuestion = paperClassQuestion;
    }

    public Integer getAnswerCount() {
        return answerCount;
    }

    public void setAnswerCount(Integer answerCount) {
        this.answerCount = answerCount;
    }

    public List<QuestionAttr> getQuestionAttrs() {
        return questionAttrs;
    }

    public void setQuestionAttrs(List<QuestionAttr> questionAttrs) {
        this.questionAttrs = questionAttrs;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public QuestionDepot getQuestionDepot(){
        return questionDepot;
    }

    public void setQuestionDepot(QuestionDepot questionDepot){
        this.questionDepot=questionDepot;
    }

    public List<QuestionRecovery> getQuestionRecoverys() {
        return questionRecoverys;
    }

    public void setQuestionRecoverys(List<QuestionRecovery> questionRecoverys) {
        this.questionRecoverys = questionRecoverys;
    }

    public List<Question> getSubs() {
        return subs;
    }

    public void setSubs(List<Question> subs) {
        this.subs = subs;
    }

	public String getDimensionId() {
		return dimensionId;
	}

	public void setDimensionId(String dimensionId) {
		this.dimensionId = dimensionId;
	}

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public Dimension getDimension() {
		return dimension;
	}

	public void setDimension(Dimension dimension) {
		this.dimension = dimension;
	}

	public Integer getIndex() {
		return Index;
	}

	public void setIndex(Integer index) {
		Index = index;
	}


	public static Map<Integer, String> getQuestionTypeNameMap() {
        Map<Integer, String> map = new HashMap<>();
        map.put(SINGLE_CHOOSE, SINGLE_DEC); // 单选
        map.put(MULTIPLE_CHOOSE, MUTIPLE_DEC);// 多选
        map.put(JDUGEMENT, JUDEGE_DEC);// 判断
        map.put(SENTENCE_COMPLETION, SENTENCE_COMPETITION_DEC);// 填空
        map.put(QUESTION_ANWSER, QUESTION_ANSWER_DEC);// 问答
        map.put(READING_COMPREHENSION, READING_COMPEHENSION_DEC);// 阅读理解
        map.put(LINE, LINE_DEC);// 连线
        map.put(SORTING, SORTING_DEC);// 排序
        return map;
    }

	/** 通过试题类型编码得到试题类型枚举值 */
	public static  Integer getQuestionTypeByCode(String typeCode) {
        if (Question.IMPORT_SINGLE_CHOOSE.equals(typeCode)) return Question.SINGLE_CHOOSE;
        if (Question.IMPORT_MULTIPLE_CHOOSE.equals(typeCode)) return Question.MULTIPLE_CHOOSE;
        if (Question.IMPORT_JDUGEMENT.equals(typeCode)) return Question.JDUGEMENT;
        if (Question.IMPORT_SENTENCE_COMPLETION.equals(typeCode)) return Question.SENTENCE_COMPLETION;
        if (Question.IMPORT_QUESTION_ANWSER.equals(typeCode)) return Question.QUESTION_ANWSER;
        if (Question.IMPORT_SORTING.equals(typeCode)) return Question.SORTING;
        return null;
    }

	/** 通过试题类型名称得到试题类型枚举值 */
	public static Integer getQuestionTypeByName(String typeName) {
        if (Question.SINGLE_DEC.equals(typeName)) return Question.SINGLE_CHOOSE;
        if (Question.MUTIPLE_DEC.equals(typeName)) return Question.MULTIPLE_CHOOSE;
        if (Question.JUDEGE_DEC.equals(typeName)) return Question.JDUGEMENT;
        if (Question.SENTENCE_COMPETITION_DEC.equals(typeName)) return Question.SENTENCE_COMPLETION;
        if (Question.QUESTION_ANSWER_DEC.equals(typeName)) return Question.QUESTION_ANWSER;
        if (Question.SORTING_DEC.equals(typeName)) return Question.SORTING;
        return null;
    }


    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getKnowledge() {
        return knowledge;
    }

    public void setKnowledge(String knowledge) {
        this.knowledge = knowledge;
    }

    public String getSkillsElment() {
        return skillsElment;
    }

    public void setSkillsElment(String skillsElment) {
        this.skillsElment = skillsElment;
    }

    public ErrorReason getReason() {
        return reason;
    }

    public void setReason(ErrorReason reason) {
        this.reason = reason;
    }

    public Double getDifficultyPercent() {
        return difficultyPercent;
    }

    public void setDifficultyPercent(Double difficultyPercent) {
        this.difficultyPercent = difficultyPercent;
    }

    public Double getDifferentPercent() {
        return differentPercent;
    }

    public void setDifferentPercent(Double differentPercent) {
        this.differentPercent = differentPercent;
    }
}
