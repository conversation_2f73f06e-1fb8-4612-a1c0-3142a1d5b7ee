package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ExamRecordFaceEntity;

/**
 * <AUTHOR>
 *
 */
public class ExamRecordFace extends ExamRecordFaceEntity {
    private String memberFullName;
    private String memberName;
    private String organizationName;

    private static final long serialVersionUID = 2128774903962672155L;
    public static final String STRING_EXAM_RECORD_FACE = "exam_record_face";
    public static final String STRING_EXAM_RECORD_FACE_2017 = "exam_record_face_2017";
    public static final String STRING_EXAM_RECORD_FACE_2018 = "exam_record_face_2018";
    public static final String STRING_EXAM_RECORD_FACE_2019 = "exam_record_face_2019";
    public static final String STRING_EXAM_RECORD_FACE_2020 = "exam_record_face_2020";
    public static final String STRING_EXAM_RECORD_FACE_2021 = "exam_record_face_2021";
    public static final String STRING_EXAM_RECORD_FACE_2022 = "exam_record_face_2022";
    public static final String STRING_EXAM_RECORD_FACE_2023 = "exam_record_face_2023";
    public static final String STRING_EXAM_RECORD_FACE_2024 = "exam_record_face_2024";
    public static final String STRING_EXAM_RECORD_FACE_2025 = "exam_record_face_2025";
    public static final String STRING_EXAM_RECORD_FACE_2026 = "exam_record_face_2026";
    public static final String STRING_EXAM_RECORD_FACE_2027 = "exam_record_face_2027";
    public static final String STRING_EXAM_RECORD_FACE_2028 = "exam_record_face_2028";
    public static final String STRING_EXAM_RECORD_FACE_2029 = "exam_record_face_2029";
    public static final String STRING_EXAM_RECORD_FACE_2030 = "exam_record_face_2030";
    //人脸监考记录表类型 0：人脸进入 1：人脸监考
    public static final Integer STRING_EXAM_RECORD_FACE_TYPE_ENTER = 0;
    public static final Integer STRING_EXAM_RECORD_FACE_TYPE_PROCTOR = 1;
    //二机位监考记录表类型 2：二机位进入 3：二机位监考
    public static final Integer STRING_EXAM_RECORD_FACE2_TYPE_ENTER = 2;
    public static final Integer STRING_EXAM_RECORD_FACE2_TYPE_PROCTOR = 3;
    //人脸监考记录表状态 0：正常 1：异常 2：标记正常3：标记异常
    public static final Integer STRING_EXAM_RECORD_FACE_STATUS_NORMAL = 0;
    public static final Integer STRING_EXAM_RECORD_FACE_TYPE_ABNORMAL = 1;
    public static final Integer STRING_EXAM_RECORD_FACE_TYPE_TAG_NORMAL = 2;
    public static final Integer STRING_EXAM_RECORD_FACE_TYPE_TAG_ABNORMAL = 3;
    public static final Integer STRING_EXAM_RECORD_FACE_STATUS_FOUR = 4;

    public String getMemberFullName() {
        return memberFullName;
    }

    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
}
