package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.SignupEntity;

/**
 * <AUTHOR>
 *
 */
public class SignUp extends SignupEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -6610134927570590911L;

	public static final String URI = "exam/signup";

	public static final String TIME = "signup_time_";

	public static final String SIGNUP = "signup";

	/**
     * 报名成功数缓存时间
     */
    public static final int CACHE_TIME = 60 * 60 * 2;

	/**
	 * 待审核
	 */
	public static final int STATUS_APPROVE = 1;

	/**
	 * 已通过
	 */
	public static final int STATUS_PASSED = 2;

	/**
	 * 已拒绝
	 */
	public static final int STATUS_REFUSE = 3;

	/**
	 * 取消报名
	 */
	public static final int STATUS_CANCEL = 4;

    public static final String STATUS_APPROVE_TEXT="待审核";
    public static final String STATUS_PASSED_TEXT="已通过";
    public static final String STATUS_REFUSE_TEXT="已拒绝";
    public static final String STATUS_CANCEL_TEXT="取消报名";

	public static final String TEMPLATE_INDEX = "序号";

	private Organization organization;
	private Member member;
	private PaperInstance paperInstance;
	private Exam exam;
	private ExamRecord examRecord;
	private Member auditMember; // 审核人

	private Integer Index;

	private Integer count;

	private SignUpAuth signUpAuth; // 报名认证信息

	private String examLevelId; // 考试级别id
	private Long time; // 重复报名剩余等待时间


    public String getExamLevelId() {
        return examLevelId;
    }

    public void setExamLevelId(String examLevelId) {
        this.examLevelId = examLevelId;
    }

    public SignUpAuth getSignUpAuth() {
        return signUpAuth;
    }

    public void setSignUpAuth(SignUpAuth signUpAuth) {
        this.signUpAuth = signUpAuth;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getIndex() {
        return Index;
    }

    public void setIndex(Integer index) {
        Index = index;
    }

    public Member getAuditMember() {
		return auditMember;
	}

	public void setAuditMember(Member auditMember) {
		this.auditMember = auditMember;
	}

	public Organization getOrganization() {
		return organization;
	}

	public Member getMember() {
		return member;
	}

	public PaperInstance getPaperInstance() {
		return paperInstance;
	}

	public Exam getExam() {
		return exam;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public void setPaperInstance(PaperInstance paperInstance) {
		this.paperInstance = paperInstance;
	}

	public void setExam(Exam exam) {
		this.exam = exam;
	}

	public ExamRecord getExamRecord() {
		return examRecord;
	}

	public void setExamRecord(ExamRecord examRecord) {
		this.examRecord = examRecord;
	}

	public Long getTime() {
		return time;
	}

	public void setTime(Long time) {
		this.time = time;
	}

}
