package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.PaperInstanceEntity;

import java.util.List;

public class PaperInstance extends PaperInstanceEntity {

    /**
	 *
	 */
	private static final long serialVersionUID = 6269149555640008067L;

	private List<String> questionCopyIds;

    private List<QuestionCopy> questions;

    /**
     *  加密后的答案
     */
    private String encryptContent;

	private Integer questionNum;

    private Integer totalScore;

    private List<AnswerRecord> answerRecords;


    public Integer getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public List<QuestionCopy> getQuestions() {
        return questions;
    }

    public void setQuestions(List<QuestionCopy> questions) {
        this.questions = questions;
    }

    public List<String> getQuestionCopyIds() {
        return questionCopyIds;
    }

    public void setQuestionCopyIds(List<String> questionCopyIds) {
        this.questionCopyIds = questionCopyIds;
    }

	public String getEncryptContent() {
		return encryptContent;
	}

	public void setEncryptContent(String encryptContent) {
		this.encryptContent = encryptContent;
	}

	public List<AnswerRecord> getAnswerRecords() {
		return answerRecords;
	}

	public void setAnswerRecords(List<AnswerRecord> answerRecords) {
		this.answerRecords = answerRecords;
	}

}
