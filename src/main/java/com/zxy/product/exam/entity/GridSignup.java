package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.GridSignupEntity;

public class GridSignup extends GridSignupEntity {
    private static final long serialVersionUID = 4041519792446345945L;

    private Member member;

    private Boolean haveChbnCertificate;

    private Member auditMember;
    private Exam exam;
    private Integer count;
    private Integer Index;


    public static final String GRID_URI = "exam/grid-signup";
    public static final String TEMPLATE_INDEX = "序号";

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Boolean getHaveChbnCertificate() {
        return haveChbnCertificate;
    }

    public void setHaveChbnCertificate(Boolean haveChbnCertificate) {
        this.haveChbnCertificate = haveChbnCertificate;
    }

    public Member getAuditMember() {
        return auditMember;
    }

    public void setAuditMember(Member auditMember) {
        this.auditMember = auditMember;
    }

    public Exam getExam() {
        return exam;
    }

    public void setExam(Exam exam) {
        this.exam = exam;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getIndex() {
        return Index;
    }

    public void setIndex(Integer index) {
        Index = index;
    }
}
