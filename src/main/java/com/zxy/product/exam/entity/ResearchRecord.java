package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ResearchRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ResearchRecord extends ResearchRecordEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 3646922758314108194L;


	/**
	 * 已完成
	 */
	public static final Integer STATUS_FINISHED = 1;


	/**
	 * 未参与
	 */
	public static final Integer STATUS_NO_JOIN = 0;

	/**
	 * 个人中心查询状态
	 *
	 */
	/**
	 * 待参加（已开始，还没有参加）
	 */
	public static final String PERSON_CENTER_STATUS_WAIT_JOIN = "wait-join";
	/**
	 * 待开始（活动时间还没开始）
	 */
	public static final String PERSON_CENTER_STATUS_WAIT_START = "wait-start";
	/**
	 * 已完成
	 */
	public static final String PERSON_CENTER_STATUS_FINISHED = "finished";

	/** 答题终端 0：PC，1：移动 ，2：微信 */
	public static final Integer CLIENT_TYPE_PC = 0;
	public static final Integer CLIENT_TYPE_APP = 1;
	public static final Integer CLIENT_TYPE_WECHAT = 2;

	/** 是否查看详情 0否 1是 */
	public static final Integer IS_VIEW_DETAIL_NO = 0;
	public static final Integer IS_VIEW_DETAIL_YES = 1;

	/** 是否已计算统计结果，0否 1是 */
    public static final Integer IS_SUMMARY_NO = 0;
    public static final Integer IS_SUMMARY_YES = 1;

	private Member member;

	private ResearchQuestionary researchQuestionary;

	private List<ResearchAnswerRecord> researchAnswerRecords;


	public Member getMember() {
		return member;
	}


	public void setMember(Member member) {
		this.member = member;
	}


	public ResearchQuestionary getResearchQuestionary() {
		return researchQuestionary;
	}


	public void setResearchQuestionary(ResearchQuestionary researchQuestionary) {
		this.researchQuestionary = researchQuestionary;
	}

	public List<ResearchAnswerRecord> getResearchAnswerRecords() {
		return researchAnswerRecords;
	}

	public void setResearchAnswerRecords(List<ResearchAnswerRecord> researchAnswerRecords) {
		this.researchAnswerRecords = researchAnswerRecords;
	}
}
