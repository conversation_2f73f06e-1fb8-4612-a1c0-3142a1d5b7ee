package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ProfessionEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Profession extends ProfessionEntity{

    private static final long serialVersionUID = -7115677658827629444L;

    private List<ProfessionLevel> professionLevels;

    /**
     * 专业-监控 pp12
     */
    public static final String PROFESSION_JK = "pp12";
    /**
     * 子专业-大核心监控（专业-监控）ps0112
     */
    public static final String SUB_PROFESSION_DHXJK = "ps0112";
    /**
     * 子专业-大无线监控（专业-监控）ps0212
     */
    public static final String SUB_PROFESSION_DWXJK = "ps0212";
    /**
     * 子专业-投诉（专业-监控）ps0312
     */
    public static final String SUB_PROFESSION_TS = "ps0312";
    /**
     * 子专业-监控（专业-监控）ps0412
     */
    public static final String SUB_PROFESSION_JK = "ps0412";


    /**
     * 专业-数据 pp14
     */
    public static final String PROFESSION_SJ = "pp14";
    /**
     * 子专业-IP（专业-数据）ps0114
     */
    public static final String SUB_PROFESSION_IP = "ps0114";
    /**
     * 子专业-IT数据库（专业-数据）ps0414
     */
    public static final String SUB_PROFESSION_IT_SJK = "ps0414";
    /**
     * 子专业-ITX86及虚拟化（专业-数据）ps0214
     */
    public static final String SUB_PROFESSION_IT_X86 = "ps0214";
    /**
     * 子专业-互联网内容管理（专业-数据）ps0614
     */
    public static final String SUB_PROFESSION_HLWNRGL = "ps0614";


    /**
     * 专业-IP pp03
     */
    public static final String PROFESSION_IP = "pp03";
    /**
     * 子专业-CMNET（专业-IP）ps0203
     */
    public static final String SUB_PROFESSION_CMNET = "ps0203";
    /**
     * 子专业-IP承载网（专业-IP）ps0303
     */
    public static final String SUB_PROFESSION_IP_CZW = "ps0303";


    /**
     * 专业-IT pp04
     */
    public static final String PROFESSION_IT = "pp04";
    /**
     * 子专业-IT数据库（专业-IT）ps0204
     */
    public static final String SUB_PROFESSION_IT_SJK_2 = "ps0204";
    /**
     * 子专业-X86及虚拟化（专业-IT）ps0404
     */
    public static final String SUB_PROFESSION_X86 = "ps0404";
    /**
     * 子专业-IT开发（专业-IT）ps0504
     */
    public static final String SUB_PROFESSION_IT = "ps0504";
    /**
     * 子专业-IT开发前端（专业-IT）ps0604
     */
    public static final String SUB_PROFESSION_IT_QD = "ps0604";
    /**
     * 子专业-IT开发后端（专业-IT）ps0704
     */
    public static final String SUB_PROFESSION_IT_HD = "ps0704";
    /**
     * 子专业-网络云（专业-IT）ps0804
     */
    public static final String SUB_PROFESSION_IT_WLY = "ps0804";

    /**
     * 子专业-数据挖掘分析（专业-IT）
     */
    public static final String SUB_PROFESSION_IT_SJWJFX = "ps1004";


    /**
     * 专业-安全 pp09
     */
    public static final String PROFESSION_AQ = "pp09";
    /**
     * 子专业-安全基础（专业-安全）ps0309
     */
    public static final String SUB_PROFESSION_AQJC = "ps0309";
    /**
     * 子专业-安全防护（专业-安全）ps0209
     */
    public static final String SUB_PROFESSION_AQFH = "ps0209";
    /**
     * 子专业-评估渗透（专业-安全）ps0109
     */
    public static final String SUB_PROFESSION_PGST = "ps0109";
    /**
     * 子专业-安全（专业-安全）ps0409
     */
    public static final String SUB_PROFESSION_AQ = "ps0409";


    /**
     * 专业-互联网 pp07
     */
    public static final String PROFESSION_HLW = "pp07";
    /**
     * 子专业-内容管理（专业-互联网）ps0107
     */
    public static final String SUB_PROFESSION_HLWNRGL_2 = "ps0107";


    /**
     * 专业-传输 pp05
     */
    public static final String PROFESSION_CS = "pp05";
    /**
     * 子专业-设备通用（专业-传输）ps0305
     */
    public static final String SUB_PROFESSION_SBTY = "ps0305";
    /**
     * 子专业-设备PTN（专业-传输）ps0505
     */
    public static final String SUB_PROFESSION_PTN = "ps0505";
    /**
     * 子专业-设备OTN（专业-传输）ps0405
     */
    public static final String SUB_PROFESSION_OTN = "ps0405";
    /**
     * 子专业-设备PON（专业-传输）ps0605
     */
    public static final String SUB_PROFESSION_PON = "ps0605";
    /**
     * 子专业-设备SPN（专业-传输）ps0705
     */
    public static final String SUB_PROFESSION_SPN = "ps0705";


    /**
     * 专业-集客家客 pp10
     */
    public static final String PROFESSION_JKJK = "pp10";
    /**
     * 子专业-集客（专业-集客家客）ps0210
     */
    public static final String SUB_PROFESSION_JI_KE = "ps0210";
    /**
     * 子专业-家客（专业-集客家客）ps0310
     */
    public static final String SUB_PROFESSION_JIA_KE= "ps0310";


    /**
     * 专业-家客业务支撑 pp16
     */
    public static final String PROFESSION_JKYWZC = "pp16";
    /**
     * 子专业-家客（专业-家客业务支撑）ps0116
     */
    public static final String SUB_PROFESSION_JIA_KE_2= "ps0116";


    /**
     * 专业-集客业务支撑 pp17
     */
    public static final String PROFESSION_JKYWZC_2 = "pp17";
    /**
     * 子专业-集客专线（专业-集客业务支撑）ps0117
     */
    public static final String SUB_PROFESSION_JKZX= "ps0117";


    /**
     * 专业-动环 pp06
     */
    public static final String PROFESSION_DH = "pp06";
    /**
     * 子专业-动环通用（专业-动环）ps0106
     */
    public static final String SUB_PROFESSION_DHTY= "ps0106";
    /**
     * 子专业-高低压配电系统（专业-动环）ps0206
     */
    public static final String SUB_PROFESSION_DHGDY= "ps0206";
    /**
     * 子专业-交直流供电系统（专业-动环）ps0306
     */
    public static final String SUB_PROFESSION_DHJZL= "ps0306";
    /**
     * 子专业-发电系统（专业-动环）ps0406
     */
    public static final String SUB_PROFESSION_DHFD= "ps0406";
    /**
     * 子专业-温控系统（专业-动环）ps0506
     */
    public static final String SUB_PROFESSION_DHWK= "ps0506";
    /**
     * 子专业-动环监控系统（专业-动环）ps0606
     */
    public static final String SUB_PROFESSION_DHJK= "ps0606";
    /**
     * 子专业-电源系统（专业-动环）ps0706
     */
    public static final String SUB_PROFESSION_DHDY= "ps0706";


    /**
     * 专业-无线 pp01
     */
    public static final String PROFESSION_WX = "pp01";
    /**
     * 子专业-GSM网优（专业-无线）ps0101
     */
    public static final String SUB_PROFESSION_GSM= "ps0101";
    /**
     * 子专业-LTE网优（专业-无线）ps0201
     */
    public static final String SUB_PROFESSION_LTE= "ps0201";


    /**
     * 专业-第三方无线 pp18
     */
    public static final String PROFESSION_DSFWX = "pp18";
    /**
     * 子专业-无线优化（专业-第三方无线）ps0118
     */
    public static final String SUB_PROFESSION_WXYH= "ps0118";


   /**
     * 专业-云计算 pp19
     */
    public static final String PROFESSION_YJS = "pp19";
    /**
     * 子专业-网络云（专业-云计算）ps0119
     */
    public static final String SUB_PROFESSION_YJS_WLY= "ps0119";

    /**
     * 专业-业务质量管理 pp13
     */
    public static final String PROFESSION_YWZLGL = "pp13";

    /**
     * 子专业-数据挖掘分析（专业-业务质量管理）ps0213
     */
    public static final String SUB_PROFESSION_YWZLGL_SJWJFX= "ps0213";


    /**
     * 专业-核心网 pp02
     */
    public static final String PROFESSION_HXW = "pp02";

    /**
     * 子专业-PS域（专业-核心网）ps0202
     */
    public static final String SUB_PROFESSION_HXW_PSY= "ps0202";

    /**
     * 子专业-5GC（专业-核心网）ps0602
     */
    public static final String SUB_PROFESSION_HXW_5GC= "ps0602";

    /**
     * 子专业-4/5G融合（专业-核心网） ps0702
     */
    public static final String SUB_PROFESSION_HXW_4G5GRH= "ps0702";



    /**
     * 是否已删除，1： 是
     */
    public static final Integer DELETE_YES = 1;
    /**
     * 是否已删除，0:否
     */
    public static final Integer DELETE_NO = 0;

    /**
     * 是否查询历史专业，1： 是
     */
    public static final Integer SELECT_YES = 1;
    /**
     * 是否查询历史专业，0:否
     */
    public static final Integer SELECT_NO = 0;

    // 子专业
    private List<Profession> subProfessions;

    // 子专业，name作为key
    private Map<String, Profession> subProfessionsMap = new HashMap<>();

    public List<ProfessionLevel> getProfessionLevels() {
        return professionLevels;
    }

    public void setProfessionLevels(List<ProfessionLevel> professionLevels) {
        this.professionLevels = professionLevels;
    }

    public List<Profession> getSubProfessions() {
        return subProfessions;
    }

    public void setSubProfessions(List<Profession> subProfessions) {
        this.subProfessions = subProfessions;
    }

    public Map<String, Profession> getSubProfessionsMap() {
        return subProfessionsMap;
    }

    public void setSubProfessionsMap(Map<String, Profession> subProfessionsMap) {
        this.subProfessionsMap = subProfessionsMap;
    }
}
