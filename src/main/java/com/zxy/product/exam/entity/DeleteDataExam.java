package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.DeleteDataExamEntity;

public class DeleteDataExam extends DeleteDataExamEntity {

    private static final String DATABASE_NAME = "exam";

    public static final String EXAM = "t_exam";

    public static final String EXAM_REGIST = "t_exam_regist";

    public static final String RESEARCH_QUESTIONARY = "t_research_questionary";

    public static final String CERTIFICATE_RECORD = "t_certificate_record";
    public static final String PAPER_INSTANCE = "t_paper_instance";
    public static final String PAPER_INSTANCE_QUESTION_COPY = "t_paper_instance_question_copy";

    public static DeleteDataExam getDeleteData(String tableName, String businessId, String companyId){
        DeleteDataExam deleteData = new DeleteDataExam();
        deleteData.setDatabaseName(DATABASE_NAME);
        deleteData.setBusinessId(businessId);
        deleteData.setTableName(tableName);
        deleteData.setBusinessId(businessId);
        deleteData.setCompanyId(companyId);
        deleteData.forInsert();
        return deleteData;
    }

}
