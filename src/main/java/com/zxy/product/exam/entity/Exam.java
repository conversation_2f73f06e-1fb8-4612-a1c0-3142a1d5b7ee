package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ExamEntity;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class Exam extends ExamEntity {
	/**
	 *
	 */
	private static final long serialVersionUID = -228554312247496935L;

	public static final String TEMPLATE_INDEX = "序号";

	public static final long CREATE_TIME_2019 = 1546272000000l;
	public static final long CREATE_TIME_2020 = 1577808000000l;
	public static final long CREATE_TIME_2021 = 1609430400000l;
	public static final long CREATE_TIME_2022 = 1640966400000l;
	public static final long CREATE_TIME_2023 = 1672502400000l;
	public static final long CREATE_TIME_2024 = 1704038400000l;
	public static final long CREATE_TIME_2025 = 1735660800000l;
	public static final long CREATE_TIME_2026 = 1767196800000l;
	public static final long CREATE_TIME_2027 = 1798732800000l;
	public static final long CREATE_TIME_2028 = 1830268800000l;
	public static final long CREATE_TIME_2029 = 1861891200000l;
	public static final long CREATE_TIME_2030 = 1893427200000l;
	public static final long CREATE_TIME_2031 = 1924963200000l;

	public static final long TIME_2017 = 1483200000000l;
	public static final long TIME_2018 = 1514736000000l;
	public static final long TIME_2019 = 1546272000000l;
	public static final long TIME_2020 = 1577808000000l;
	public static final long TIME_2021 = 1609430400000l;
	public static final long TIME_2022 = 1640966400000l;
	public static final long TIME_2023 = 1672502400000l;
	public static final long TIME_2024 = 1704038400000l;
	public static final long TIME_2025 = 1735660800000l;
	public static final long TIME_2026 = 1767196800000l;
	public static final long TIME_2027 = 1798732800000l;
	public static final long TIME_2028 = 1830268800000l;
	public static final long TIME_2029 = 1861891200000l;
	public static final long TIME_2030 = 1893427200000l;
	public static final long TIME_2031 = 1924963200000l;

    public static final String CLOUD_URI = "exam/cloud-exam";
    public static final String GRID_URI = "exam/grid-exam";

    public static final String GRID = "grid";

	public static final Integer NORTH = 1; // 北区
	public static final Integer SOURCE = 2; // 南区

    /**
     * CHBN知识赋能行动
     */
    public static final Integer ACTIVITY_TYPE_CHBN = 1;

	/**
	 * 普通考试证书（横版）
	 */
	public static final Integer TYPE_EXAM_TRANSVERSE = 16;

	/**
     * 证书是否自动发放；1:是
     */
    public static final Integer ISSUE_FLAG_YES = 1;
    /**
     * 证书是否自动发放；0:否
     */
    public static final Integer ISSUE_FLAG_NO = 0;

	/**
     * 是否启用预审核；1:是
     */
    public static final Integer PRE_APPROVAL_YES = 1;
    /**
     * 是否启用预审核；0:否
     */
    public static final Integer PRE_APPROVAL_NO = 0;

    /**
     * 预审规则,1选中
     */
    public static final Integer PRE_APPROVAL_RULE_YES = 1;
    /**
     * 预审规则,0未选中
     */
    public static final Integer PRE_APPROVAL_RULE_NO = 0;

    /**
     * 答案显示详情,4 不显示详情按钮
     */
    public static final Integer SHOW_ANSWER_RULE_4 = 4;
    /**
     * 答案显示详情,5 全部不显示
     */
    public static final Integer SHOW_ANSWER_RULE_5 = 5;


	/**
     * 准考证是否配置，1：配置
     */
    public static final Integer ADMISSION_TICKET_YES = 1;
    /**
     * 准考证是否配置，0：不配置
     */
    public static final Integer ADMISSION_TICKET_NO = 0;

	/**
	 * 正式考试
	 */
	public static final Integer EXAM_OFFICIAL_TYPE = 1;
	/**
	 * 非正式考试
	 */
	public static final Integer EXAM_UN_OFFICIAL_TYPE = 2;
	/**
     * 集团认证考试
     */
    public static final Integer EXAM_AUTHENTICATION_TYPE = 3;
    /**
    * 省级认证考试
    */
    public static final Integer EXAM_AUTHENTICATION_PROVINCE_TYPE = 4;
    /**
    * 移动云考试
    */
    public static final Integer EXAM_CLOUD_TYPE = 5;

    /**
    * 网格长认证考试
    */
    public static final Integer EXAM_GRID_TYPE = 6;

    /**
     * 集团，省级认证考试
     */
    public static final Integer[] EXAM_AUTH_TYPE = {3, 4};

    /**
     * 集团，省级认证，移动云考试
     */
    public static final Integer[] EXAM_AUTH_CLOUD_TYPE = {3, 4, 5};

    /**
     * 集团，省级认证，移动云，网格长
     */
    public static final Integer[] EXAM_AUTH_GRID_TYPE = {3, 4, 5, 6};

    /**
     * app所有考试
     */
    public static final Integer[] EXAM_ALL_TYPE = {1, 2, 3, 4, 5, 6};

	/**
	 * 普通考试
	 */
	public static final Integer[] EXAM_TYPE_1_2 = {1, 2};


	/**
	 * 考试状态为报名中、未开始
	 */
	public static final Integer[] EXAM_STATUS_3_4 = {3, 4};

	/**
	 * 是否为强基计划考试-是
	 */
	public static final Integer STRONG_BASE_FLAG_1 = 1;

	/**
	 * 是否为强基计划考试-否
	 */
	public static final Integer STRONG_BASE_FLAG_0 = 0;


	/**
	 * 显示答案规则: 标准答案，自己答案，得分同时显示
	 */
	public static final int SHOW_ANSWER_ALL = 1;
	/**
	 * 显示答案规则: 只显示自己的答案和得分
	 */
	public static final int SHOW_ANSWER_SELF_AND_SCORE = 2;
	/**
	 * 显示答案规则: 只显示得分
	 */
	public static final int SHOW_ANSWER_ONLY_SCORE = 3;
	/**
     * 显示答案规则: 全部不显示
     */
    public static final Integer NO_SHOW_ANYTHING = 5;
	/**
	 * 获取学分规则: 通过后获取全部学分
	 */
	public static final int GET_CREDIT_ALL = 1;
	/**
	 * 获取学分规则: 通过后按照百分比获取学分
	 */
	public static final int GET_CREDIT_PERCENT = 2;
	/**
	 *
	 */
	public static final Integer EXAM_NO = 0;
	/**
	 *
	 */
	public static final Integer EXAM_YES = 1;

	/**
	 * 是否需要报名，0否
	 */
	public static final Integer EXAM_NEED_APPLICANT_NO = 0;
	/**
	 * 是否需要报名，1是
	 */
	public static final Integer EXAM_NEED_APPLICANT_YES = 1;

	/**
     * 1.网络部,2.市场部,3.其它为0
     */
	 public static final Integer NETWORKDEPART = 1;
     public static final Integer MARKETDEPART = 2;
     public static final Integer OTHERDEPART= 0;

	/**
	 * 1: 未发布
	 */
	public static final Integer STATUS_NOT_PUBLISH = 1;
	/**
	 * 2: 发布中
	 */
	public static final Integer STATUS_PUBLISHING = 2;
	/**
	 * 3： 未开始
	 */
	public static final Integer STATUS_NOT_START = 3;
	/**
	 * 4: 报名中
	 */
	public static final Integer STATUS_SIGNUPING = 4;
	/**
	 * 5: 开考中
	 */
	public static final Integer STATUS_STARTING = 5;
	/**
	 * 6: 已结束
	 */
	public static final Integer STATUS_END = 6;
	/**
	 * 7: 信息更新中
	 */
	public static final Integer STATUS_UPDATING = 7;
	/**
	 *	报名需要审核
	 */
	public static final int APPLICANT_NEED_AUDIT = 1;
	/**
	 * 考试试卷缓存时间
	 */
	public static final int CACHE_TIME = 60 * 60 * 24 * 2;
	/**
	 * 缓存1天时间
	 */
	public static final int CACHE_TIME_ONE_DAY = 60 * 60 * 24 * 1;
	/**
	 * 答案钥匙缓存时间
	 */
	public static final int ANSWER_KEY_CACHE_TIME = 60 * 60 * 24 * 3;
	/**
	 * 无限时
	 */
	public static final int NO_LIMIT_TIME = 0;
	/**
	 * 默认考试次数
	 */
	public static final Integer REMOTE_ALLOW_EXAM_TIMES = 1;
	/**
	 * 通知阅卷老师 当前考试需要阅卷
	 */
	public static final String TEMPLATE_CODE_MARK_PAPER = "doc_audit_pass";
	/**
	 * 通知阅卷老师  待办需要阅卷
	 */
	public static final String TEMPLATE_CODE_TO_DO_MARK_PAPER = "doc_audit_pass";
	/**
	 * 试卷答案 缓存 key
	 */
	public static final String PAPER_ANSWER_CACHE_KEY = "exam/paper/paperAnwerCacheKey";
	/**
	 * 试卷答案加密钥匙 key的  放缓存的key
	 */
	public static final String ENCRYPT_ANSWER_KEY_CACHE_KEY = "exam/paper/encryptAnserKeyCacheKey";
	/**
	 * 权限URI
	 */
	public static final String URI = "exam/exam";

	/**
	 * 1考试活动
	 */
	public static final Integer EXAM_ACTIVITY_SOURCE_TYPE = 1;
	/**
	 * 2：课程
	 */
	public static final Integer EXAM_COURSE_SOURCE_TYPE = 2;
	/**
	 * 3专题
	 */
	public static final Integer EXAM_SUBJECT_SOURCE_TYPE = 3;
	/**
	 * 4班级
	 */
	public static final Integer EXAM_CLASS_SOURCE_TYPE = 4;
	/**
	 * 5直播
	 */
	public static final Integer EXAM_LIVE_SOURCE_TYPE = 5;
	/**
	 * 6职位
	 */
	public static final Integer EXAM_HUMAN_POSITION_SOURCE_TYPE = 6;
	/**
	 * 序号
	 */
	private Integer Index;
	/**
	 * 个人中心  考试查询状态
	 * 1: 考试中
	 * 2： 未开始
	 * 3： 审核中
	 * 4： 已完成
	 */
	public static final Integer WAIT_EXAM_PERSON_CENTER_STATUS = 1;
	public static final Integer WAIT_START_PERSON_CENTER_STATUS = 2;
	public static final Integer WAIT_APPROVE_PERSON_CENTER_STATUS = 3;
	public static final Integer FINISHED_PERSON_CENTER_STATUS = 4;

	/**
	 * 及格就通过考试
	 */
	public static final Integer IS_OVER_BY_PASS_EXAM = 1;

	/**
	 * 推送个人中心
	 */
	public static final Integer IS_PUSH_CENTER = 1;

	/**
     * 适用终端："1,2" pc,app "1":pc "2":app
     */
//    public static final String CLIENT_ALL = "1,2";
    public static final String CLIENT_ALL = "0";
    public static final String CLIENT_PC = "1";
    public static final String CLIENT_APP = "2";

    public static final String PUBLISH_NOTICE = "exam-public-notice";
	//启用新考试流程
	public static final Integer ENABLE_NEW_PROCESS= 1;


    /**
     * 开启人脸监考
     */
    public static final Integer FACE_MONITOR_YES = 1;

    /**
     * 未开启人脸监考
     */
    public static final Integer FACE_MONITOR_NO = 0;

    /**
     * 开启人脸检测进入考试
     */
    public static final Integer FACE_ENTER_YES = 1;

    /**
     * 未开启人脸检测进入考试
     */
    public static final Integer FACE_ENTER_NO = 0;
	/**
	 * 开启第二机位监考
	 */
	public static final Integer FACE_SECOND_MONITOR_YES = 1;

	/**
	 * 未开启第二机位监考
	 */
	public static final Integer FACE_SECOND_MONITOR_NO = 0;

	/**
	 * 开启第二机位检测进入考试
	 */
	public static final Integer FACE_SECOND_ENTER_YES = 1;

	/**
	 * 未开启第二机位检测进入考试
	 */
	public static final Integer FACE_SECOND_ENTER_NO = 0;
    /**
     * 未开启不定向选择
     */
    public static final Integer INDEFINITE_NO = 0;

    /**
     * 开启不定向选择
     */
    public static final Integer INDEFINITE_YES = 1;

	// 发布部门
	private Organization publishOrganization;

	// 所属部门
	private Organization ownerOrganization;

	// 发布人
	private Member publisher;

	// 关联的试卷
	private PaperClass paperClass;

	private Organization organization;

	private List<MarkConfig> markConfigs;

	private List<AudienceItem> audienceItems;

	private SignUp signUp;

	private PaperInstance paper;

	private ExamRecord examRecord;

	private List<BusinessTopic> topic;

	private String organizationName; // 归属部门名称

    private String publicClient;//自适应终端

	/**
	 * 考试组报名次数
	 */
	private Integer examRegistrationFrequency;


    /**
	 * 当前考生剩下次数
	 */
	private Integer remainTimes;

	/**
	 * 已经考了的次数
	 */
	private Integer examedTimes;

	/**
	 * 交卷人数
	 */
	private Integer submitPaperNumber;

	/**
	 * 合格人数
	 */
	private Integer passNumber;

	/**
	 * 发布消息
	 */
	private List<ExamNotice> examNotices;

	/**
	 * 创建者

	 */
	private String creator;
	/**
     * 人数
     */
    private Integer count;

    private List<Long> time;

    private Profession profession; // 专业
    private Profession subProfession; // 子专业
    private ProfessionLevel level; // 等级
    private EquipmentType equipmentType;// 设备

    private PersonalTemplate personalTemplate; //个人信息模板

    private Member member; //基本信息

    private List<Member> invigilatorList; //监考老师

    private Organization company; //公司

    private CertificateRecord certificateRecord; //证书发放记录

    private ExamRegist examRegist;

    private SignUpAuth signUpAuth;

    private Question question;

    private QuestionDepot questionDepot;

    private String questionDepotName;

    private Integer sumScore;

    private Integer unSumScore;

    private Integer difficulty;

    private Double ph;

    private Double distinction; // 区分度

    private Boolean belongAuditTime; // 辅助字段：当前是否在报名审核时间范围内，true代表是

    private int failNum; // 不及格人数

    private String adoptPercent; // 通过率

    /** 1,有考试记录 */
    public static final Integer EXAM_TIMES_YES = 1;

    /** 0,没有考试记录 */
    public static final Integer EXAM_TIMES_NO = 0;


    private Integer haveExamTimes; // 是否有考试记录

    private Long examEndTime; // 考试结束时间


    /** 1:每次试卷相同 */
    public static final Integer RANDOM_TYPE_ONE = 1;

    /** 2：每次试卷随机 */
    public static final Integer RANDOM_TYPE_TWO = 2;


    private CloudProfession cloudProfession;

    private CloudLevel cloudLevel;

    private String num;

    private CloudSignup cloudSignup;

    private GridSignup gridSignup;

    private List<GridCourse> gridCourseList;

	private List<RelevanceCourseExam> examCourseList;

    private GridLevel gridLevel;

    /**
     * 网格长考试，考试是否获得chbn证书
     */
    private Boolean haveChbnCertificate;
    /**
     * 网格长考试，考试是否获得完成所有学习
     */
    private Boolean finishAllCourse;

    //是否进行了第一次人脸检测
    private Boolean finishEnterFace;

    private Integer faceStatus;

	/**
	 * 是否推送学习计划 1是，0否
	 */
	private Integer pushLearningPlan;

	/**
	 * 提醒时间
	 */
	private Long remindTime;

	/**
	 * 提醒类型  1:短信  2:站内信  3:邮件 4:oa 逗号分隔
	 */
	private String remindType;


	private Integer supplement;//是否为补充考试

	/**
	 * 空间资源状态
	 */
	private Integer examVirtualSpacesStatus;

	/**
	 * 考试组中的考试是否允许继续考试
	 */
	private Boolean examAgain;

	private String paperPath;

	public Integer getExamRegistrationFrequency() {
		return examRegistrationFrequency;
	}

	public void setExamRegistrationFrequency(Integer examRegistrationFrequency) {
		this.examRegistrationFrequency = examRegistrationFrequency;
	}
	private QuestionCopy questionCopy;

	private AnswerRecord answerRecord;

	private QuestionAttrCopy questionAttrCopy;

	private PaperInstanceQuestionCopy paperInstanceQuestionCopy;



	public Integer getExamVirtualSpacesStatus() {
		return examVirtualSpacesStatus;
	}

	public void setExamVirtualSpacesStatus(Integer examVirtualSpacesStatus) {
		this.examVirtualSpacesStatus = examVirtualSpacesStatus;
	}

	public Integer getSupplement() {
		return supplement;
	}

	public void setSupplement(Integer supplement) {
		this.supplement = supplement;
	}


	public List<GridCourse> getGridCourseList() {
        return gridCourseList;
    }

    public void setGridCourseList(List<GridCourse> gridCourseList) {
        this.gridCourseList = gridCourseList;
    }

    public CloudSignup getCloudSignup() {
        return cloudSignup;
    }

    public void setCloudSignup(CloudSignup cloudSignup) {
        this.cloudSignup = cloudSignup;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public CloudProfession getCloudProfession() {
        return cloudProfession;
    }

    public void setCloudProfession(CloudProfession cloudProfession) {
        this.cloudProfession = cloudProfession;
    }

    public CloudLevel getCloudLevel() {
        return cloudLevel;
    }

    public void setCloudLevel(CloudLevel cloudLevel) {
        this.cloudLevel = cloudLevel;
    }

    public Long getExamEndTime() {
        return examEndTime;
    }

    public void setExamEndTime(Long examEndTime) {
        this.examEndTime = examEndTime;
    }

    public Integer getHaveExamTimes() {
        return haveExamTimes;
    }

    public void setHaveExamTimes(Integer haveExamTimes) {
        this.haveExamTimes = haveExamTimes;
    }

    public String getAdoptPercent() {
        return adoptPercent;
    }

    public void setAdoptPercent(String adoptPercent) {
        this.adoptPercent = adoptPercent;
    }

    public int getFailNum() {
        return failNum;
    }

    public void setFailNum(int failNum) {
        this.failNum = failNum;
    }

    public Double getDistinction() {
        return distinction;
    }

    public void setDistinction(Double distinction) {
        this.distinction = distinction;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getUnSumScore() {
        return unSumScore;
    }

    public void setUnSumScore(Integer unSumScore) {
        this.unSumScore = unSumScore;
    }

    public Double getPh() {
        return ph;
    }

    public void setPh(Double ph) {
        this.ph = ph;
    }

    public Integer getSumScore() {
        return sumScore;
    }

    public void setSumScore(Integer sumScore) {
        this.sumScore = sumScore;
    }

    public Question getQuestion() {
        return question;
    }

    public void setQuestion(Question question) {
        this.question = question;
    }

    public QuestionDepot getQuestionDepot() {
        return questionDepot;
    }

    public void setQuestionDepot(QuestionDepot questionDepot) {
        this.questionDepot = questionDepot;
    }

    public String getQuestionDepotName() {
        return questionDepotName;
    }

    public void setQuestionDepotName(String questionDepotName) {
        this.questionDepotName = questionDepotName;
    }

    public SignUpAuth getSignUpAuth() {
        return signUpAuth;
    }

    public void setSignUpAuth(SignUpAuth signUpAuth) {
        this.signUpAuth = signUpAuth;
    }

    public ExamRegist getExamRegist() {
        return examRegist;
    }

    public void setExamRegist(ExamRegist examRegist) {
        this.examRegist = examRegist;
    }

    public CertificateRecord getCertificateRecord() {
        return certificateRecord;
    }

    public void setCertificateRecord(CertificateRecord certificateRecord) {
        this.certificateRecord = certificateRecord;
    }

    public PersonalTemplate getPersonalTemplate() {
        return personalTemplate;
    }

    public void setPersonalTemplate(PersonalTemplate personalTemplate) {
        this.personalTemplate = personalTemplate;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public List<Member> getInvigilatorList() {
        return invigilatorList;
    }

    public void setInvigilatorList(List<Member> invigilatorList) {
        this.invigilatorList = invigilatorList;
    }

    public Organization getCompany() {
        return company;
    }

    public void setCompany(Organization company) {
        this.company = company;
    }

    public List<Long> getTime() {
        return time;
    }

    public void setTime(List<Long> time) {
        this.time = time;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

	public PaperInstance getPaper() {
		return paper;
	}

	public void setPaper(PaperInstance paper) {
		this.paper = paper;
	}

	public SignUp getSignUp() {
		return signUp;
	}

	public void setSignUp(SignUp signUp) {
		this.signUp = signUp;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public PaperClass getPaperClass() {
		return paperClass;
	}

	public void setPaperClass(PaperClass paperClass) {
		this.paperClass = paperClass;
	}

	public Organization getPublishOrganization() {
		return publishOrganization;
	}

	public void setPublishOrganization(Organization publishOrganization) {
		this.publishOrganization = publishOrganization;
	}

	public Organization getOwnerOrganization() {
		return ownerOrganization;
	}

	public void setOwnerOrganization(Organization ownerOrganization) {
		this.ownerOrganization = ownerOrganization;
	}

	public Member getPublisher() {
		return publisher;
	}

	public void setPublisher(Member publisher) {
		this.publisher = publisher;
	}

	public List<MarkConfig> getMarkConfigs() {
		return markConfigs;
	}

	public void setMarkConfigs(List<MarkConfig> markConfigs) {
		this.markConfigs = markConfigs;
	}

	public List<AudienceItem> getAudienceItems() {
		return audienceItems;
	}

	public void setAudienceItems(List<AudienceItem> audienceItems) {
		this.audienceItems = audienceItems;
	}

	public ExamRecord getExamRecord() {
		return examRecord;
	}

	public void setExamRecord(ExamRecord examRecord) {
		this.examRecord = examRecord;
	}


	public Integer getRemainTimes() {
		return remainTimes;
	}

	public void setRemainTimes(Integer remainTimes) {
		this.remainTimes = remainTimes;
	}

	public List<BusinessTopic> getTopic() {
		return topic;
	}

	public void setTopic(List<BusinessTopic> topic) {
		this.topic = topic;
	}

	public Integer getSubmitPaperNumber() {
		return submitPaperNumber;
	}

	public Integer getPassNumber() {
		return passNumber;
	}

	public void setSubmitPaperNumber(Integer submitPaperNumber) {
		this.submitPaperNumber = submitPaperNumber;
	}

	public void setPassNumber(Integer passNumber) {
		this.passNumber = passNumber;
	}

	public Integer getExamedTimes() {
		return examedTimes;
	}

	public void setExamedTimes(Integer examedTimes) {
		this.examedTimes = examedTimes;
	}

	public List<ExamNotice> getExamNotices() {
		return examNotices;
	}

	public void setExamNotices(List<ExamNotice> examNotices) {
		this.examNotices = examNotices;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

    public Profession getProfession() {
        return profession;
    }

    public void setProfession(Profession profession) {
        this.profession = profession;
    }

    public Profession getSubProfession() {
        return subProfession;
    }

    public void setSubProfession(Profession subProfession) {
        this.subProfession = subProfession;
    }

    public Integer getIndex() {
		return Index;
	}

	public void setIndex(Integer index) {
		Index = index;
	}

	public ProfessionLevel getLevel() {
        return level;
    }

    public void setLevel(ProfessionLevel level) {
        this.level = level;
    }

    public EquipmentType getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(EquipmentType equipmentType) {
        this.equipmentType = equipmentType;
    }

	public Boolean getBelongAuditTime() {
		return belongAuditTime;
	}

	public void setBelongAuditTime(Boolean belongAuditTime) {
		this.belongAuditTime = belongAuditTime;
	}

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public GridLevel getGridLevel() {
        return gridLevel;
    }

    public void setGridLevel(GridLevel gridLevel) {
        this.gridLevel = gridLevel;
    }

    public Boolean getHaveChbnCertificate() {
        return haveChbnCertificate;
    }

    public void setHaveChbnCertificate(Boolean haveChbnCertificate) {
        this.haveChbnCertificate = haveChbnCertificate;
    }

    public Boolean getFinishAllCourse() {
        return finishAllCourse;
    }

    public void setFinishAllCourse(Boolean finishAllCourse) {
        this.finishAllCourse = finishAllCourse;
    }

    public GridSignup getGridSignup() {
        return gridSignup;
    }

    public void setGridSignup(GridSignup gridSignup) {
        this.gridSignup = gridSignup;
    }

    public String getPublicClient() {
        return publicClient;
    }

    public void setPublicClient(String publicClient) {
        this.publicClient = publicClient;
    }

    public Boolean getFinishEnterFace() {
        return finishEnterFace;
    }

    public void setFinishEnterFace(Boolean finishEnterFace) {
        this.finishEnterFace = finishEnterFace;
    }

    public Integer getFaceStatus() {
        return faceStatus;
    }

    public void setFaceStatus(Integer faceStatus) {
        this.faceStatus = faceStatus;
    }

	public Integer getPushLearningPlan() {
		return pushLearningPlan;
	}

	public void setPushLearningPlan(Integer pushLearningPlan) {
		this.pushLearningPlan = pushLearningPlan;
	}

	public Long getRemindTime() {
		return remindTime;
	}

	public void setRemindTime(Long remindTime) {
		this.remindTime = remindTime;
	}

	public String getRemindType() {
		return remindType;
	}

	public void setRemindType(String remindType) {
		this.remindType = remindType;
	}

	public Boolean getExamAgain() {
		return examAgain;
	}

	public void setExamAgain(Boolean examAgain) {
		this.examAgain = examAgain;
	}

	public List<RelevanceCourseExam> getExamCourseList() {
		return examCourseList;
	}

	public void setExamCourseList(List<RelevanceCourseExam> examCourseList) {
		this.examCourseList = examCourseList;
	}

	public String getPaperPath() {
		return paperPath;
	}

	public void setPaperPath(String paperPath) {
		this.paperPath = paperPath;
	}

	public QuestionCopy getQuestionCopy() {
		return questionCopy;
	}

	public void setQuestionCopy(QuestionCopy questionCopy) {
		this.questionCopy = questionCopy;
	}

	public AnswerRecord getAnswerRecord() {
		return answerRecord;
	}

	public void setAnswerRecord(AnswerRecord answerRecord) {
		this.answerRecord = answerRecord;
	}

	public QuestionAttrCopy getQuestionAttrCopy() {
		return questionAttrCopy;
	}

	public void setQuestionAttrCopy(QuestionAttrCopy questionAttrCopy) {
		this.questionAttrCopy = questionAttrCopy;
	}

	public PaperInstanceQuestionCopy getPaperInstanceQuestionCopy() {
		return paperInstanceQuestionCopy;
	}

	public void setPaperInstanceQuestionCopy(PaperInstanceQuestionCopy paperInstanceQuestionCopy) {
		this.paperInstanceQuestionCopy = paperInstanceQuestionCopy;
	}
}
