package com.zxy.product.exam.entity;


import com.zxy.product.exam.jooq.tables.pojos.SignupRecordEntity;

public class SignupRecord extends SignupRecordEntity{


	private static final long serialVersionUID = 7767814358562302342L;

	/**
     * 待审核
     */
    public static final int STATUS_APPROVE = 0;

    /**
     * 已通过
     */
    public static final int STATUS_PASSED = 1;

    /**
     * 已拒绝
     */
    public static final int STATUS_REFUSE = 2;

    /**
     * 预审核id，系统机器人
     */
    public static final String PRE_AUDIT_MEMBER_ID = "system_robot";
    public static final String PRE_AUDIT_MEMBER_NAME = "系统自动审核";

    /** 是否最新 0否 */
    public static final Integer IS_CURRENT_NO = 0;
    /** 是否最新 1是 */
    public static final Integer IS_CURRENT_YES = 1;

}
