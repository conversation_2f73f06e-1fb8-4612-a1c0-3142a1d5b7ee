package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.AnswerRecordEntity;
import org.jooq.impl.TableImpl;

import java.math.BigDecimal;

import static com.zxy.product.exam.jooq.Tables.*;

/**
 * <AUTHOR> ouyang(<EMAIL>)
 */
public class AnswerRecord extends AnswerRecordEntity{

    /**
     *
     */
    private static final long serialVersionUID = 4049197054960772146L;

    /**
     * 正确
     */
    public static final Integer RIGHT = 1;

    /**
     * 错误
     */
    public static final Integer WRONG = 0;

    public static final Integer ZERO_SCORE = 0;

    public static final String STRING_ANSWER_RECORD = "answer_record";
    public static final String STRING_ANSWER_RECORD_2019 = "answer_record_2019";
    public static final String STRING_ANSWER_RECORD_2020 = "answer_record_2020";
    public static final String STRING_ANSWER_RECORD_2021 = "answer_record_2021";
    public static final String STRING_ANSWER_RECORD_2022 = "answer_record_2022";
    public static final String STRING_ANSWER_RECORD_2023 = "answer_record_2023";
    public static final String STRING_ANSWER_RECORD_2024 = "answer_record_2024";
    public static final String STRING_ANSWER_RECORD_2025 = "answer_record_2025";
    public static final String STRING_ANSWER_RECORD_2026 = "answer_record_2026";
    public static final String STRING_ANSWER_RECORD_2027 = "answer_record_2027";
    public static final String STRING_ANSWER_RECORD_2028 = "answer_record_2028";
    public static final String STRING_ANSWER_RECORD_2029 = "answer_record_2029";
    public static final String STRING_ANSWER_RECORD_2030 = "answer_record_2030";

    private BigDecimal sumScore;

    private int count;


    private QuestionCopy questionCopy;

    private String questionCopyParentId;

    private Integer seq;

    private String attrValue;

    public String getAttrValue() {
        return attrValue;
    }

    public void setAttrValue(String attrValue) {
        this.attrValue = attrValue;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public String getQuestionCopyParentId() {
        return questionCopyParentId;
    }

    public void setQuestionCopyParentId(String questionCopyParentId) {
        this.questionCopyParentId = questionCopyParentId;
    }

    public int getCount() {
        return count;
    }


    public void setCount(int count) {
        this.count = count;
    }



    public BigDecimal getSumScore() {
        return sumScore;
    }


    public void setSumScore(BigDecimal sumScore) {
        this.sumScore = sumScore;
    }


    public QuestionCopy getQuestionCopy() {
        return questionCopy;
    }


    public void setQuestionCopy(QuestionCopy questionCopy) {
        this.questionCopy = questionCopy;
    }


    public static TableImpl<?> getAnswerRecordTable(String answerRecordStringTable) {
        if (AnswerRecord.STRING_ANSWER_RECORD_2019.equals(answerRecordStringTable)) return ANSWER_RECORD_2019;
        if (AnswerRecord.STRING_ANSWER_RECORD_2020.equals(answerRecordStringTable)) return ANSWER_RECORD_2020;
        if (AnswerRecord.STRING_ANSWER_RECORD_2021.equals(answerRecordStringTable)) return ANSWER_RECORD_2021;
        if (AnswerRecord.STRING_ANSWER_RECORD_2022.equals(answerRecordStringTable)) return ANSWER_RECORD_2022;
        if (AnswerRecord.STRING_ANSWER_RECORD_2023.equals(answerRecordStringTable)) return ANSWER_RECORD_2023;
        if (AnswerRecord.STRING_ANSWER_RECORD_2024.equals(answerRecordStringTable)) return ANSWER_RECORD_2024;
        if (AnswerRecord.STRING_ANSWER_RECORD_2025.equals(answerRecordStringTable)) return ANSWER_RECORD_2025;
        if (AnswerRecord.STRING_ANSWER_RECORD_2026.equals(answerRecordStringTable)) return ANSWER_RECORD_2026;
        if (AnswerRecord.STRING_ANSWER_RECORD_2027.equals(answerRecordStringTable)) return ANSWER_RECORD_2027;
        if (AnswerRecord.STRING_ANSWER_RECORD_2028.equals(answerRecordStringTable)) return ANSWER_RECORD_2028;
        if (AnswerRecord.STRING_ANSWER_RECORD_2029.equals(answerRecordStringTable)) return ANSWER_RECORD_2029;
        if (AnswerRecord.STRING_ANSWER_RECORD_2030.equals(answerRecordStringTable)) return ANSWER_RECORD_2030;
        return ANSWER_RECORD;
    }



}
