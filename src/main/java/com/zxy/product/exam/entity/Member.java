package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.MemberEntity;

public class Member extends MemberEntity {

    /**
     *
     */
    private static final long serialVersionUID = 8117000700366419972L;

    public static final String uri = "human/member";

    /** 1内部用户 */
    public static final Integer FROM_INNER = 1;
    /** 2注册用户*/
    public static final Integer FROM_REGISTER = 2;

    /** 集团总部的path %*/
    public static final String JT_PATH = "1,10000001,96493,%";

    private Organization organization; // 所属部门
    private Organization companyOrganization; // 所属机构
    private Organization rootOrganization; // 所属根组织
    private Organization company; // 单位

    private ExamRecord examRecord;

    private Exam exam;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Organization getCompanyOrganization() {
        return companyOrganization;
    }

    public void setCompanyOrganization(Organization companyOrganization) {
        this.companyOrganization = companyOrganization;
    }

    public Organization getRootOrganization() {
        return rootOrganization;
    }

    public void setRootOrganization(Organization rootOrganization) {
        this.rootOrganization = rootOrganization;
    }

    public ExamRecord getExamRecord() {
        return examRecord;
    }

    public void setExamRecord(ExamRecord examRecord) {
        this.examRecord = examRecord;
    }

	public Exam getExam() {
		return exam;
	}

	public void setExam(Exam exam) {
		this.exam = exam;
	}

    public Organization getCompany() {
        return company;
    }

    public void setCompany(Organization company) {
        this.company = company;
    }

}
