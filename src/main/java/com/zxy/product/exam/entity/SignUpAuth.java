package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.SignUpAuthEntity;
/**
 *
 * ClassName: SignUpAuth <br/>
 * Reason: 报名认证信息表. <br/>
 * date: 2017年10月19日 下午7:46:00 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
public class SignUpAuth  extends SignUpAuthEntity{


	private static final long serialVersionUID = 3481797979453361132L;

	private String idcard;
	private String mail;
	private String phone;
	private String orgName;
	private Profession profession; // 专业
    private Profession subProfession; // 子专业
    private EquipmentType equipmentType;// 设备
    private Member member;
    private Organization organization;
    private int myCount; // 我已报名的次数
    private int overSignupRule; // 是否超过报名次数限制

    /** 是否超过报名次数限制 ：0 未超过 */
    public static final Integer NOT_OVER = 0;
    /** 是否超过报名次数限制 ：1 已超过 */
    public static final Integer IS_OVER = 1;


    public int getOverSignupRule() {
        return overSignupRule;
    }
    public void setOverSignupRule(int overSignupRule) {
        this.overSignupRule = overSignupRule;
    }
	public int getMyCount() {
        return myCount;
    }
    public void setMyCount(int myCount) {
        this.myCount = myCount;
    }
	public String getIdcard() {
		return idcard;
	}
	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Profession getProfession() {
        return profession;
    }
    public void setProfession(Profession profession) {
        this.profession = profession;
    }
    public Profession getSubProfession() {
        return subProfession;
    }
    public void setSubProfession(Profession subProfession) {
        this.subProfession = subProfession;
    }
    public EquipmentType getEquipmentType() {
		return equipmentType;
	}
	public void setEquipmentType(EquipmentType equipmentType) {
		this.equipmentType = equipmentType;
	}
    public Member getMember() {
        return member;
    }
    public void setMember(Member member) {
        this.member = member;
    }
    public Organization getOrganization() {
        return organization;
    }
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }



}
