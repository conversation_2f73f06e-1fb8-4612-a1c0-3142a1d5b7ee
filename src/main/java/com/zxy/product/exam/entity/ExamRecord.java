package com.zxy.product.exam.entity;


import com.zxy.product.exam.jooq.tables.pojos.ExamRecordEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * 考试记录
 */
public class ExamRecord extends ExamRecordEntity {
	private static final long serialVersionUID = -1155291505063263932L;
	private Member member;
	private Organization organization;
	private Organization company;
	private Exam exam;
	private Integer totalScore;// 试卷总分
	private Integer signupStatus;// 报名审核状态
	/**
	 * 考试参与时长
	 */
	private Integer participateTime;

    public static final String STRING_EXAM_RECORD = "exam_record";
    public static final String STRING_EXAM_RECORD_2017 = "exam_record_2017";
    public static final String STRING_EXAM_RECORD_2018 = "exam_record_2018";
    public static final String STRING_EXAM_RECORD_2019 = "exam_record_2019";
    public static final String STRING_EXAM_RECORD_2020 = "exam_record_2020";
    public static final String STRING_EXAM_RECORD_2021 = "exam_record_2021";
    public static final String STRING_EXAM_RECORD_2022 = "exam_record_2022";
    public static final String STRING_EXAM_RECORD_2023 = "exam_record_2023";
    public static final String STRING_EXAM_RECORD_2024 = "exam_record_2024";
	public static final String STRING_EXAM_RECORD_2025 = "exam_record_2025";
	public static final String STRING_EXAM_RECORD_2026 = "exam_record_2026";
	public static final String STRING_EXAM_RECORD_2027 = "exam_record_2027";
	public static final String STRING_EXAM_RECORD_2028 = "exam_record_2028";
	public static final String STRING_EXAM_RECORD_2029 = "exam_record_2029";
	public static final String STRING_EXAM_RECORD_2030 = "exam_record_2030";

    public static final String[] STRING_EXAM_RECORD_ALL = {
            STRING_EXAM_RECORD_2017,
            STRING_EXAM_RECORD_2018,
            STRING_EXAM_RECORD_2019,
            STRING_EXAM_RECORD_2020,
            STRING_EXAM_RECORD_2021,
            STRING_EXAM_RECORD_2022,
            STRING_EXAM_RECORD_2023,
            STRING_EXAM_RECORD_2024,
			STRING_EXAM_RECORD_2025,
			STRING_EXAM_RECORD_2026,
			STRING_EXAM_RECORD_2027,
			STRING_EXAM_RECORD_2028,
			STRING_EXAM_RECORD_2029,
			STRING_EXAM_RECORD_2030
    };

	public static final String[] STRING_EXAM_RECORD_OTHER = {
			STRING_EXAM_RECORD_2021,
			STRING_EXAM_RECORD_2022,
			STRING_EXAM_RECORD_2023,
			STRING_EXAM_RECORD_2024,
			STRING_EXAM_RECORD_2025,
			STRING_EXAM_RECORD_2026,
			STRING_EXAM_RECORD_2027,
			STRING_EXAM_RECORD_2028,
			STRING_EXAM_RECORD_2029,
			STRING_EXAM_RECORD_2030
	};

	/**
	 * 全部
	 */
	public static final Integer STATUS_ALL = 0;

	/**
	 * 未开始
	 */
	public static final Integer STATUS_TO_BE_STARTED = 1;

	/**
	 * 进行中
	 */
	public static final Integer STATUS_DOING = 2;

	/**
	 * 异常
	 */
	public static final Integer STATUS_TIME_EXCEPTION = 4;

	/**
	 * 待评卷
	 */
	public static final Integer STATUS_TO_BE_OVER = 5;

	/**
	 * 及格
	 */
	public static final Integer STATUS_PASS = 6;

	/**
	 * 不及格
	 */
	public static final Integer STATUS_NOT_PASS = 7;

	/**
	 * 没有设置及格分数的  考完就是已完成
	 */
	public static final Integer STATUS_FINISHED = 8;

	/**
	 * 作废
	 */
	public static final Integer STATUS_NULLIFY = 9;

	/**
	 * pc
	 */
	public static final Integer CLIENT_TYPE_PC = 1;

	/**
	 * app
	 */
	public static final Integer CLIENT_TYPE_APP = 2;

	public static final Integer IS_RESET = 1;

	/**
	 * 当前考试记录
	 */
	public static final Integer CURRENT = 1;

	public static final Integer IS_NOT_CURRENT = 0;

	/**
	 * 已经完成
	 */
	public static final Integer IS_FINISHED = 1;
	/**
	 * 没完成
	 */
	public static final Integer IS_NOT_FINISHED = 0;

	public static final Integer EXCEPTION_ORDER = 2;

	public static final String SUBMIT_PAPER_EXAM_RECORD_ID = "examRecordId";
	public static final String SUBMIT_PAPER_EXAM_ID = "submitPaperExamId";
	public static final String SUBMIT_PAPER_TYPE = "submitType";
	public static final String SUBMIT_PAPER_CLIENT_TYPE = "clientType";
	public static final String SUBMIT_PAPER_TIME = "submitTime";
	public static final String SUBMIT_PAPER_ANSWER_RECORD = "answerRecord";
	public static final String SUBMIT_USER_IP = "submitUserIp";
	public static final String SUBMIT_PAPER_NO_ANSWER_COUNT = "submitPaperNoAnswerCount";
	public static final String SUBMIT_PAPER_ANSWERED_COUNT = "submitPaperAnsweredCount";
	public static final String SUBMIT_DETAIL_TYPE = "submitDetailType";
	public static final String SUBMIT_CLIENT_VERSION = "submitClientVersion";

	/*
		{ 1：自动，2：手动，3：超时，4：强制，5：切屏 }
	 */
	public static final Integer SUBMIT_TYPE_AUTO = 1;
	public static final Integer SUBMIT_TYPE_HAND = 2;
	public static final Integer SUBMIT_TYPE_TIMEOUT = 3;
	public static final Integer SUBMIT_TYPE_FORCE = 4;
	public static final Integer SUBMIT_TYPE_FULL_SWITCH = 5;



	/**
	 * 导出头部字段描述
	 */
	//序号
	public static final String TEMPLATE_INDEX = "序号";
	public static final String TEMPLATE_FULL_NAME = "姓名";
	public static final String TEMPLATE_MEMBER_NAME = "员工编号";
	public static final String TEMPLATE_ORGANIZATION = "归属部门";
	public static final String TEMPLATE_COMPANY = "单位";
	public static final String TEMPLATE_POSITION = "职位";
	public static final String TEMPLATE_START_TIME = "开始时间";
	public static final String TEMPLATE_END_TIME = "结束时间";
	public static final String TEMPLATE_LAST_SAVA_TIME = "最后保存时间";
	public static final String TEMPLATE_SUBMIT_TIME = "交卷时间";
	public static final String TEMPLATE_CLIENT = "使用终端";
	public static final String TEMPLATE_CLIENT_VERSION = "终端类型";
	public static final String TEMPLATE_STATUS = "答题状态";
	public static final String TEMPLATE_SCORE = "成绩";
	public static final String TEMPLATE_USED_TIME = "用时";
	public static final String TEMPLATE_FIRST_X_TIMES = "第x次";
	public static final String TEMPLATE_PERSONAL_PASSWORD = "个人密码";
	public static final String TEMPLATE_USER_IP = "用户IP";
	public static final String TEMPLATE_CUT_SCREEN_TIMES = "已切屏次数";
	public static final String TEMPLATE_NO_ANSWER_TIMES = "未答题数";
	public static final String TEMPLATE_YES_ANSWER_TIMES = "已答题数";
	public static final String TEMPLATE_RIGHT_COUNT = "正确题数";
	public static final String TEMPLATE_QUESTION_COUNT = "总题数";

	/**
	 * 全量答题记录
	 */
	public static final String FULL_ANSWER_JSON = "fullAnswerJson";

	public static final String EXAM_RECORD = "examRecord";
	/**
	 * 增量记录
	 */
	public static final String MODIFY_ANSWER_RECORD = "modifyAnswerRecord";

	/**
	 * 缓存时间1天
	 */
	public static final int CACHE_TIME = 60 * 60 * 24 * 1;


	/**
	 * 第一次考试记录
	 */
	public static final Integer FIRST_TIME = 1;

    /**
     * 人脸监考状态
     */
    public static final Integer MARK_NORMAL = 2;
    public static final Integer MARK_ABNORMAL = 3;

	/**
	 * 用时
	 */
	private Integer usedTime;

	/**
	 * 最新缓存时间，用于不同端参考同一考试，作为时间上的判断
	 * 如果后台缓存时间比客户端缓存时间大，那就取后台服务的数据，
	 * 反则直接取客户端缓存数据
	 */
	private Long lastCacheTime;

	/**
	 * 当前服务器时间
	 */
	private Long currentTime;

	/**
	 * 导出序号
	 */
	private Integer index;


	private PaperInstance paperInstance;

	/**
	 * 最高成绩recordId
	 */
	private String topScoreRecordId;

	private List<QuestionCopy> questionCopyList;

	public List<QuestionCopy> getQuestionCopyList() {
		return questionCopyList;
	}

	public void setQuestionCopyList(List<QuestionCopy> questionCopyList) {
		this.questionCopyList = questionCopyList;
	}

	public static String getExamRecordKey(String examId, String memberId) {
		return examId + "#" + memberId;
	}

	public static String getExamRecordKeyByParms(String... params) {
		return Arrays.stream(params).collect(Collectors.joining("#"));
	}


	private  Integer count;

	private Integer type;

	private int row; // 行数，导入时辅助
	private List<RowError> errors = new ArrayList<>();

	private String times4String;
	private String index4String;

	public Integer getParticipateTime() {
		return participateTime;
	}

	public void setParticipateTime(Integer participateTime) {
		this.participateTime = participateTime;
	}

	public String getTimes4String() {
		return times4String;
	}

	public void setTimes4String(String times4String) {
		this.times4String = times4String;
	}


	public String getIndex4String() {
		return index4String;
	}

	public void setIndex4String(String index4String) {
		this.index4String = index4String;
	}

	public int getRow() {
		return row;
	}

	public void setRow(int row) {
		this.row = row;
	}

	public List<RowError> getErrors() {
		return errors;
	}

	public void setErrors(List<RowError> errors) {
		this.errors = errors;
	}

	public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Organization getCompany() {
        return company;
    }

    public void setCompany(Organization company) {
        this.company = company;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public enum SubmitType {
		Hand, Auto, Force
	}

	public String encryptScore;

	public String getEncryptScore() {
		return encryptScore;
	}

	public void setEncryptScore(String encryptScore) {
		this.encryptScore = encryptScore;
	}

	public Exam getExam() {
		return exam;
	}

	public void setExam(Exam exam) {
		this.exam = exam;
	}

	public Integer getTotalScore() {
		return totalScore;
	}

	public void setTotalScore(Integer totalScore) {
		this.totalScore = totalScore;
	}

	public Integer getSignupStatus() {
		return signupStatus;
	}

	public void setSignupStatus(Integer signupStatus) {
		this.signupStatus = signupStatus;
	}

	public Integer getUsedTime() {
		return usedTime;
	}

	public void setUsedTime(Integer useTime) {
		this.usedTime = useTime;
	}

	public Long getLastCacheTime() {
		return lastCacheTime;
	}

	public void setLastCacheTime(Long lastCacheTime) {
		this.lastCacheTime = lastCacheTime;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}


	public Long getCurrentTime() {
		return currentTime;
	}


	public void setCurrentTime(Long currentTime) {
		this.currentTime = currentTime;
	}


	public PaperInstance getPaperInstance() {
		return paperInstance;
	}


	public void setPaperInstance(PaperInstance paperInstance) {
		this.paperInstance = paperInstance;
	}



	public Organization getOrganization() {
		return organization;
	}



	public void setOrganization(Organization organization) {
		this.organization = organization;
	}



	public String getTopScoreRecordId() {
		return topScoreRecordId;
	}



	public void setTopScoreRecordId(String topScoreRecordId) {
		this.topScoreRecordId = topScoreRecordId;
	}



}
