package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.PaperClassEntity;

import java.util.List;

public class PaperClass extends PaperClassEntity {

    private static final long serialVersionUID = 8890205382089151228L;

    public static final int PAPER_INSTANCE_FACTOR = 5; // 试题因子
    public static final int PAPER_CLASS_TYPE_TACTIC = 3; // 随机组卷
    public static final int PAPER_CLASS_TYPE_TEMP = 2; // 临时组卷
    public static final int PAPER_CLASS_TYPE_NORMAL = 1; // 普通试卷

    /**
     * 试题默认顺序
     */
    public static final int PAPER_SORT_REMOTE = 1;
    /**
     * 试题乱序
     */
    public static final int PAPER_SORT_QUESTION = 2;
    /**
     * 选项乱序
     */
    public static final int PAPER_SORT_QUESTION_ATTR = 3;
    /**
     * 试题和选项乱序
     */
    public static final int PAPER_SORT_QUESTION_AND_ATTR = 4;

    public static final String URI = "exam/exam";

	public static final Integer IS_PUBLISH = 1;

	public static final Integer ASSOCIATED_STATE = 1;

    private Organization organization;

    private Integer examCount;

    private List<PaperClassTactic> paperClassTactics;

    private List<PaperClassQuestion> paperClassQuestions;

    private Exam exam;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    public List<PaperClassTactic> getPaperClassTactics(){
        return paperClassTactics;
    }
    public void setPaperClassTactics(List<PaperClassTactic> paperClassTactics){
        this.paperClassTactics=paperClassTactics;
    }
    public List<PaperClassQuestion> getPaperClassQuestions(){
        return paperClassQuestions;
    }
    public void setPaperClassQuestions(List<PaperClassQuestion> paperClassQuestions){
        this.paperClassQuestions=paperClassQuestions;
    }
    public Integer getExamCount(){
        return examCount;
    }
    public void setExamCount(Integer examCount){
        this.examCount=examCount;
    }

	public Exam getExam() {
		return exam;
	}

	public void setExam(Exam exam) {
		this.exam = exam;
	}
}
