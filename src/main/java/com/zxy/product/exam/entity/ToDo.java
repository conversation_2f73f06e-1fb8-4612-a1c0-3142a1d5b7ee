package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ToDoEntity;

/**
 * <AUTHOR>
 *
 */
public class ToDo extends ToDoEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = -6822382508226750391L;

	/**
	 * 阅卷
	 */
	public static final int MARK_PAPER_TYPE = 1;

	/**
	 * 是否需要评卷 消息头
	 */
	public static final String IS_NEED_MARK = "is-need-mark";

	/**
     * 包含的题型，0：只有其他主观题
     */
	public static final Integer INCLUDE_TYPE_0 = 0;

	/**
     * 包含的题型，1：只有填空题
     */
    public static final Integer INCLUDE_TYPE_1 = 1;

    /**
     * 包含的题型，2：填空题+其他主观题
     */
    public static final Integer INCLUDE_TYPE_2 = 2;

    /**
     * 是否已被审核，0：否
     */
    public static final Integer AUDITED_0 = 0;

    /**
     * 是否已被审核，1：是
     */
    public static final Integer AUDITED_1 = 1;

    /**
     * 是否已被评卷，0：否
     */
    public static final Integer SUBMITED_0 = 0;

    /**
     * 是否已被评卷，1：是
     */
    public static final Integer SUBMITED_1 = 1;

	private ExamRecord examRecord;

	public ExamRecord getExamRecord() {
		return examRecord;
	}

	public void setExamRecord(ExamRecord examRecord) {
		this.examRecord = examRecord;
	}



}
