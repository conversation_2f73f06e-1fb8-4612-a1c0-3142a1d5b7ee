package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ExamStudyPlanConfigEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2022/6/9
 * @description ：学习计划
 */
public class ExamStudyPlanConfig extends ExamStudyPlanConfigEntity {
    private static final long serialVersionUID = 157124961523879581L;

    /**
     * 考试
     */
    public static final Integer BUSINESS_TYPE_EXAM = 3;

    /**
     * 调研
     */
    public static final Integer BUSINESS_TYPE_RESEARCH = 4;

    /**
     * 是否已经推送 N
     */
    public static final Integer IS_PUSH_NO = 0;

    /**
     * 是否已经推送 Y
     */
    public static final Integer IS_PUSH_YES = 1;

    /**
     * 是否推送学习计划 N
     */
    public static final Integer PUSH_LEARNING_PLAN_NO = 0;

    /**
     * 是否推送学习计划 Y
     */
    public static final Integer PUSH_LEARNING_PLAN_YES = 1;
    /**
     * 推送处理成功 Y
     */
    public static final Integer HANDLE_STATUS = 0;//推送处理成功
    /**
     * 推送处理成功 Y
     */
    public static final Integer HANDLE_STATUS_YES = 1;//推送处理成功
    /**
     * 撤销处理成功 Y
     */
    public static final Integer HANDLE_STATUS_NO = 2;//撤销处理成功
}
