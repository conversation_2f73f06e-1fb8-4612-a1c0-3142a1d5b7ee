package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.OrganizationEntity;

public class Organization extends OrganizationEntity {

    /**
     *
     */
    private static final long serialVersionUID = -6870709483895917568L;

    private String companyName; // 企业名称


    /**
     * 机构id：省公司-北京
     */
    public static final String COMPANY_BJ = "1105092";
    /**
     * 机构id：省公司-天津
     */
    public static final String COMPANY_TJ = "1567690";
    /**
     * 机构id：省公司-河北
     */
    public static final String COMPANY_HB = "1011203";
    /**
     * 机构id：省公司-山西
     */
    public static final String COMPANY_SX = "98209";
    /**
     * 机构id：省公司-内蒙古
     */
    public static final String COMPANY_NMG = "1093623";
    /**
     * 机构id：省公司-辽宁
     */
    public static final String COMPANY_LN = "1061451";
    /**
     * 机构id：省公司-吉林
     */
    public static final String COMPANY_JL = "1073360";
    /**
     * 机构id：省公司-黑龙江
     */
    public static final String COMPANY_HLJ = "44261";
    /**
     * 机构id：省公司-上海
     */
    public static final String COMPANY_SH = "1754249";
    /**
     * 机构id：省公司-苏州
     */
    public static final String COMPANY_SZ = "1148390";
    /**
     * 机构id：省公司-浙江
     */
    public static final String COMPANY_ZJ = "80307";
    /**
     * 机构id：省公司-福建
     */
    public static final String COMPANY_FJ = "1093072";
    /**
     * 机构id：省公司-安徽
     */
    public static final String COMPANY_AH = "552512";
    /**
     * 机构id：省公司-江西
     */
    public static final String COMPANY_JX = "567498";
    /**
     * 机构id：省公司-山东
     */
    public static final String COMPANY_SD = "1180849";
    /**
     * 机构id：省公司-河南
     */
    public static final String COMPANY_HN = "55836";
    /**
     * 机构id：省公司-湖北
     */
    public static final String COMPANY_HB_2 = "1018307";
    /**
     * 机构id：省公司-湖南
     */
    public static final String COMPANY_HN_2 = "105408";
    /**
     * 机构id：省公司-广东
     */
    public static final String COMPANY_GD = "1073923";
    /**
     * 机构id：省公司-广西
     */
    public static final String COMPANY_GX = "993478";
    /**
     * 机构id：省公司-海南
     */
    public static final String COMPANY_HN_3 = "1013550";
    /**
     * 机构id：省公司-重庆
     */
    public static final String COMPANY_CQ = "1095592";
    /**
     * 机构id：省公司-四川
     */
    public static final String COMPANY_SC = "1495996";
    /**
     * 机构id：省公司-贵州
     */
    public static final String COMPANY_GZ = "1495590";
    /**
     * 机构id：省公司-云南
     */
    public static final String COMPANY_YN = "1094794";
    /**
     * 机构id：省公司-西藏
     */
    public static final String COMPANY_XZ = "1133482";
    /**
     * 机构id：省公司-陕西
     */
    public static final String COMPANY_SX_2 = "1012970";
    /**
     * 机构id：省公司-甘肃
     */
    public static final String COMPANY_GS = "1322085";
    /**
     * 机构id：省公司-青海
     */
    public static final String COMPANY_QH = "1100282";
    /**
     * 机构id：省公司-宁夏
     */
    public static final String COMPANY_NX = "1012774";
    /**
     * 机构id：省公司-新疆
     */
    public static final String COMPANY_XJ = "1062701";
    /**
     * 机构id：网络事业部
     */
    public static final String COMPANY_WLB_1 = "96531";
    /**
     * 机构id：中国移动通信集团有限公司/网络事业部
     */
    public static final String COMPANY_WLB_2 = "c93e4dec-be81-40b9-9833-12cdb502d908";
    /**
     * 机构id：集团公司
     */
    public static final String COMPANY_JT = "96493";




    /**
     * 机构id：辛姆巴科公司
     */
    public static final String COMPANY_XMBK = "79495165";
    /**
     * 机构id：卓望控股有限公司
     */
    public static final String COMPANY_ZWKG = "79495166";
    /**
     * 机构id：中国移动香港有限公司
     */
    public static final String COMPANY_ZGYDXG = "284519";
    /**
     * 机构id：香港机构
     */
    public static final String COMPANY_XGJG = "284518";
    /**
     * 机构id：中移智行网络科技有限公司
     */
    public static final String COMPANY_ZYZX = "52f4b0b6-eab9-4ae8-9d7c-fac64f69362a";


    /**
     * 组织id：中移信息技术有限公司
     */
    public static final String ORGANIZATION_ZYXX = "55c7f92f-0f14-4ce8-a80b-407691bd7958";


    /**
     * 启用的组织
     */
    public static final int ORGANIZATION_STATUS = 1;




    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }





}
