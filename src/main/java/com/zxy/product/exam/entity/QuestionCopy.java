package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.QuestionCopyEntity;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class QuestionCopy extends QuestionCopyEntity implements Comparable<QuestionCopy> {

    /**
     *
     */
    private static final long serialVersionUID = -7293362715917256159L;

    /**
     *	试题顺序常量
     */
    /**
     * 试题ID
     */
    public static final String ORDER_QUESTION_ID = "questionId";
    /**
     * 试题类型
     */
    public static final String ORDER_QUESTION_TYPE = "type";
    /**
     * 试题顺序
     */
    public static final String ORDER_QUESTION_ORDER = "order";
    /**
     * 试题属性
     */
    public static final String ORDER_QUESTION_ATTR = "attrOrder";
    /**
     * 子题目顺序
     */
    public static final String ORDER_QUESTION_SUBS = "subsOrder";
    /**
     * 试题属性ID
     */
    public static final String ORDER_QUESTION_ATTR_ID = "attrId";
    /**
     * 试题属性NAME
     */
    public static final String ORDER_QUESTION_ATTR_NAME = "name";
    /**
     * 试题属性顺序
     */
    public static final String ORDER_QUESTION_ATTR_ORDER = "order";



    private List<QuestionAttrCopy> questionAttrCopys;

    private List<QuestionCopy> subs;

    private AnswerRecord answerRecord;

    private Integer sequence;

    private List<QuestionRecovery> questionRecoverys;

    private Question question;

    private String parsing;

    private String parsingText;


    public String getParsing() {
        return parsing;
    }

    public void setParsing(String parsing) {
        this.parsing = parsing;
    }

    public String getParsingText() {
        return parsingText;
    }

    public void setParsingText(String parsingText) {
        this.parsingText = parsingText;
    }

    public Question getQuestion() {
		return question;
	}

	public void setQuestion(Question question) {
		this.question = question;
	}

	public List<QuestionCopy> getSubs() {
        return subs;
    }

    public void setSubs(List<QuestionCopy> subs) {
        this.subs = subs;
    }

    public List<QuestionAttrCopy> getQuestionAttrCopys() {
        return questionAttrCopys;
    }

    public void setQuestionAttrCopys(List<QuestionAttrCopy> questionAttrCopys) {
        this.questionAttrCopys = questionAttrCopys;
    }

    public AnswerRecord getAnswerRecord() {
        return answerRecord;
    }

    public void setAnswerRecord(AnswerRecord answerRecord) {
        this.answerRecord = answerRecord;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

	public List<QuestionRecovery> getQuestionRecoverys() {
		return questionRecoverys;
	}

	public void setQuestionRecoverys(List<QuestionRecovery> questionRecoverys) {
		this.questionRecoverys = questionRecoverys;
	}

    @Override
    public int compareTo(QuestionCopy o) {
        if (this.getSequence() != null) {
            if (o.getSequence() == null) return 1;
            return this.getSequence().compareTo(o.getSequence());
        }
        return 0;
    }
}
