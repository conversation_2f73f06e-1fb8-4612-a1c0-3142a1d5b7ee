package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.ExamRegistEntity;

import java.math.BigDecimal;

public class ExamRegist extends ExamRegistEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 6158593404262127412L;

	// 移动云考试URL
    public static final  String PERSONAL_CLOUD_URL="exam/personal-cloud-report";
    // 网格长考试URL
    public static final  String PERSONAL_GRID_URL="exam/personal-grid-report";


    public static final String STRING_EXAM_REGIST = "exam_regist";
    public static final String STRING_EXAM_REGIST_2017 = "exam_regist_2017";
    public static final String STRING_EXAM_REGIST_2018 = "exam_regist_2018";
    public static final String STRING_EXAM_REGIST_2019 = "exam_regist_2019";
    public static final String STRING_EXAM_REGIST_2020 = "exam_regist_2020";
    public static final String STRING_EXAM_REGIST_2021 = "exam_regist_2021";
    public static final String STRING_EXAM_REGIST_2022 = "exam_regist_2022";
    public static final String STRING_EXAM_REGIST_2023 = "exam_regist_2023";
    public static final String STRING_EXAM_REGIST_2024 = "exam_regist_2024";
	public static final String STRING_EXAM_REGIST_2025 = "exam_regist_2025";
	public static final String STRING_EXAM_REGIST_2026 = "exam_regist_2026";
	public static final String STRING_EXAM_REGIST_2027 = "exam_regist_2027";
	public static final String STRING_EXAM_REGIST_2028 = "exam_regist_2028";
	public static final String STRING_EXAM_REGIST_2029 = "exam_regist_2029";
	public static final String STRING_EXAM_REGIST_2030 = "exam_regist_2030";

    public static final String[] STRING_EXAM_REGIST_ALL = {
            STRING_EXAM_REGIST_2017,
            STRING_EXAM_REGIST_2018,
            STRING_EXAM_REGIST_2019,
            STRING_EXAM_REGIST_2020,
            STRING_EXAM_REGIST_2021,
            STRING_EXAM_REGIST_2022,
            STRING_EXAM_REGIST_2023,
            STRING_EXAM_REGIST_2024,
			STRING_EXAM_REGIST_2025,
			STRING_EXAM_REGIST_2026,
			STRING_EXAM_REGIST_2027,
			STRING_EXAM_REGIST_2028,
			STRING_EXAM_REGIST_2029,
			STRING_EXAM_REGIST_2030
    };

    public static final String[] STRING_YEAR = {
//            "2017",
//            "2018",
//            "2019",
            "2020",
            "2021",
            "2022",
            "2023",
            "2024",
			"2025",
			"2026",
			"2027",
			"2028",
			"2029",
			"2030"
    };

    public static final String[] STRING_ONLY_YEAR = {
//            "2021",
//            "2022",
            "2023",
            "2024",
			"2025",
			"2026",
			"2027",
			"2028",
			"2029",
			"2030"
    };


	/**
	 * 是否已发放证书： 1是
	 */
	public static final Integer CERTIFICATE_ISSUE_YES = 1;
	/**
	 * 是否已发放证书：0否
	 */
	public static final Integer CERTIFICATE_ISSUE_NO = 0;
	/**
	 * 考试中
	 */
	public static final int STATUS_EXAM_STARTING = 1;
	/**
	 * 未开始
	 */
	public static final int STATUS_NO_BEGIN = 2;
	/**
	 * 审核中
	 */
	public static final int STATUS_APPROVING = 3;
	/**
	 * 已完成
	 */
	public static final int STATUS_FINISHED = 4;

	/**
	 * 被拒绝
	 */
	public static final int  STATUS_BE_REFUSE = 5;

	/**
	 * 已结束
	 */
	public static final int  STATUS_BE_OVER = 6;


	/**
	 * 报名考试
	 */
	public static final int TYPE_SIGN_UP = 1;
	/**
	 * 指定考试
	 */
	public static final int TYPE_SPEC_EXAM = 2;
	public static final int ALREADY_GRANT = 1;
	public static final int NO_GRANT = 2;
	/**
	 * 普通考试(非报名，非指定)
	 */
	public static final int TYPE_ORDINARY_EXAM = 3;

	public static final String TEMPLATE_INDEX = "序号";
	//通过状态
	public static final String HAVE_PASS="已通过";
	public static final String NOT_PASS="未通过";
	public static final String NULLIFY="作废";
	//发放状态
	public static final String HAVE_GRANT="已发放";
	public static final String NOT_GRANT="未发放";

	public static final String SELF_EXAM_DSC="自考";
			public static final String MANUAL_GRANT_DSC="手动发放";
	private Member member;

	private Organization organization;

	private Long examTime;

	private String examName;

	private String examOrgName;
	//是否发放证书
	private Integer isGrant;

	//正确率
	private String correctRate;

	//总正确率
    private String totalCorrectRate;

	//用户正确率JSON
	private String userRateJson;

	//知识点Id
	private String  questionDepotId;

	private Question question;

	private PaperClassQuestion paperClassQuestion;

	private BigDecimal sumScore;

	private Integer countNum;


	private Exam exam;
	//考试开始时间
	private Long startTime;
	private Integer Index;

	private String certificateRecordId;

	private PaperInstance paper;
	private String memberOrganizationId;
	private String examOrganizationId;
	private String professionId;
	private String subProfessionId;
    private String levelId;
	private String equipmentTypeId;


	public String getProfessionId() {
        return professionId;
    }

    public void setProfessionId(String professionId) {
        this.professionId = professionId;
    }

    public String getSubProfessionId() {
        return subProfessionId;
    }

    public void setSubProfessionId(String subProfessionId) {
        this.subProfessionId = subProfessionId;
    }

    public String getLevelId() {
        return levelId;
    }

    public void setLevelId(String levelId) {
        this.levelId = levelId;
    }

    public String getEquipmentTypeId() {
        return equipmentTypeId;
    }

    public void setEquipmentTypeId(String equipmentTypeId) {
        this.equipmentTypeId = equipmentTypeId;
    }

    public String getMemberOrganizationId() {
        return memberOrganizationId;
    }

    public void setMemberOrganizationId(String memberOrganizationId) {
        this.memberOrganizationId = memberOrganizationId;
    }

    public String getExamOrganizationId() {
        return examOrganizationId;
    }

    public void setExamOrganizationId(String examOrganizationId) {
        this.examOrganizationId = examOrganizationId;
    }

    /**
	 * 揭秘云计算
	 */
	public static final String NEWENERGY_EXAM_YJS="63f5d0da-a5ea-4ceb-86b2-a788070fede6";
	/**
     * 人人学IT
     */
    public static final String NEWENERGY_EXAM_IT="66211046-de11-4a94-aa7d-fd917d32ad6a";
    /**
     * 探索大数据和人工智能
     */
    public static final String NEWENERGY_EXAM_DSJ="2e51fe9f-45bb-4b79-bf01-37a1836f4498";
    /**
     * SDN与NFV技术介绍
     */
    public static final String NEWENERGY_EXAM_SDN="ca7f9bdc-48dd-4ec2-aa8e-3d0200a266ae";
    /**
     * 5G技术发展与未来运用
     */
    public static final String NEWENERGY_EXAM_5G="623fb6d9-108d-429c-b014-9e632449fcaf";
    /**
     * 物联网就在你我身边
     */
    public static final String NEWENERGY_EXAM_WLW="a2c3a6cb-d185-43f2-b8a9-665a1c9e2f9f";
    /**
     * CDN技术介绍
     */
    public static final String NEWENERGY_EXAM_CDN="21beee32-72d4-4fcb-8007-328783f9e34f";
    /**
     * 走进安全技术
     */
    public static final String NEWENERGY_EXAM_AQ="9b2ed8b1-ea85-4b3b-8887-8074508b04ae";
    /**
     * 软件开发应知应会
     */
    public static final String NEWENERGY_EXAM_RJ="d0fc6249-6510-4f9c-83ff-134c69fb57a3";


    public PaperInstance getPaper() {
        return paper;
    }

    public void setPaper(PaperInstance paper) {
        this.paper = paper;
    }

    public String getTotalCorrectRate() {
        return totalCorrectRate;
    }

    public void setTotalCorrectRate(String totalCorrectRate) {
        this.totalCorrectRate = totalCorrectRate;
    }

    public Question getQuestion() {
        return question;
    }

    public void setQuestion(Question question) {
        this.question = question;
    }

    public PaperClassQuestion getPaperClassQuestion() {
        return paperClassQuestion;
    }

    public void setPaperClassQuestion(PaperClassQuestion paperClassQuestion) {
        this.paperClassQuestion = paperClassQuestion;
    }


    public BigDecimal getSumScore() {
        return sumScore;
    }

    public void setSumScore(BigDecimal sumScore) {
        this.sumScore = sumScore;
    }

    public Integer getCountNum() {
        return countNum;
    }

    public void setCountNum(Integer countNum) {
        this.countNum = countNum;
    }

    public String getCorrectRate() {
		return correctRate;
	}

	public void setCorrectRate(String correctRate) {
		this.correctRate = correctRate;
	}

	public String getUserRateJson() {
		return userRateJson;
	}

	public void setUserRateJson(String userRateJson) {
		this.userRateJson = userRateJson;
	}

	public String getQuestionDepotId() {
		return questionDepotId;
	}

	public void setQuestionDepotId(String questionDepotId) {
		this.questionDepotId = questionDepotId;
	}

	public Integer getIndex() {
		return Index;
	}

	public void setIndex(Integer index) {
		Index = index;
	}

	//证书记录表
	private CertificateRecord certificateRecord;

	// 判断个人中心档案是否显示操作
	private Integer isOperation;

	/**
	 * 个人中心档案显示操作
	 */
	public static final Integer OPERATION_YES = 1;

	/**
     * 个人中心档案不显示操作
     */
	public static final Integer OPERATION_NO = 0;

	/**
	 * 及格状态，1：不及格
	 */
	public static final Integer PASS_STATUS_NO = 1;

	/**
	 * 及格状态，0：及格
	 */
	public static final Integer PASS_STATUS_YES = 0;

	/**
	 * 及格状态，2：已完成（没有设置及格分数的）
	 */
	public static final Integer PASS_STATUS_FINISH = 2;

	/**
	 * 及格状态，3：作废
	 */
	public static final Integer PASS_STATUS_NULLIFY = 3;

	private ExamRecord examRecord;

	private Integer count;




	public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public ExamRecord getExamRecord() {
        return examRecord;
    }

    public void setExamRecord(ExamRecord examRecord) {
        this.examRecord = examRecord;
    }

    public Integer getIsOperation() {
        return isOperation;
    }

    public void setIsOperation(Integer isOperation) {
        this.isOperation = isOperation;
    }

    public Integer getIsGrant() {
		return isGrant;
	}

	public void setIsGrant(Integer isGrant) {
		this.isGrant = isGrant;
	}

	public CertificateRecord getCertificateRecord() {
		return certificateRecord;
	}

	public void setCertificateRecord(CertificateRecord certificateRecord) {
		this.certificateRecord = certificateRecord;
	}

	public Long getStartTime() {
		return startTime;
	}

	public void setStartTime(Long startTime) {
		this.startTime = startTime;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Long getExamTime() {
		return examTime;
	}

	public void setExamTime(Long examTime) {
		this.examTime = examTime;
	}

	public String getExamName() {
		return examName;
	}

	public void setExamName(String examName) {
		this.examName = examName;
	}

    public Exam getExam() {
        return exam;
    }

    public void setExam(Exam exam) {
        this.exam = exam;
    }

	public String getExamOrgName() {
		return examOrgName;
	}

	public void setExamOrgName(String examOrgName) {
		this.examOrgName = examOrgName;
	}


	public String getCertificateRecordId() {
		return certificateRecordId;
	}

	public void setCertificateRecordId(String certificateRecordId) {
		this.certificateRecordId = certificateRecordId;
	}
}
