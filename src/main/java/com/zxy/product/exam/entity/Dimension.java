package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.DimensionEntity;

import java.util.List;

public class Dimension extends DimensionEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 553420402832655310L;

	public static final Integer IS_EMPTY_YES = 1; // 是否是空维度，是
	public static final Integer IS_EMPTY_NO = 0;// 是否是空维度，否
	public static final String CODE_PREFIX_DY = "dy";// 调研维度编码前缀
	public static final String CODE_PREFIX_PG = "pg";// 评估维度编码前缀

	private List<Question> questions;

	public List<Question> getQuestions() {
		return questions;
	}

	public void setQuestions(List<Question> questions) {
		this.questions = questions;
	}



}
