package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.GridCourseExamEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GridCourseExam extends GridCourseExamEntity {
    private static final long serialVersionUID = 9093780856311904007L;

    /**
     * 级别，1初级，2中级，3高级
      */
    public static final Integer level_1 = 1;
    public static final Integer level_2 = 2;
    public static final Integer level_3 = 3;

    /**
     * 难度类型，1简单，2中等，3困难
     */
    public static final Integer type_1 = 1;
    public static final Integer type_2 = 2;
    public static final Integer type_3 = 3;

    private List<Exam> examList;

    public List<Exam> getExamList() {
        return examList;
    }

    public void setExamList(List<Exam> examList) {
        this.examList = examList;
    }
}
