package com.zxy.product.exam.entity;

import com.zxy.product.exam.jooq.tables.pojos.QuestionDepotEntity;

import java.util.List;

public class QuestionDepot extends QuestionDepotEntity {

	private static final long serialVersionUID = -7887985533772557558L;

	public static final String QUESTION_DEPOT_URI = "exam/question-depot";

	/**
	 * 仅供自己
	 */
	public static final int AUTH_SELF = 0;
	/**
	 * 分享下级
	 */
	public static final int AUTH_LOWER = 1;
	/**
	 * 公开
	 */
	public static final int AUTH_PUBLIC = 2;
	/**
     * 仅创建者可见
     */
    public static final int AUTH_CREATOR = 3;

	public static final int STATUS_PUBLISH = 1;

	//普通考试
	public static final Integer COMMAN_TYPE = 1;

	public static final int STATUS_NO_PUBLISH = 0;

	/**
     * 试题目录类型，3-同步过来的试题
     */
    public static final Integer TYPE_SYNC = 3;

	/**
	 * 导入模板头中文字段描述
	 */
	public static final String TEMPLATE_INDEX = "序号";
	public static final String TEMPLATE_DEPOT_NAME = "目录名称";
	public static final String TEMPLATE_CODE = "编码";
	public static final String TEMPLATE_PARENT_CODE = "上级目录编码";
	public static final String TEMPLATE_ORGANIZATION = "归属部门";
	public static final String TEMPLATE_ORGANIZATION_CODE = "归属部门编码";
	/**
	 * 导出中文字段描述
	 */
	public static final String TEMPLATE_NAME = "名称";

	/**
	 * 导出类型
	 */
	// 模板
	public static final Object EXPORT_TYPE_TEMPLATE = 1;
	// 导出数据
	public static final Object EXPORT_TYPE_DATA = 2;
    // 导出错误数据
	public static final Object EXPORT_TYPE_ERROR = 3;

	/**
	 * 已加密
	 */
	public static final Integer ENCRYPT_YES = 1;
	/**
	 * 未加密
	 */
	public static final Integer ENCRYPT_NO = 0;

	private Organization organization;

	private String parentName;

	private Integer parentIsSetEncrypt;

	private String parentEncrypt;

	private String parentCreateMember;

	private List<QuestionCount> questionCounts;

	//知识点下试题数目(后评估报表用)
	private Integer questionNum;

	//知识点动态头部名称(后评估报表用)
	private String depotHeaderName;

	/**
	 * 导出时名称
	 */
	private String exportName;

	/**
	 * 导出序号
	 */
	private Integer index;

	private String parentCode;



	public Integer getQuestionNum() {
		return questionNum;
	}

	public void setQuestionNum(Integer questionNum) {
		this.questionNum = questionNum;
	}

	public String getDepotHeaderName() {
		return depotHeaderName;
	}

	public void setDepotHeaderName(String depotHeaderName) {
		this.depotHeaderName = depotHeaderName;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public Integer getParentIsSetEncrypt() {
		return parentIsSetEncrypt;
	}

	public void setParentIsSetEncrypt(Integer parentIsSetEncrypt) {
		this.parentIsSetEncrypt = parentIsSetEncrypt;
	}

	public String getParentEncrypt() {
		return parentEncrypt;
	}

	public void setParentEncrypt(String parentEncrypt) {
		this.parentEncrypt = parentEncrypt;
	}

	public String getParentCreateMember() {
		return parentCreateMember;
	}

	public void setParentCreateMember(String parentCreateMember) {
		this.parentCreateMember = parentCreateMember;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public List<QuestionCount> getQuestionCounts() {
		return questionCounts;
	}

	public void setQuestionCounts(List<QuestionCount> questionCounts) {
		this.questionCounts = questionCounts;
	}

	public String getExportName() {
		return exportName;
	}

	public void setExportName(String exportName) {
		this.exportName = exportName;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getParentCode() {
		return parentCode;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}
}
