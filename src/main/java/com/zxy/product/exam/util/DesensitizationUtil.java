package com.zxy.product.exam.util;

import org.springframework.util.StringUtils;

/**
 * 脱敏
 * <AUTHOR>
 */
public class DesensitizationUtil {

    /**
     * 员工编号脱敏
     * @param employeeId 员工编号
     */
    public static String desensitizeEmployeeId(String employeeId) {
        if (StringUtils.isEmpty(employeeId)){
            return employeeId;
        }
        if (employeeId.length() == 18) {
            return desensitize18DigitId(employeeId);
        } else if (employeeId.length() == 11 && isNumeric(employeeId)) {
            return desensitize11DigitId(employeeId);
        } else {
            return employeeId; // 其他情况不进行脱敏
        }
    }

    private static String desensitize18DigitId(String employeeId) {
        return employeeId.substring(0, 6) + "********" + employeeId.substring(14);
    }

    private static String desensitize11DigitId(String employeeId) {
        return employeeId.substring(0, 3) + "****" + employeeId.substring(7);
    }

    private static boolean isNumeric(String str) {
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }
}
