/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq;

import com.zxy.product.exam.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Exam extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam</code>
     */
    public static final Exam EXAM_SCHEMA = new Exam();

    //todo exam 和 exam-stu 表信息均在此类中按照shema分块进行维护
    //todo 以下是exam schema中的表信息
    /**
     * 历史考试归档表
     */
    public final ArchivedExam ARCHIVED_EXAM = ArchivedExam.ARCHIVED_EXAM;

    /**
     * 子认证-考试组-考试内容关联关系表
     */
    public final SubAuthenticatedExamGroup SUB_AUTHENTICATED_EXAM_GROUP = SubAuthenticatedExamGroup.SUB_AUTHENTICATED_EXAM_GROUP;

    /**
     * 子认证-考试证书取消证书记录表
     */
    public final SubAuthenticatedCancelCertificateRecord SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD = SubAuthenticatedCancelCertificateRecord.SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD;

    /**
     * 活动
     */
    public final Activity ACTIVITY = Activity.ACTIVITY;

    /**
     * 通知公告
     */
    public final Announcement ANNOUNCEMENT = Announcement.ANNOUNCEMENT;

    /**
     * The table <code>exam.t_answer_record_temp</code>.
     */
    public final AnswerRecordTemp ANSWER_RECORD_TEMP = AnswerRecordTemp.ANSWER_RECORD_TEMP;

    /**
     * 受众项
     */
    public final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 资源门户受众对象
     */
    public final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * 调研评估关联表
     */
    public final BusinessResearch BUSINESS_RESEARCH = BusinessResearch.BUSINESS_RESEARCH;

    /**
     * 资源话题表
     */
    public final BusinessTopic BUSINESS_TOPIC = BusinessTopic.BUSINESS_TOPIC;

    /**
     * 证书迁移记录表
     */
    public final CertificateHistory CERTIFICATE_HISTORY = CertificateHistory.CERTIFICATE_HISTORY;

    /**
     * 证书发放记录表
     */
    public final CertificateRecord CERTIFICATE_RECORD = CertificateRecord.CERTIFICATE_RECORD;

    /**
     * 云改专区通知公告
     */
    public final CloudAnnouncement CLOUD_ANNOUNCEMENT = CloudAnnouncement.CLOUD_ANNOUNCEMENT;

    /**
     * 云改专区考试
     */
    public final CloudExam CLOUD_EXAM = CloudExam.CLOUD_EXAM;

    /**
     * 移动云考试等级表
     */
    public final CloudLevel CLOUD_LEVEL = CloudLevel.CLOUD_LEVEL;

    /**
     * 移动云考试专业表
     */
    public final CloudProfession CLOUD_PROFESSION = CloudProfession.CLOUD_PROFESSION;

    /**
     * The table <code>exam.t_dimension</code>.
     */
    public final Dimension DIMENSION = Dimension.DIMENSION;

    /**
     * The table <code>exam.t_dimension_question</code>.
     */
    public final DimensionQuestion DIMENSION_QUESTION = DimensionQuestion.DIMENSION_QUESTION;

    /**
     * 认证考试设备表
     */
    public final EquipmentType EQUIPMENT_TYPE = EquipmentType.EQUIPMENT_TYPE;

    /**
     * 试题报错原因表
     */
    public final ErrorReason ERROR_REASON = ErrorReason.ERROR_REASON;

    /**
     * The table <code>exam.t_exam</code>.
     */
    public final com.zxy.product.exam.jooq.tables.Exam EXAM = com.zxy.product.exam.jooq.tables.Exam.EXAM;

    /**
     * The table <code>exam.t_exam_notice</code>.
     */
    public final ExamNotice EXAM_NOTICE = ExamNotice.EXAM_NOTICE;

    /**
     * 业务关联话题
     */
    public final ExamTopic EXAM_TOPIC = ExamTopic.EXAM_TOPIC;

    /**
     * The table <code>exam.t_grant_detail</code>.
     */
    public final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

    /**
     * 网格长审核管理
     */
    public final GridAudit GRID_AUDIT = GridAudit.GRID_AUDIT;

    /**
     * 网格长认证考试关联的学习
     */
    public final GridCourse GRID_COURSE = GridCourse.GRID_COURSE;

    /**
     * 网格长各级别对应的考试和学习
     */
    public final GridCourseExam GRID_COURSE_EXAM = GridCourseExam.GRID_COURSE_EXAM;

	/**
     * 网格长材料管理
     */
    public final GridFile GRID_FILE = GridFile.GRID_FILE;

	/**
     * 网格长各级别对应的正式考试
     */
    public final GridFormalExam GRID_FORMAL_EXAM = GridFormalExam.GRID_FORMAL_EXAM;

    /**
     * 网格长认证考试等级表
     */
    public final GridLevel GRID_LEVEL = GridLevel.GRID_LEVEL;

    /**
     * 考试监考老师表
     */
    public final Invigilator INVIGILATOR = Invigilator.INVIGILATOR;

    /**
     * 监考范围表
     */
    public final InvigilatorGrant INVIGILATOR_GRANT = InvigilatorGrant.INVIGILATOR_GRANT;

    /**
     * 监考范围详情表
     */
    public final InvigilatorGrantDetail INVIGILATOR_GRANT_DETAIL = InvigilatorGrantDetail.INVIGILATOR_GRANT_DETAIL;

    /**
     * The table <code>exam.t_mark_config</code>.
     */
    public final MarkConfig MARK_CONFIG = MarkConfig.MARK_CONFIG;

    /**
     * The table <code>exam.t_mark_record</code>.
     */
    public final MarkRecord MARK_RECORD = MarkRecord.MARK_RECORD;

    /**
     * The table <code>exam.t_member</code>.
     */
    public final Member MEMBER = Member.MEMBER;

    /**
     * 增量提交答题记录表
     */
    public final ModifyAnswerRecord MODIFY_ANSWER_RECORD = ModifyAnswerRecord.MODIFY_ANSWER_RECORD;

    /**
     * 原试题表中存在易错率的数据
     */
    public final OldQuestion OLD_QUESTION = OldQuestion.OLD_QUESTION;

    /**
     * The table <code>exam.t_organization</code>.
     */
    public final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>exam.t_organization_detail</code>.
     */
    public final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 试卷类
     */
    public final PaperClass PAPER_CLASS = PaperClass.PAPER_CLASS;

    /**
     * 试卷类与试题关联
     */
    public final PaperClassQuestion PAPER_CLASS_QUESTION = PaperClassQuestion.PAPER_CLASS_QUESTION;

    /**
     * 组卷策略
     */
    public final PaperClassTactic PAPER_CLASS_TACTIC = PaperClassTactic.PAPER_CLASS_TACTIC;

    /**
     * 试卷实例
     */
    public final PaperInstance PAPER_INSTANCE = PaperInstance.PAPER_INSTANCE;

    /**
     * 试卷实例与试题副本关联
     */
    public final PaperInstanceQuestionCopy PAPER_INSTANCE_QUESTION_COPY = PaperInstanceQuestionCopy.PAPER_INSTANCE_QUESTION_COPY;

    /**
     * 试卷实例与试题副本关联_2017
     */
    public final PaperInstanceQuestionCopy_2017 PAPER_INSTANCE_QUESTION_COPY_2017 = PaperInstanceQuestionCopy_2017.PAPER_INSTANCE_QUESTION_COPY_2017;

    /**
     * 试卷实例与试题副本关联_2018
     */
    public final PaperInstanceQuestionCopy_2018 PAPER_INSTANCE_QUESTION_COPY_2018 = PaperInstanceQuestionCopy_2018.PAPER_INSTANCE_QUESTION_COPY_2018;

    /**
     * 试卷实例与试题副本关联_2019
     */
    public final PaperInstanceQuestionCopy_2019 PAPER_INSTANCE_QUESTION_COPY_2019 = PaperInstanceQuestionCopy_2019.PAPER_INSTANCE_QUESTION_COPY_2019;

    /**
     * 试卷实例与试题副本关联_2020
     */
    public final PaperInstanceQuestionCopy_2020 PAPER_INSTANCE_QUESTION_COPY_2020 = PaperInstanceQuestionCopy_2020.PAPER_INSTANCE_QUESTION_COPY_2020;

    /**
     * 试卷实例与试题副本关联_2021
     */
    public final PaperInstanceQuestionCopy_2021 PAPER_INSTANCE_QUESTION_COPY_2021 = PaperInstanceQuestionCopy_2021.PAPER_INSTANCE_QUESTION_COPY_2021;

    /**
     * 试卷实例与试题副本关联_2022
     */
    public final PaperInstanceQuestionCopy_2022 PAPER_INSTANCE_QUESTION_COPY_2022 = PaperInstanceQuestionCopy_2022.PAPER_INSTANCE_QUESTION_COPY_2022;

    /**
     * 试卷实例与试题副本关联_2023
     */
    public final PaperInstanceQuestionCopy_2023 PAPER_INSTANCE_QUESTION_COPY_2023 = PaperInstanceQuestionCopy_2023.PAPER_INSTANCE_QUESTION_COPY_2023;

    /**
     * 试卷实例与试题副本关联_2024
     */
    public final PaperInstanceQuestionCopy_2024 PAPER_INSTANCE_QUESTION_COPY_2024 = PaperInstanceQuestionCopy_2024.PAPER_INSTANCE_QUESTION_COPY_2024;

    /**
     * 试卷实例与试题副本关联_2025
     */
    public final PaperInstanceQuestionCopy_2025 PAPER_INSTANCE_QUESTION_COPY_2025 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2025.PAPER_INSTANCE_QUESTION_COPY_2025;

    /**
     * 试卷实例与试题副本关联_2026
     */
    public final PaperInstanceQuestionCopy_2026 PAPER_INSTANCE_QUESTION_COPY_2026 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2026.PAPER_INSTANCE_QUESTION_COPY_2026;

    /**
     * 试卷实例与试题副本关联_2027
     */
    public final PaperInstanceQuestionCopy_2027 PAPER_INSTANCE_QUESTION_COPY_2027 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2027.PAPER_INSTANCE_QUESTION_COPY_2027;

    /**
     * 试卷实例与试题副本关联_2028
     */
    public final PaperInstanceQuestionCopy_2028 PAPER_INSTANCE_QUESTION_COPY_2028 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2028.PAPER_INSTANCE_QUESTION_COPY_2028;

    /**
     * 试卷实例与试题副本关联_2029
     */
    public final PaperInstanceQuestionCopy_2029 PAPER_INSTANCE_QUESTION_COPY_2029 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2029.PAPER_INSTANCE_QUESTION_COPY_2029;

    /**
     * 试卷实例与试题副本关联_2030
     */
    public final PaperInstanceQuestionCopy_2030 PAPER_INSTANCE_QUESTION_COPY_2030 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2030.PAPER_INSTANCE_QUESTION_COPY_2030;

    /**
     * PCCW HR接口调用结果
     */
    public final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;


	/**
     * 个性化学习专区对应试题目录
     */
    public final PersonalDepot PERSONAL_DEPOT = PersonalDepot.PERSONAL_DEPOT;

    /**
     * 认证考试专业表
     */
    public final Profession PROFESSION = Profession.PROFESSION;

    /**
     * 等级表，不再与子专业关联
     */
    public final ProfessionLevel PROFESSION_LEVEL = ProfessionLevel.PROFESSION_LEVEL;

    /**
     * The table <code>exam.t_question</code>.
     */
    public final Question QUESTION = Question.QUESTION;

    /**
     * The table <code>exam.t_question_attr</code>.
     */
    public final QuestionAttr QUESTION_ATTR = QuestionAttr.QUESTION_ATTR;

    /**
     * The table <code>exam.t_question_attr_copy</code>.
     */
    public final QuestionAttrCopy QUESTION_ATTR_COPY = QuestionAttrCopy.QUESTION_ATTR_COPY;

    /**
     * 试题副本
     */
    public final QuestionCopy QUESTION_COPY = QuestionCopy.QUESTION_COPY;

    /**
     * The table <code>exam.t_question_count</code>.
     */
    public final QuestionCount QUESTION_COUNT = QuestionCount.QUESTION_COUNT;

    /**
     * The table <code>exam.t_question_depot</code>.
     */
    public final QuestionDepot QUESTION_DEPOT = QuestionDepot.QUESTION_DEPOT;

    /**
     * 试题易错率统计
     */
    public final QuestionErrorRate QUESTION_ERROR_RATE = QuestionErrorRate.QUESTION_ERROR_RATE;

    /**
     * The table <code>exam.t_question_recovery</code>.
     */
    public final QuestionRecovery QUESTION_RECOVERY = QuestionRecovery.QUESTION_RECOVERY;

    /**
     * The table <code>exam.t_research_answer_record</code>.
     */
    public final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = ResearchAnswerRecord.RESEARCH_ANSWER_RECORD;

    /**
     * The table <code>exam.t_research_history_result</code>.
     */
    public final ResearchHistoryResult RESEARCH_HISTORY_RESULT = ResearchHistoryResult.RESEARCH_HISTORY_RESULT;

    /**
     * The table <code>exam.t_research_questionary</code>.
     */
    public final ResearchQuestionary RESEARCH_QUESTIONARY = ResearchQuestionary.RESEARCH_QUESTIONARY;

    /**
     * The table <code>exam.t_research_record</code>.
     */
    public final ResearchRecord RESEARCH_RECORD = ResearchRecord.RESEARCH_RECORD;

    /**
     * The table <code>exam.t_research_summary</code>.
     */
    public final ResearchSummary RESEARCH_SUMMARY = ResearchSummary.RESEARCH_SUMMARY;

    /**
     * t_sync_info同步试卷临时信息存储
     */
    public final SyncInfo SYNC_INFO = SyncInfo.SYNC_INFO;

    /**
     * 今日答题表
     */
    public final TodayQuestion TODAY_QUESTION = TodayQuestion.TODAY_QUESTION;

    /**
     * 考试调研-任务关联表（学习计划）
     */
    public final ExamStudyPlanConfig EXAM_STUDY_PLAN_CONFIG = ExamStudyPlanConfig.EXAM_STUDY_PLAN_CONFIG;

    /**
     * The table <code>exam.t_delete_data_exam</code>.
     */
    public final DeleteDataExam DELETE_DATA_EXAM = DeleteDataExam.DELETE_DATA_EXAM;

    /**
     * 学习风格测评表
     */
    public final LearningStyleEvaluation LEARNING_STYLE_EVALUATION = LearningStyleEvaluation.LEARNING_STYLE_EVALUATION;

    /**
     * 考试试卷附件表
     */
    public final ExamPaperAttachment EXAM_PAPER_ATTACHMENT = ExamPaperAttachment.EXAM_PAPER_ATTACHMENT;

    /**
     * 考试关联课程
     */
    public final RelevanceCourseExam RELEVANCE_COURSE_EXAM = RelevanceCourseExam.RELEVANCE_COURSE_EXAM;



    //todo 以下是exam-stu schema中的表信息

    public final AnswerRecordProcess ANSWER_RECORD_PROCESS = AnswerRecordProcess.ANSWER_RECORD_PROCESS;

    public final AnswerRecordProcess_0 ANSWER_RECORD_PROCESS_0 = AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0;

    public final AnswerRecordProcess_1 ANSWER_RECORD_PROCESS_1 = AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1;

    public final AnswerRecordProcess_2 ANSWER_RECORD_PROCESS_2 = AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2;

    public final AnswerRecordProcess_3 ANSWER_RECORD_PROCESS_3 = AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3;

    public final AnswerRecordProcess_4 ANSWER_RECORD_PROCESS_4 = AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4;

    public final AnswerRecordProcess_5 ANSWER_RECORD_PROCESS_5 = AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5;

    public final AnswerRecordProcess_6 ANSWER_RECORD_PROCESS_6 = AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6;

    public final AnswerRecordProcess_7 ANSWER_RECORD_PROCESS_7 = AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7;

    public final AnswerRecordProcess_8 ANSWER_RECORD_PROCESS_8 = AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8;

    public final AnswerRecordProcess_9 ANSWER_RECORD_PROCESS_9 = AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9;
    /**
     * The table <code>exam.t_answer_record</code>.
     */
    public final AnswerRecord ANSWER_RECORD = AnswerRecord.ANSWER_RECORD;

    /**
     * 答案记录表_2019
     */
    public final AnswerRecord_2019 ANSWER_RECORD_2019 = AnswerRecord_2019.ANSWER_RECORD_2019;

    /**
     * 答案记录表_2020
     */
    public final AnswerRecord_2020 ANSWER_RECORD_2020 = AnswerRecord_2020.ANSWER_RECORD_2020;

    /**
     * 答案记录表_2021
     */
    public final AnswerRecord_2021 ANSWER_RECORD_2021 = AnswerRecord_2021.ANSWER_RECORD_2021;

    /**
     * 答案记录表_2022
     */
    public final AnswerRecord_2022 ANSWER_RECORD_2022 = AnswerRecord_2022.ANSWER_RECORD_2022;

    /**
     * 答案记录表_2023
     */
    public final AnswerRecord_2023 ANSWER_RECORD_2023 = AnswerRecord_2023.ANSWER_RECORD_2023;

    /**
     * 答案记录表_2024
     */
    public final AnswerRecord_2024 ANSWER_RECORD_2024 = AnswerRecord_2024.ANSWER_RECORD_2024;

    /**
     * 答案记录表_2025
     */
    public final AnswerRecord_2025 ANSWER_RECORD_2025 = com.zxy.product.exam.jooq.tables.AnswerRecord_2025.ANSWER_RECORD_2025;

    /**
     * 答案记录表_2026
     */
    public final AnswerRecord_2026 ANSWER_RECORD_2026 = com.zxy.product.exam.jooq.tables.AnswerRecord_2026.ANSWER_RECORD_2026;

    /**
     * 答案记录表_2027
     */
    public final AnswerRecord_2027 ANSWER_RECORD_2027 = com.zxy.product.exam.jooq.tables.AnswerRecord_2027.ANSWER_RECORD_2027;

    /**
     * 答案记录表_2028
     */
    public final AnswerRecord_2028 ANSWER_RECORD_2028 = com.zxy.product.exam.jooq.tables.AnswerRecord_2028.ANSWER_RECORD_2028;

    /**
     * 答案记录表_2029
     */
    public final AnswerRecord_2029 ANSWER_RECORD_2029 = com.zxy.product.exam.jooq.tables.AnswerRecord_2029.ANSWER_RECORD_2029;

    /**
     * 答案记录表_2030
     */
    public final AnswerRecord_2030 ANSWER_RECORD_2030 = com.zxy.product.exam.jooq.tables.AnswerRecord_2030.ANSWER_RECORD_2030;

    /**
     * The table <code>exam.t_exam_record</code>.
     */
    public final ExamRecord EXAM_RECORD = ExamRecord.EXAM_RECORD;

    /**
     * 考试记录表_2017
     */
    public final ExamRecord_2017 EXAM_RECORD_2017 = ExamRecord_2017.EXAM_RECORD_2017;

    /**
     * 考试记录表_2018
     */
    public final ExamRecord_2018 EXAM_RECORD_2018 = ExamRecord_2018.EXAM_RECORD_2018;

    /**
     * 考试记录表_2019
     */
    public final ExamRecord_2019 EXAM_RECORD_2019 = ExamRecord_2019.EXAM_RECORD_2019;

    /**
     * 考试记录表_2020
     */
    public final ExamRecord_2020 EXAM_RECORD_2020 = ExamRecord_2020.EXAM_RECORD_2020;

    /**
     * 考试记录表_2021
     */
    public final ExamRecord_2021 EXAM_RECORD_2021 = ExamRecord_2021.EXAM_RECORD_2021;

    /**
     * 考试记录表_2022
     */
    public final ExamRecord_2022 EXAM_RECORD_2022 = ExamRecord_2022.EXAM_RECORD_2022;

    /**
     * 考试记录表_2023
     */
    public final ExamRecord_2023 EXAM_RECORD_2023 = ExamRecord_2023.EXAM_RECORD_2023;

    /**
     * 考试记录表_2024
     */
    public final ExamRecord_2024 EXAM_RECORD_2024 = ExamRecord_2024.EXAM_RECORD_2024;

    /**
     * 考试记录表_2025
     */
    public final ExamRecord_2025 EXAM_RECORD_2025 = com.zxy.product.exam.jooq.tables.ExamRecord_2025.EXAM_RECORD_2025;

    /**
     * 考试记录表_2026
     */
    public final ExamRecord_2026 EXAM_RECORD_2026 = com.zxy.product.exam.jooq.tables.ExamRecord_2026.EXAM_RECORD_2026;

    /**
     * 考试记录表_2027
     */
    public final ExamRecord_2027 EXAM_RECORD_2027 = com.zxy.product.exam.jooq.tables.ExamRecord_2027.EXAM_RECORD_2027;

    /**
     * 考试记录表_2028
     */
    public final ExamRecord_2028 EXAM_RECORD_2028 = com.zxy.product.exam.jooq.tables.ExamRecord_2028.EXAM_RECORD_2028;

    /**
     * 考试记录表_2029
     */
    public final ExamRecord_2029 EXAM_RECORD_2029 = com.zxy.product.exam.jooq.tables.ExamRecord_2029.EXAM_RECORD_2029;

    /**
     * 考试记录表_2030
     */
    public final ExamRecord_2030 EXAM_RECORD_2030 = com.zxy.product.exam.jooq.tables.ExamRecord_2030.EXAM_RECORD_2030;


    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace EXAM_RECORD_FACE = ExamRecordFace.EXAM_RECORD_FACE;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2017 EXAM_RECORD_FACE_2017 = ExamRecordFace_2017.EXAM_RECORD_FACE_2017;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2018 EXAM_RECORD_FACE_2018 = ExamRecordFace_2018.EXAM_RECORD_FACE_2018;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2019 EXAM_RECORD_FACE_2019 = ExamRecordFace_2019.EXAM_RECORD_FACE_2019;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2020 EXAM_RECORD_FACE_2020 = ExamRecordFace_2020.EXAM_RECORD_FACE_2020;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2021 EXAM_RECORD_FACE_2021 = ExamRecordFace_2021.EXAM_RECORD_FACE_2021;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2022 EXAM_RECORD_FACE_2022 = ExamRecordFace_2022.EXAM_RECORD_FACE_2022;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2023 EXAM_RECORD_FACE_2023 = ExamRecordFace_2023.EXAM_RECORD_FACE_2023;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2024 EXAM_RECORD_FACE_2024 = ExamRecordFace_2024.EXAM_RECORD_FACE_2024;


    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2025 EXAM_RECORD_FACE_2025 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2025.EXAM_RECORD_FACE_2025;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2026 EXAM_RECORD_FACE_2026 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2026.EXAM_RECORD_FACE_2026;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2027 EXAM_RECORD_FACE_2027 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2027.EXAM_RECORD_FACE_2027;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2028 EXAM_RECORD_FACE_2028 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2028.EXAM_RECORD_FACE_2028;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2029 EXAM_RECORD_FACE_2029 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2029.EXAM_RECORD_FACE_2029;

    /**
     * 人脸监考记录表
     */
    public final ExamRecordFace_2030 EXAM_RECORD_FACE_2030 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2030.EXAM_RECORD_FACE_2030;

    /**
     * 考试注册表
     */
    public final ExamRegist EXAM_REGIST = ExamRegist.EXAM_REGIST;

    /**
     * 考试注册表_2017
     */
    public final ExamRegist_2017 EXAM_REGIST_2017 = ExamRegist_2017.EXAM_REGIST_2017;

    /**
     * 考试注册表_2018
     */
    public final ExamRegist_2018 EXAM_REGIST_2018 = ExamRegist_2018.EXAM_REGIST_2018;

    /**
     * 考试注册表_2019
     */
    public final ExamRegist_2019 EXAM_REGIST_2019 = ExamRegist_2019.EXAM_REGIST_2019;

    /**
     * 考试注册表_2020
     */
    public final ExamRegist_2020 EXAM_REGIST_2020 = ExamRegist_2020.EXAM_REGIST_2020;

    /**
     * 考试注册表_2021
     */
    public final ExamRegist_2021 EXAM_REGIST_2021 = ExamRegist_2021.EXAM_REGIST_2021;

    /**
     * 考试注册表_2022
     */
    public final ExamRegist_2022 EXAM_REGIST_2022 = ExamRegist_2022.EXAM_REGIST_2022;

    /**
     * 考试注册表_2023
     */
    public final ExamRegist_2023 EXAM_REGIST_2023 = ExamRegist_2023.EXAM_REGIST_2023;

    /**
     * 考试注册表_2024
     */
    public final ExamRegist_2024 EXAM_REGIST_2024 = ExamRegist_2024.EXAM_REGIST_2024;


    /**
     * 考试注册表_2025
     */
    public final ExamRegist_2025 EXAM_REGIST_2025 = com.zxy.product.exam.jooq.tables.ExamRegist_2025.EXAM_REGIST_2025;

    /**
     * 考试注册表_2026
     */
    public final ExamRegist_2026 EXAM_REGIST_2026 = com.zxy.product.exam.jooq.tables.ExamRegist_2026.EXAM_REGIST_2026;

    /**
     * 考试注册表_2027
     */
    public final ExamRegist_2027 EXAM_REGIST_2027 = com.zxy.product.exam.jooq.tables.ExamRegist_2027.EXAM_REGIST_2027;

    /**
     * 考试注册表_2028
     */
    public final ExamRegist_2028 EXAM_REGIST_2028 = com.zxy.product.exam.jooq.tables.ExamRegist_2028.EXAM_REGIST_2028;

    /**
     * 考试注册表_2029
     */
    public final ExamRegist_2029 EXAM_REGIST_2029 = com.zxy.product.exam.jooq.tables.ExamRegist_2029.EXAM_REGIST_2029;

    /**
     * 考试注册表_2030
     */
    public final ExamRegist_2030 EXAM_REGIST_2030 = com.zxy.product.exam.jooq.tables.ExamRegist_2030.EXAM_REGIST_2030;

    /**
     * 个人信息信息模板表
     */
    public final PersonalTemplate PERSONAL_TEMPLATE = PersonalTemplate.PERSONAL_TEMPLATE;

    /**
     * The table <code>exam.t_signup</code>.
     */
    public final Signup SIGNUP = Signup.SIGNUP;

    /**
     * 考试报名记录表
     */
    public final SignupRecord SIGNUP_RECORD = SignupRecord.SIGNUP_RECORD;

    /**
     * 报名认证信息表
     */
    public final SignUpAuth SIGN_UP_AUTH = SignUpAuth.SIGN_UP_AUTH;

    /**
     * 移动云考试报名表
     */
    public final CloudSignup CLOUD_SIGNUP = CloudSignup.CLOUD_SIGNUP;

    /**
     * 网格长考试报名表
     */
    public final GridSignup GRID_SIGNUP = GridSignup.GRID_SIGNUP;

    /**
     * The table <code>exam.t_to_do</code>.
     */
    public final ToDo TO_DO = ToDo.TO_DO;

    /**
     * The table <code>exam-stu.t_exam_online_log</code>.
     */
    public final ExamOnlineLog EXAM_ONLINE_LOG = com.zxy.product.exam.jooq.tables.ExamOnlineLog.EXAM_ONLINE_LOG;

    /**
     * 数智测评结果表
     */
    public final DigitalIntelligenceResult DIGITAL_INTELLIGENCE_RESULT = com.zxy.product.exam.jooq.tables.DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT;



    /**
     * No further instances allowed
     */
    private Exam() {
        super("exam", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
                //todo exam 和 exam-stu 表信息均在此类中按照shema分块进行维护
                //todo 以下是exam schema中的表信息
                RelevanceCourseExam.RELEVANCE_COURSE_EXAM,
                Activity.ACTIVITY,
                ArchivedExam.ARCHIVED_EXAM,
                SubAuthenticatedExamGroup.SUB_AUTHENTICATED_EXAM_GROUP,
                SubAuthenticatedCancelCertificateRecord.SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD,
                Announcement.ANNOUNCEMENT,
                AnswerRecordTemp.ANSWER_RECORD_TEMP,
                AudienceItem.AUDIENCE_ITEM,
                AudienceMember.AUDIENCE_MEMBER,
                AudienceObject.AUDIENCE_OBJECT,
                BusinessResearch.BUSINESS_RESEARCH,
                BusinessTopic.BUSINESS_TOPIC,
                CertificateHistory.CERTIFICATE_HISTORY,
                CertificateRecord.CERTIFICATE_RECORD,
                CloudAnnouncement.CLOUD_ANNOUNCEMENT,
                CloudExam.CLOUD_EXAM,
                CloudLevel.CLOUD_LEVEL,
                CloudProfession.CLOUD_PROFESSION,
                Dimension.DIMENSION,
                DimensionQuestion.DIMENSION_QUESTION,
                EquipmentType.EQUIPMENT_TYPE,
                ErrorReason.ERROR_REASON,
                com.zxy.product.exam.jooq.tables.Exam.EXAM,
                ExamNotice.EXAM_NOTICE,
                ExamTopic.EXAM_TOPIC,
                GrantDetail.GRANT_DETAIL,
                GridAudit.GRID_AUDIT,
                GridCourse.GRID_COURSE,
                GridCourseExam.GRID_COURSE_EXAM,
                GridFile.GRID_FILE,
                GridFormalExam.GRID_FORMAL_EXAM,
                GridLevel.GRID_LEVEL,
                Invigilator.INVIGILATOR,
                InvigilatorGrant.INVIGILATOR_GRANT,
                InvigilatorGrantDetail.INVIGILATOR_GRANT_DETAIL,
                MarkConfig.MARK_CONFIG,
                MarkRecord.MARK_RECORD,
                Member.MEMBER,
                ModifyAnswerRecord.MODIFY_ANSWER_RECORD,
                OldQuestion.OLD_QUESTION,
                Organization.ORGANIZATION,
                OrganizationDetail.ORGANIZATION_DETAIL,
                PaperClass.PAPER_CLASS,
                PaperClassQuestion.PAPER_CLASS_QUESTION,
                PaperClassTactic.PAPER_CLASS_TACTIC,
                PaperInstance.PAPER_INSTANCE,
                PaperInstanceQuestionCopy.PAPER_INSTANCE_QUESTION_COPY,
                PaperInstanceQuestionCopy_2017.PAPER_INSTANCE_QUESTION_COPY_2017,
                PaperInstanceQuestionCopy_2018.PAPER_INSTANCE_QUESTION_COPY_2018,
                PaperInstanceQuestionCopy_2019.PAPER_INSTANCE_QUESTION_COPY_2019,
                PaperInstanceQuestionCopy_2020.PAPER_INSTANCE_QUESTION_COPY_2020,
                PaperInstanceQuestionCopy_2021.PAPER_INSTANCE_QUESTION_COPY_2021,
                PaperInstanceQuestionCopy_2022.PAPER_INSTANCE_QUESTION_COPY_2022,
                PaperInstanceQuestionCopy_2023.PAPER_INSTANCE_QUESTION_COPY_2023,
                PaperInstanceQuestionCopy_2024.PAPER_INSTANCE_QUESTION_COPY_2024,
                PaperInstanceQuestionCopy_2025.PAPER_INSTANCE_QUESTION_COPY_2025,
                PaperInstanceQuestionCopy_2026.PAPER_INSTANCE_QUESTION_COPY_2026,
                PaperInstanceQuestionCopy_2027.PAPER_INSTANCE_QUESTION_COPY_2027,
                PaperInstanceQuestionCopy_2028.PAPER_INSTANCE_QUESTION_COPY_2028,
                PaperInstanceQuestionCopy_2029.PAPER_INSTANCE_QUESTION_COPY_2029,
                PaperInstanceQuestionCopy_2030.PAPER_INSTANCE_QUESTION_COPY_2030,
                PccwResult.PCCW_RESULT,
                PersonalDepot.PERSONAL_DEPOT,
                Profession.PROFESSION,
                ProfessionLevel.PROFESSION_LEVEL,
                Question.QUESTION,
                QuestionAttr.QUESTION_ATTR,
                QuestionAttrCopy.QUESTION_ATTR_COPY,
                QuestionCopy.QUESTION_COPY,
                QuestionCount.QUESTION_COUNT,
                QuestionDepot.QUESTION_DEPOT,
                QuestionErrorRate.QUESTION_ERROR_RATE,
                QuestionRecovery.QUESTION_RECOVERY,
                ResearchAnswerRecord.RESEARCH_ANSWER_RECORD,
                ResearchHistoryResult.RESEARCH_HISTORY_RESULT,
                ResearchQuestionary.RESEARCH_QUESTIONARY,
                ResearchRecord.RESEARCH_RECORD,
                ResearchSummary.RESEARCH_SUMMARY,
                SyncInfo.SYNC_INFO,
                TodayQuestion.TODAY_QUESTION,
                ExamStudyPlanConfig.EXAM_STUDY_PLAN_CONFIG,
                DeleteDataExam.DELETE_DATA_EXAM,
                LearningStyleEvaluation.LEARNING_STYLE_EVALUATION,
                ExamPaperAttachment.EXAM_PAPER_ATTACHMENT,

                //todo 以下是exam-stu schema中的表信息
                ExamRecord.EXAM_RECORD,
                ExamRecord_2017.EXAM_RECORD_2017,
                ExamRecord_2018.EXAM_RECORD_2018,
                ExamRecord_2019.EXAM_RECORD_2019,
                ExamRecord_2020.EXAM_RECORD_2020,
                ExamRecord_2021.EXAM_RECORD_2021,
                ExamRecord_2022.EXAM_RECORD_2022,
                ExamRecord_2023.EXAM_RECORD_2023,
                ExamRecord_2024.EXAM_RECORD_2024,
                ExamRecord_2025.EXAM_RECORD_2025,
                ExamRecord_2026.EXAM_RECORD_2026,
                ExamRecord_2027.EXAM_RECORD_2027,
                ExamRecord_2028.EXAM_RECORD_2028,
                ExamRecord_2029.EXAM_RECORD_2029,
                ExamRecord_2030.EXAM_RECORD_2030,
                AnswerRecord.ANSWER_RECORD,
                AnswerRecord_2019.ANSWER_RECORD_2019,
                AnswerRecord_2020.ANSWER_RECORD_2020,
                AnswerRecord_2021.ANSWER_RECORD_2021,
                AnswerRecord_2022.ANSWER_RECORD_2022,
                AnswerRecord_2023.ANSWER_RECORD_2023,
                AnswerRecord_2024.ANSWER_RECORD_2024,
                AnswerRecord_2025.ANSWER_RECORD_2025,
                AnswerRecord_2026.ANSWER_RECORD_2026,
                AnswerRecord_2027.ANSWER_RECORD_2027,
                AnswerRecord_2028.ANSWER_RECORD_2028,
                AnswerRecord_2029.ANSWER_RECORD_2029,
                AnswerRecord_2030.ANSWER_RECORD_2030,
                ExamRecordFace.EXAM_RECORD_FACE,
                ExamRecordFace_2017.EXAM_RECORD_FACE_2017,
                ExamRecordFace_2018.EXAM_RECORD_FACE_2018,
                ExamRecordFace_2019.EXAM_RECORD_FACE_2019,
                ExamRecordFace_2020.EXAM_RECORD_FACE_2020,
                ExamRecordFace_2021.EXAM_RECORD_FACE_2021,
                ExamRecordFace_2022.EXAM_RECORD_FACE_2022,
                ExamRecordFace_2023.EXAM_RECORD_FACE_2023,
                ExamRecordFace_2024.EXAM_RECORD_FACE_2024,
                ExamRecordFace_2025.EXAM_RECORD_FACE_2025,
                ExamRecordFace_2026.EXAM_RECORD_FACE_2026,
                ExamRecordFace_2027.EXAM_RECORD_FACE_2027,
                ExamRecordFace_2028.EXAM_RECORD_FACE_2028,
                ExamRecordFace_2029.EXAM_RECORD_FACE_2029,
                ExamRecordFace_2030.EXAM_RECORD_FACE_2030,
                ExamRegist.EXAM_REGIST,
                ExamRegist_2017.EXAM_REGIST_2017,
                ExamRegist_2018.EXAM_REGIST_2018,
                ExamRegist_2019.EXAM_REGIST_2019,
                ExamRegist_2020.EXAM_REGIST_2020,
                ExamRegist_2021.EXAM_REGIST_2021,
                ExamRegist_2022.EXAM_REGIST_2022,
                ExamRegist_2023.EXAM_REGIST_2023,
                ExamRegist_2024.EXAM_REGIST_2024,
                ExamRegist_2025.EXAM_REGIST_2025,
                ExamRegist_2026.EXAM_REGIST_2026,
                ExamRegist_2027.EXAM_REGIST_2027,
                ExamRegist_2028.EXAM_REGIST_2028,
                ExamRegist_2029.EXAM_REGIST_2029,
                ExamRegist_2030.EXAM_REGIST_2030,
                AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0,
                AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1,
                AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2,
                AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3,
                AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4,
                AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5,
                AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6,
                AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7,
                AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8,
                AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9,
                AnswerRecordProcess.ANSWER_RECORD_PROCESS,
                PersonalTemplate.PERSONAL_TEMPLATE,
                Signup.SIGNUP,
                SignupRecord.SIGNUP_RECORD,
                SignUpAuth.SIGN_UP_AUTH,
                CloudSignup.CLOUD_SIGNUP,
                GridSignup.GRID_SIGNUP,
                ToDo.TO_DO,
                ExamOnlineLog.EXAM_ONLINE_LOG,
                DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT
                );
    }
}
