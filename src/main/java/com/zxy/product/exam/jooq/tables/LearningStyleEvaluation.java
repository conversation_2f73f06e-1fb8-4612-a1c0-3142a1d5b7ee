/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.LearningStyleEvaluationRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习风格测评表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LearningStyleEvaluation extends TableImpl<LearningStyleEvaluationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_learning_style_evaluation</code>
     */
    public static final LearningStyleEvaluation LEARNING_STYLE_EVALUATION = new LearningStyleEvaluation();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LearningStyleEvaluationRecord> getRecordType() {
        return LearningStyleEvaluationRecord.class;
    }

    /**
     * The column <code>exam.t_learning_style_evaluation.f_id</code>. 主键
     */
    public final TableField<LearningStyleEvaluationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_member_id</code>. 人员id
     */
    public final TableField<LearningStyleEvaluationRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员id");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_hearing</code>. 听觉型
     */
    public final TableField<LearningStyleEvaluationRecord, String> HEARING = createField("f_hearing", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "听觉型");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_viewing</code>. 视觉型
     */
    public final TableField<LearningStyleEvaluationRecord, String> VIEWING = createField("f_viewing", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "视觉型");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_rw</code>. 读写型
     */
    public final TableField<LearningStyleEvaluationRecord, String> RW = createField("f_rw", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "读写型");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_action</code>. 动作型
     */
    public final TableField<LearningStyleEvaluationRecord, String> ACTION = createField("f_action", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "动作型");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_result</code>. 测评结果(视觉型4,听觉型3,读写型2,动觉型1)
     */
    public final TableField<LearningStyleEvaluationRecord, Integer> RESULT = createField("f_result", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "测评结果(视觉型4,听觉型3,读写型2,动觉型1)");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_create_time</code>. 创建时间
     */
    public final TableField<LearningStyleEvaluationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_learning_style_evaluation.f_modify_date</code>. 修改时间
     */
    public final TableField<LearningStyleEvaluationRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_learning_style_evaluation</code> table reference
     */
    public LearningStyleEvaluation() {
        this("t_learning_style_evaluation", null);
    }

    /**
     * Create an aliased <code>exam.t_learning_style_evaluation</code> table reference
     */
    public LearningStyleEvaluation(String alias) {
        this(alias, LEARNING_STYLE_EVALUATION);
    }

    private LearningStyleEvaluation(String alias, Table<LearningStyleEvaluationRecord> aliased) {
        this(alias, aliased, null);
    }

    private LearningStyleEvaluation(String alias, Table<LearningStyleEvaluationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习风格测评表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LearningStyleEvaluationRecord> getPrimaryKey() {
        return Keys.KEY_T_LEARNING_STYLE_EVALUATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LearningStyleEvaluationRecord>> getKeys() {
        return Arrays.<UniqueKey<LearningStyleEvaluationRecord>>asList(Keys.KEY_T_LEARNING_STYLE_EVALUATION_PRIMARY, Keys.KEY_T_LEARNING_STYLE_EVALUATION_IDX_MEMBER_ID);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LearningStyleEvaluation as(String alias) {
        return new LearningStyleEvaluation(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LearningStyleEvaluation rename(String name) {
        return new LearningStyleEvaluation(name, null);
    }
}
