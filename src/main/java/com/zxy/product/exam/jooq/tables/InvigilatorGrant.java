/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.InvigilatorGrantRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 监考范围表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InvigilatorGrant extends TableImpl<InvigilatorGrantRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_invigilator_grant</code>
     */
    public static final InvigilatorGrant INVIGILATOR_GRANT = new InvigilatorGrant();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InvigilatorGrantRecord> getRecordType() {
        return InvigilatorGrantRecord.class;
    }

    /**
     * The column <code>exam.t_invigilator_grant.f_id</code>. ID
     */
    public final TableField<InvigilatorGrantRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_invigilator_grant.f_invigilator_id</code>. 监考老师表记录id
     */
    public final TableField<InvigilatorGrantRecord, String> INVIGILATOR_ID = createField("f_invigilator_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "监考老师表记录id");

    /**
     * The column <code>exam.t_invigilator_grant.f_exam_id</code>. 考试id
     */
    public final TableField<InvigilatorGrantRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_invigilator_grant.f_member_id</code>. 用户id
     */
    public final TableField<InvigilatorGrantRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_invigilator_grant.f_organization_id</code>. 组织id
     */
    public final TableField<InvigilatorGrantRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织id");

    /**
     * The column <code>exam.t_invigilator_grant.f_type</code>. 是否包含子部门，0否 1是
     */
    public final TableField<InvigilatorGrantRecord, Integer> TYPE = createField("f_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否包含子部门，0否 1是");

    /**
     * The column <code>exam.t_invigilator_grant.f_create_time</code>. 创建时间
     */
    public final TableField<InvigilatorGrantRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>exam.t_invigilator_grant</code> table reference
     */
    public InvigilatorGrant() {
        this("t_invigilator_grant", null);
    }

    /**
     * Create an aliased <code>exam.t_invigilator_grant</code> table reference
     */
    public InvigilatorGrant(String alias) {
        this(alias, INVIGILATOR_GRANT);
    }

    private InvigilatorGrant(String alias, Table<InvigilatorGrantRecord> aliased) {
        this(alias, aliased, null);
    }

    private InvigilatorGrant(String alias, Table<InvigilatorGrantRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "监考范围表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<InvigilatorGrantRecord> getPrimaryKey() {
        return Keys.KEY_T_INVIGILATOR_GRANT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<InvigilatorGrantRecord>> getKeys() {
        return Arrays.<UniqueKey<InvigilatorGrantRecord>>asList(Keys.KEY_T_INVIGILATOR_GRANT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InvigilatorGrant as(String alias) {
        return new InvigilatorGrant(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InvigilatorGrant rename(String name) {
        return new InvigilatorGrant(name, null);
    }
}
