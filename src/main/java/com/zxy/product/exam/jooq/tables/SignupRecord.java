/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SignupRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 考试报名记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SignupRecord extends TableImpl<SignupRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_signup_record</code>
     */
    public static final SignupRecord SIGNUP_RECORD = new SignupRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignupRecordRecord> getRecordType() {
        return SignupRecordRecord.class;
    }

    /**
     * The column <code>exam-stu.t_signup_record.f_id</code>. ID
     */
    public final TableField<SignupRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam-stu.t_signup_record.f_exam_id</code>. 考试id
     */
    public final TableField<SignupRecordRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam-stu.t_signup_record.f_member_id</code>. 用户id
     */
    public final TableField<SignupRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam-stu.t_signup_record.f_audit_status</code>. 审核状态，0待审核 1审核通过 2已拒绝
     */
    public final TableField<SignupRecordRecord, Integer> AUDIT_STATUS = createField("f_audit_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "审核状态，0待审核 1审核通过 2已拒绝");

    /**
     * The column <code>exam-stu.t_signup_record.f_audit_member_id</code>. 审核人，用户id、系统自动审核
     */
    public final TableField<SignupRecordRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "审核人，用户id、系统自动审核");

    /**
     * The column <code>exam-stu.t_signup_record.f_audit_time</code>. 审核时间
     */
    public final TableField<SignupRecordRecord, Long> AUDIT_TIME = createField("f_audit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "审核时间");

    /**
     * The column <code>exam-stu.t_signup_record.f_create_time</code>. 创建时间
     */
    public final TableField<SignupRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_signup_record.f_is_current</code>. 是否最新的报名记录0否 1是
     */
    public final TableField<SignupRecordRecord, Integer> IS_CURRENT = createField("f_is_current", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否最新的报名记录0否 1是");

    /**
     * The column <code>exam-stu.t_signup_record.f_signup_id</code>. 报名记录id
     */
    public final TableField<SignupRecordRecord, String> SIGNUP_ID = createField("f_signup_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "报名记录id");

    /**
     * Create a <code>exam-stu.t_signup_record</code> table reference
     */
    public SignupRecord() {
        this("t_signup_record", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_signup_record</code> table reference
     */
    public SignupRecord(String alias) {
        this(alias, SIGNUP_RECORD);
    }

    private SignupRecord(String alias, Table<SignupRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private SignupRecord(String alias, Table<SignupRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试报名记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignupRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGNUP_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignupRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<SignupRecordRecord>>asList(Keys.KEY_T_SIGNUP_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SignupRecord as(String alias) {
        return new SignupRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SignupRecord rename(String name) {
        return new SignupRecord(name, null);
    }
}
