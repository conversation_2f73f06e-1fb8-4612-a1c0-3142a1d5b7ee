/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CloudAnnouncementRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
/**
 * 云改专区通知公告
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CloudAnnouncement extends TableImpl<CloudAnnouncementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_cloud_announcement</code>
     */
    public static final CloudAnnouncement CLOUD_ANNOUNCEMENT = new CloudAnnouncement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CloudAnnouncementRecord> getRecordType() {
        return CloudAnnouncementRecord.class;
    }

    /**
     * The column <code>exam.t_cloud_announcement.f_id</code>. ID
     */
    public final TableField<CloudAnnouncementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_cloud_announcement.f_create_time</code>.
     */
    public final TableField<CloudAnnouncementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_cloud_announcement.f_title</code>. 公告标题
     */
    public final TableField<CloudAnnouncementRecord, String> TITLE = createField("f_title", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "公告标题");

    /**
     * The column <code>exam.t_cloud_announcement.f_description</code>. 公告描述
     */
    public final TableField<CloudAnnouncementRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "公告描述");

    /**
     * Create a <code>exam.t_cloud_announcement</code> table reference
     */
    public CloudAnnouncement() {
        this("t_cloud_announcement", null);
    }

    /**
     * Create an aliased <code>exam.t_cloud_announcement</code> table reference
     */
    public CloudAnnouncement(String alias) {
        this(alias, CLOUD_ANNOUNCEMENT);
    }

    private CloudAnnouncement(String alias, Table<CloudAnnouncementRecord> aliased) {
        this(alias, aliased, null);
    }

    private CloudAnnouncement(String alias, Table<CloudAnnouncementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "云改专区通知公告");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CloudAnnouncementRecord> getPrimaryKey() {
        return Keys.KEY_T_CLOUD_ANNOUNCEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CloudAnnouncementRecord>> getKeys() {
        return Arrays.<UniqueKey<CloudAnnouncementRecord>>asList(Keys.KEY_T_CLOUD_ANNOUNCEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CloudAnnouncement as(String alias) {
        return new CloudAnnouncement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CloudAnnouncement rename(String name) {
        return new CloudAnnouncement(name, null);
    }
}
