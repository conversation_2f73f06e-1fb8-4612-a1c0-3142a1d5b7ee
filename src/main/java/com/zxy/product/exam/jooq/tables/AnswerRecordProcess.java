/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecordProcessRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 问题记录流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecordProcess extends TableImpl<AnswerRecordProcessRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_answer_record_process</code>
     */
    public static final AnswerRecordProcess ANSWER_RECORD_PROCESS = new AnswerRecordProcess();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecordProcessRecord> getRecordType() {
        return AnswerRecordProcessRecord.class;
    }

    /**
     * The column <code>exam-stu.t_answer_record_process.id</code>.
     */
    public final TableField<AnswerRecordProcessRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecordProcessRecord, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process.f_question_id</code>.
     */
    public final TableField<AnswerRecordProcessRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process.f_answer</code>.
     */
    public final TableField<AnswerRecordProcessRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process.f_create_time</code>.
     */
    public final TableField<AnswerRecordProcessRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>exam-stu.t_answer_record_process</code> table reference
     */
    public AnswerRecordProcess() {
        this("t_answer_record_process", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_answer_record_process</code> table reference
     */
    public AnswerRecordProcess(String alias) {
        this(alias, ANSWER_RECORD_PROCESS);
    }

    private AnswerRecordProcess(String alias, Table<AnswerRecordProcessRecord> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecordProcess(String alias, Table<AnswerRecordProcessRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "问题记录流水表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Identity<AnswerRecordProcessRecord, Long> getIdentity() {
        return Keys.IDENTITY_ANSWER_RECORD_PROCESS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecordProcessRecord> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_PROCESS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecordProcessRecord>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecordProcessRecord>>asList(Keys.KEY_T_ANSWER_RECORD_PROCESS_PRIMARY, Keys.KEY_T_ANSWER_RECORD_PROCESS_UNIQ_QUESTION_ID_EXAM_RECORD_ID);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess as(String alias) {
        return new AnswerRecordProcess(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecordProcess rename(String name) {
        return new AnswerRecordProcess(name, null);
    }
}
