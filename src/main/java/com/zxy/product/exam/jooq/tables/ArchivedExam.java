/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ArchivedExamRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 历史考试归档表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ArchivedExam extends TableImpl<ArchivedExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_archived_exam</code>
     */
    public static final ArchivedExam ARCHIVED_EXAM = new ArchivedExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ArchivedExamRecord> getRecordType() {
        return ArchivedExamRecord.class;
    }

    /**
     * The column <code>exam.t_archived_exam.f_id</code>.
     */
    public final TableField<ArchivedExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_archived_exam.f_create_time</code>. 创建时间
     */
    public final TableField<ArchivedExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_archived_exam.f_organization_id</code>. 归属部门ID  和ownerOrganizationId 一样
     */
    public final TableField<ArchivedExamRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "归属部门ID  和ownerOrganizationId 一样");

    /**
     * The column <code>exam.t_archived_exam.f_name</code>. 考试名称
     */
    public final TableField<ArchivedExamRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试名称");

    /**
     * The column <code>exam.t_archived_exam.f_start_time</code>. 考试开始时间
     */
    public final TableField<ArchivedExamRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "考试开始时间");

    /**
     * The column <code>exam.t_archived_exam.f_end_time</code>. 考试结束时间
     */
    public final TableField<ArchivedExamRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "考试结束时间");

    /**
     * The column <code>exam.t_archived_exam.f_duration</code>. 考试时长(分钟)
     */
    public final TableField<ArchivedExamRecord, Integer> DURATION = createField("f_duration", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试时长(分钟)");

    /**
     * The column <code>exam.t_archived_exam.f_pass_score</code>. 及格成绩
     */
    public final TableField<ArchivedExamRecord, Integer> PASS_SCORE = createField("f_pass_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "及格成绩");

    /**
     * The column <code>exam.t_archived_exam.f_cover_id</code>. 封面id
     */
    public final TableField<ArchivedExamRecord, String> COVER_ID = createField("f_cover_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面id");

    /**
     * The column <code>exam.t_archived_exam.f_certificate_id</code>. 证书id
     */
    public final TableField<ArchivedExamRecord, String> CERTIFICATE_ID = createField("f_certificate_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书id");

    /**
     * The column <code>exam.t_archived_exam.f_support_app</code>. 1:pc 2: app
     */
    public final TableField<ArchivedExamRecord, String> SUPPORT_APP = createField("f_support_app", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "1:pc 2: app");

    /**
     * The column <code>exam.t_archived_exam.f_exam_notes</code>. 考前须知
     */
    public final TableField<ArchivedExamRecord, String> EXAM_NOTES = createField("f_exam_notes", org.jooq.impl.SQLDataType.VARCHAR.length(3000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考前须知");

    /**
     * The column <code>exam.t_archived_exam.f_publisher_id</code>. 发布人
     */
    public final TableField<ArchivedExamRecord, String> PUBLISHER_ID = createField("f_publisher_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布人");

    /**
     * The column <code>exam.t_archived_exam.f_owned_organization_id</code>. 所属部门
     */
    public final TableField<ArchivedExamRecord, String> OWNED_ORGANIZATION_ID = createField("f_owned_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属部门");

    /**
     * The column <code>exam.t_archived_exam.f_publish_organization_id</code>. 发布部门
     */
    public final TableField<ArchivedExamRecord, String> PUBLISH_ORGANIZATION_ID = createField("f_publish_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布部门");

    /**
     * The column <code>exam.t_archived_exam.f_type</code>. 考试类型,1普通考试 2测试考试 3集团认证考试 4省公司认证考试 5移动云考试 6网格长认证考试
     */
    public final TableField<ArchivedExamRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试类型,1普通考试 2测试考试 3集团认证考试 4省公司认证考试 5移动云考试 6网格长认证考试");

    /**
     * The column <code>exam.t_archived_exam.f_applicant_number</code>. 报名人数
     */
    public final TableField<ArchivedExamRecord, Integer> APPLICANT_NUMBER = createField("f_applicant_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "报名人数");

    /**
     * The column <code>exam.t_archived_exam.f_join_number</code>. 参加考试人数
     */
    public final TableField<ArchivedExamRecord, Integer> JOIN_NUMBER = createField("f_join_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "参加考试人数");

    /**
     * The column <code>exam.t_archived_exam.f_pass_percent</code>. 及格率
     */
    public final TableField<ArchivedExamRecord, Integer> PASS_PERCENT = createField("f_pass_percent", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "及格率");

    /**
     * The column <code>exam.t_archived_exam.f_status</code>. 考试状态1: 未开始, 2: 发布中, 3: 未开始,4：报名中  5: 开考中，6: 已结束,
     */
    public final TableField<ArchivedExamRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试状态1: 未开始, 2: 发布中, 3: 未开始,4：报名中  5: 开考中，6: 已结束,");

    /**
     * The column <code>exam.t_archived_exam.f_need_applicant</code>. 是否需要报名，1是，2否
     */
    public final TableField<ArchivedExamRecord, Integer> NEED_APPLICANT = createField("f_need_applicant", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否需要报名，1是，2否");

    /**
     * The column <code>exam.t_archived_exam.f_applicant_start_time</code>. 报名开始时间
     */
    public final TableField<ArchivedExamRecord, Long> APPLICANT_START_TIME = createField("f_applicant_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "报名开始时间");

    /**
     * The column <code>exam.t_archived_exam.f_applicant_end_time</code>. 报名截止时间
     */
    public final TableField<ArchivedExamRecord, Long> APPLICANT_END_TIME = createField("f_applicant_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "报名截止时间");

    /**
     * The column <code>exam.t_archived_exam.f_applicant_need_audit</code>. 报名是否需要审核,0不需要,1需要
     */
    public final TableField<ArchivedExamRecord, Integer> APPLICANT_NEED_AUDIT = createField("f_applicant_need_audit", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "报名是否需要审核,0不需要,1需要");

    /**
     * The column <code>exam.t_archived_exam.f_is_open_practice</code>. 是否开放练习
     */
    public final TableField<ArchivedExamRecord, Integer> IS_OPEN_PRACTICE = createField("f_is_open_practice", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否开放练习");

    /**
     * The column <code>exam.t_archived_exam.f_paper_class_id</code>. 关联的试卷id
     */
    public final TableField<ArchivedExamRecord, String> PAPER_CLASS_ID = createField("f_paper_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "关联的试卷id");

    /**
     * The column <code>exam.t_archived_exam.f_need_review</code>. 报名考试是否需要审核
     */
    public final TableField<ArchivedExamRecord, Integer> NEED_REVIEW = createField("f_need_review", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "报名考试是否需要审核");

    /**
     * The column <code>exam.t_archived_exam.f_is_allow_switch</code>. 是否限制切屏
     */
    public final TableField<ArchivedExamRecord, Integer> IS_ALLOW_SWITCH = createField("f_is_allow_switch", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否限制切屏");

    /**
     * The column <code>exam.t_archived_exam.f_allow_switch_times</code>. 允许切屏次数
     */
    public final TableField<ArchivedExamRecord, Integer> ALLOW_SWITCH_TIMES = createField("f_allow_switch_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "允许切屏次数");

    /**
     * The column <code>exam.t_archived_exam.f_is_show_answer_immed</code>. 考试结束后，是否可以立即查看答案
     */
    public final TableField<ArchivedExamRecord, Integer> IS_SHOW_ANSWER_IMMED = createField("f_is_show_answer_immed", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试结束后，是否可以立即查看答案");

    /**
     * The column <code>exam.t_archived_exam.f_show_answer_rule</code>. 显示答案的规则
     */
    public final TableField<ArchivedExamRecord, Integer> SHOW_ANSWER_RULE = createField("f_show_answer_rule", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "显示答案的规则");

    /**
     * The column <code>exam.t_archived_exam.f_is_allow_pause</code>. 是否允许暂停
     */
    public final TableField<ArchivedExamRecord, Integer> IS_ALLOW_PAUSE = createField("f_is_allow_pause", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否允许暂停");

    /**
     * The column <code>exam.t_archived_exam.f_is_allow_add_room</code>. 是否允许添加考场
     */
    public final TableField<ArchivedExamRecord, Integer> IS_ALLOW_ADD_ROOM = createField("f_is_allow_add_room", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否允许添加考场");

    /**
     * The column <code>exam.t_archived_exam.f_allow_exam_much_times</code>. 是否允许多次考试
     */
    public final TableField<ArchivedExamRecord, Integer> ALLOW_EXAM_MUCH_TIMES = createField("f_allow_exam_much_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否允许多次考试");

    /**
     * The column <code>exam.t_archived_exam.f_allow_exam_times</code>. 允许考试的次数
     */
    public final TableField<ArchivedExamRecord, Integer> ALLOW_EXAM_TIMES = createField("f_allow_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "允许考试的次数");

    /**
     * The column <code>exam.t_archived_exam.f_paper_show_rule</code>. 考试试卷显示规则(1: 一屏一题显示, 2: 所有题目一并显示)
     */
    public final TableField<ArchivedExamRecord, Integer> PAPER_SHOW_RULE = createField("f_paper_show_rule", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试试卷显示规则(1: 一屏一题显示, 2: 所有题目一并显示)");

    /**
     * The column <code>exam.t_archived_exam.f_paper_sort_rule</code>. 试卷排序规则（1:默认排序, 2:试题打乱排序, 3:选项打乱排序, 4:试题及选项打乱排序)
     */
    public final TableField<ArchivedExamRecord, Integer> PAPER_SORT_RULE = createField("f_paper_sort_rule", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "试卷排序规则（1:默认排序, 2:试题打乱排序, 3:选项打乱排序, 4:试题及选项打乱排序)");

    /**
     * The column <code>exam.t_archived_exam.f_parent_id</code>.
     */
    public final TableField<ArchivedExamRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_archived_exam.f_anonymity_mark</code>. 匿名评卷 （0:否 1:是）
     */
    public final TableField<ArchivedExamRecord, Integer> ANONYMITY_MARK = createField("f_anonymity_mark", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "匿名评卷 （0:否 1:是）");

    /**
     * The column <code>exam.t_archived_exam.f_has_cert</code>. 是否拥有证书
     */
    public final TableField<ArchivedExamRecord, Integer> HAS_CERT = createField("f_has_cert", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否拥有证书");

    /**
     * The column <code>exam.t_archived_exam.f_source_type</code>. 来源， 1：考试活动，2：课程，3专题，4班级，5直播
     */
    public final TableField<ArchivedExamRecord, Integer> SOURCE_TYPE = createField("f_source_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源， 1：考试活动，2：课程，3专题，4班级，5直播");

    /**
     * The column <code>exam.t_archived_exam.f_send_to_center</code>.
     */
    public final TableField<ArchivedExamRecord, Integer> SEND_TO_CENTER = createField("f_send_to_center", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_archived_exam.f_is_over_by_pass_exam</code>. 是否及格才算完成（提供其他模块使用）
     */
    public final TableField<ArchivedExamRecord, Integer> IS_OVER_BY_PASS_EXAM = createField("f_is_over_by_pass_exam", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否及格才算完成（提供其他模块使用）");

    /**
     * The column <code>exam.t_archived_exam.f_is_set_pass_score</code>. 是否设置分数
     */
    public final TableField<ArchivedExamRecord, Integer> IS_SET_PASS_SCORE = createField("f_is_set_pass_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否设置分数");

    /**
     * The column <code>exam.t_archived_exam.f_previous_status</code>. 撤销 前一个状态
     */
    public final TableField<ArchivedExamRecord, Integer> PREVIOUS_STATUS = createField("f_previous_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "撤销 前一个状态");

    /**
     * The column <code>exam.t_archived_exam.f_join_person_time</code>. 参考人次
     */
    public final TableField<ArchivedExamRecord, Integer> JOIN_PERSON_TIME = createField("f_join_person_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "参考人次");

    /**
     * The column <code>exam.t_archived_exam.f_cover_id_path</code>. 封面文件路径
     */
    public final TableField<ArchivedExamRecord, String> COVER_ID_PATH = createField("f_cover_id_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面文件路径");

    /**
     * The column <code>exam.t_archived_exam.f_client_type</code>. 客户端类型 0: 全部, 1: PC, 2: APP
     */
    public final TableField<ArchivedExamRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "客户端类型 0: 全部, 1: PC, 2: APP");

    /**
     * The column <code>exam.t_archived_exam.f_password</code>. 考试密码
     */
    public final TableField<ArchivedExamRecord, String> PASSWORD = createField("f_password", org.jooq.impl.SQLDataType.CHAR.length(6).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CHAR)), this, "考试密码");

    /**
     * The column <code>exam.t_archived_exam.f_is_set_password</code>. 是否设置考试密码 ，0:否，1：是
     */
    public final TableField<ArchivedExamRecord, Integer> IS_SET_PASSWORD = createField("f_is_set_password", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否设置考试密码 ，0:否，1：是");

    /**
     * The column <code>exam.t_archived_exam.f_is_set_personal_code</code>. 是否设置个人码，0:否，1：是
     */
    public final TableField<ArchivedExamRecord, Integer> IS_SET_PERSONAL_CODE = createField("f_is_set_personal_code", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否设置个人码，0:否，1：是");

    /**
     * The column <code>exam.t_archived_exam.f_is_permit_view_code</code>. 是否允许考试查看个人码，0:否，1：是
     */
    public final TableField<ArchivedExamRecord, Integer> IS_PERMIT_VIEW_CODE = createField("f_is_permit_view_code", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否允许考试查看个人码，0:否，1：是");

    /**
     * The column <code>exam.t_archived_exam.f_profession_id</code>. 考试专业id
     */
    public final TableField<ArchivedExamRecord, String> PROFESSION_ID = createField("f_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试专业id");

    /**
     * The column <code>exam.t_archived_exam.f_sub_profession_id</code>. 考试子专业id
     */
    public final TableField<ArchivedExamRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试子专业id");

    /**
     * The column <code>exam.t_archived_exam.f_level_id</code>. 等级id
     */
    public final TableField<ArchivedExamRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级id");

    /**
     * The column <code>exam.t_archived_exam.f_annual</code>. 年度
     */
    public final TableField<ArchivedExamRecord, Integer> ANNUAL = createField("f_annual", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "年度");

    /**
     * The column <code>exam.t_archived_exam.f_exam_batch</code>. 考试批次
     */
    public final TableField<ArchivedExamRecord, String> EXAM_BATCH = createField("f_exam_batch", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试批次");

    /**
     * The column <code>exam.t_archived_exam.f_audit_start_time</code>. 审核时间
     */
    public final TableField<ArchivedExamRecord, Long> AUDIT_START_TIME = createField("f_audit_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "审核时间");

    /**
     * The column <code>exam.t_archived_exam.f_audit_end_time</code>. 审核结束时间
     */
    public final TableField<ArchivedExamRecord, Long> AUDIT_END_TIME = createField("f_audit_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "审核结束时间");

    /**
     * The column <code>exam.t_archived_exam.f_pre_approval</code>. 是否启用预审核；0:否，1:是 默认为1
     */
    public final TableField<ArchivedExamRecord, Integer> PRE_APPROVAL = createField("f_pre_approval", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否启用预审核；0:否，1:是 默认为1");

    /**
     * The column <code>exam.t_archived_exam.f_issue_time</code>. 自动发放时间
     */
    public final TableField<ArchivedExamRecord, Long> ISSUE_TIME = createField("f_issue_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "自动发放时间");

    /**
     * The column <code>exam.t_archived_exam.f_issue_flag</code>. 是否自动发放：0:否，1:是，默认为1
     */
    public final TableField<ArchivedExamRecord, Integer> ISSUE_FLAG = createField("f_issue_flag", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否自动发放：0:否，1:是，默认为1");

    /**
     * The column <code>exam.t_archived_exam.f_evalution_rule</code>. 证书综合评价规则，一个json数据
     */
    public final TableField<ArchivedExamRecord, String> EVALUTION_RULE = createField("f_evalution_rule", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书综合评价规则，一个json数据");

    /**
     * The column <code>exam.t_archived_exam.f_equipment_type_id</code>. 设备id
     */
    public final TableField<ArchivedExamRecord, String> EQUIPMENT_TYPE_ID = createField("f_equipment_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备id");

    /**
     * The column <code>exam.t_archived_exam.f_pre_approval_rule</code>. 预审规则，{r1:0,r2:1,r3:0,r4:1},0未选中，1选中
     */
    public final TableField<ArchivedExamRecord, String> PRE_APPROVAL_RULE = createField("f_pre_approval_rule", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "预审规则，{r1:0,r2:1,r3:0,r4:1},0未选中，1选中");

    /**
     * The column <code>exam.t_archived_exam.f_admission_ticket</code>. 准考证是否配置
     */
    public final TableField<ArchivedExamRecord, Integer> ADMISSION_TICKET = createField("f_admission_ticket", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "准考证是否配置");

    /**
     * The column <code>exam.t_archived_exam.f_need_fill_out_info</code>. 考试报名是否需要填写个人信息 0:否，1:是
     */
    public final TableField<ArchivedExamRecord, Integer> NEED_FILL_OUT_INFO = createField("f_need_fill_out_info", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "考试报名是否需要填写个人信息 0:否，1:是");

    /**
     * The column <code>exam.t_archived_exam.f_force_submit_time</code>. 强制交卷人数
     */
    public final TableField<ArchivedExamRecord, Integer> FORCE_SUBMIT_TIME = createField("f_force_submit_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "强制交卷人数");

    /**
     * The column <code>exam.t_archived_exam.f_normal_submit_time</code>. 正常交卷人数
     */
    public final TableField<ArchivedExamRecord, Integer> NORMAL_SUBMIT_TIME = createField("f_normal_submit_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "正常交卷人数");

    /**
     * The column <code>exam.t_archived_exam.f_over_time_submit_time</code>. 超时交卷人数
     */
    public final TableField<ArchivedExamRecord, Integer> OVER_TIME_SUBMIT_TIME = createField("f_over_time_submit_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "超时交卷人数");

    /**
     * The column <code>exam.t_archived_exam.f_going_join_number</code>. 考试中人数
     */
    public final TableField<ArchivedExamRecord, Integer> GOING_JOIN_NUMBER = createField("f_going_join_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试中人数");

    /**
     * The column <code>exam.t_archived_exam.f_switch_submit_person_time</code>. 切屏强制交卷人次
     */
    public final TableField<ArchivedExamRecord, Integer> SWITCH_SUBMIT_PERSON_TIME = createField("f_switch_submit_person_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "切屏强制交卷人次");

    /**
     * The column <code>exam.t_archived_exam.f_auth_exam_org_type</code>. 认证考试部门类型,1.网络部,2.市场部,其它为0
     */
    public final TableField<ArchivedExamRecord, Integer> AUTH_EXAM_ORG_TYPE = createField("f_auth_exam_org_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "认证考试部门类型,1.网络部,2.市场部,其它为0");

    /**
     * The column <code>exam.t_archived_exam.f_show_score_time</code>. 成绩公布时间
     */
    public final TableField<ArchivedExamRecord, Long> SHOW_SCORE_TIME = createField("f_show_score_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "成绩公布时间");

    /**
     * The column <code>exam.t_archived_exam.f_random_type</code>. 随机组卷方式(1:每次试卷相同，2：每次试卷随机)
     */
    public final TableField<ArchivedExamRecord, Integer> RANDOM_TYPE = createField("f_random_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "随机组卷方式(1:每次试卷相同，2：每次试卷随机)");

    /**
     * The column <code>exam.t_archived_exam.f_indefinite</code>. 不定向开关（0：关闭 1：开启）
     */
    public final TableField<ArchivedExamRecord, Integer> INDEFINITE = createField("f_indefinite", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "不定向开关（0：关闭 1：开启）");

    /**
     * The column <code>exam.t_archived_exam.f_is_set_encrypt</code>. 考试管理是否加密，0：否，1：是
     */
    public final TableField<ArchivedExamRecord, Integer> IS_SET_ENCRYPT = createField("f_is_set_encrypt", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "考试管理是否加密，0：否，1：是");

    /**
     * The column <code>exam.t_archived_exam.f_encrypt</code>. 考试管理密码
     */
    public final TableField<ArchivedExamRecord, String> ENCRYPT = createField("f_encrypt", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试管理密码");

    /**
     * The column <code>exam.t_archived_exam.f_create_member_id</code>. 考试创建者id
     */
    public final TableField<ArchivedExamRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试创建者id");

    /**
     * The column <code>exam.t_archived_exam.f_face_enter</code>. 是否人脸进入考试(0：关闭，1：开启)
     */
    public final TableField<ArchivedExamRecord, Integer> FACE_ENTER = createField("f_face_enter", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否人脸进入考试(0：关闭，1：开启)");

    /**
     * The column <code>exam.t_archived_exam.f_face_monitor</code>. 是否人脸监考(0：关闭，1：开启)
     */
    public final TableField<ArchivedExamRecord, Integer> FACE_MONITOR = createField("f_face_monitor", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否人脸监考(0：关闭，1：开启)");

    /**
     * Create a <code>exam.t_archived_exam</code> table reference
     */
    public ArchivedExam() {
        this("t_archived_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_archived_exam</code> table reference
     */
    public ArchivedExam(String alias) {
        this(alias, ARCHIVED_EXAM);
    }

    private ArchivedExam(String alias, Table<ArchivedExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private ArchivedExam(String alias, Table<ArchivedExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "历史考试归档表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ArchivedExamRecord> getPrimaryKey() {
        return Keys.KEY_T_ARCHIVED_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ArchivedExamRecord>> getKeys() {
        return Arrays.<UniqueKey<ArchivedExamRecord>>asList(Keys.KEY_T_ARCHIVED_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ArchivedExam as(String alias) {
        return new ArchivedExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ArchivedExam rename(String name) {
        return new ArchivedExam(name, null);
    }
}
