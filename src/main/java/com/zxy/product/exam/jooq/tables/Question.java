/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Question extends TableImpl<QuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question</code>
     */
    public static final Question QUESTION = new Question();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionRecord> getRecordType() {
        return QuestionRecord.class;
    }

    /**
     * The column <code>exam.t_question.f_id</code>.
     */
    public final TableField<QuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question.f_create_time</code>.
     */
    public final TableField<QuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question.f_type</code>. 类型
     */
    public final TableField<QuestionRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型");

    /**
     * The column <code>exam.t_question.f_content</code>. 试题题干，富文本格式
     */
    public final TableField<QuestionRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "试题题干，富文本格式");

    /**
     * The column <code>exam.t_question.f_is_subjective</code>. 是否主观题
     */
    public final TableField<QuestionRecord, Integer> IS_SUBJECTIVE = createField("f_is_subjective", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否主观题");

    /**
     * The column <code>exam.t_question.f_parent_id</code>. 父题
     */
    public final TableField<QuestionRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "父题");

    /**
     * The column <code>exam.t_question.f_question_depot_id</code>. 题库
     */
    public final TableField<QuestionRecord, String> QUESTION_DEPOT_ID = createField("f_question_depot_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "题库");

    /**
     * The column <code>exam.t_question.f_difficulty</code>. 难度
     */
    public final TableField<QuestionRecord, Integer> DIFFICULTY = createField("f_difficulty", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "难度");

    /**
     * The column <code>exam.t_question.f_organization_id</code>. 所属部门
     */
    public final TableField<QuestionRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属部门");

    /**
     * The column <code>exam.t_question.f_score</code>. 分数
     */
    public final TableField<QuestionRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分数");

    /**
     * The column <code>exam.t_question.f_error_rate</code>. 易错率
     */
    public final TableField<QuestionRecord, Integer> ERROR_RATE = createField("f_error_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "易错率");

    /**
     * The column <code>exam.t_question.f_status</code>. 状态
     */
    public final TableField<QuestionRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态");

    /**
     * The column <code>exam.t_question.f_mark_amount</code>. 收藏
     */
    public final TableField<QuestionRecord, Integer> MARK_AMOUNT = createField("f_mark_amount", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "收藏");

    /**
     * The column <code>exam.t_question.f_recovery_count</code>. 纠错
     */
    public final TableField<QuestionRecord, Integer> RECOVERY_COUNT = createField("f_recovery_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "纠错");

    /**
     * The column <code>exam.t_question.f_source_type</code>. 来源类型 1:考试,2调研活动，3：调研问卷，4：评估问卷
     */
    public final TableField<QuestionRecord, Integer> SOURCE_TYPE = createField("f_source_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源类型 1:考试,2调研活动，3：调研问卷，4：评估问卷");

    /**
     * The column <code>exam.t_question.f_content_text</code>. 试题题干，纯文本格式
     */
    public final TableField<QuestionRecord, String> CONTENT_TEXT = createField("f_content_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "试题题干，纯文本格式");

    /**
     * The column <code>exam.t_question.f_exam_num</code>. 试题编号，同步试卷的时候落库，报错的时候返回第三方
     */
    public final TableField<QuestionRecord, String> EXAM_NUM = createField("f_exam_num", org.jooq.impl.SQLDataType.VARCHAR.length(128).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题编号，同步试卷的时候落库，报错的时候返回第三方");

    /**
     * The column <code>exam.t_question.f_exam_profession_name</code>. 试题所属专业，同步试卷的时候落库
     */
    public final TableField<QuestionRecord, String> EXAM_PROFESSION_NAME = createField("f_exam_profession_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题所属专业，同步试卷的时候落库");

    /**
     * The column <code>exam.t_question.f_exam_sub_profession_name</code>. 试题所属子专业，同步试卷的时候落库
     */
    public final TableField<QuestionRecord, String> EXAM_SUB_PROFESSION_NAME = createField("f_exam_sub_profession_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题所属子专业，同步试卷的时候落库");

    /**
     * The column <code>exam.t_question.f_parsing</code>. 试题解析富文本
     */
    public final TableField<QuestionRecord, String> PARSING = createField("f_parsing", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "试题解析富文本");

    /**
     * The column <code>exam.t_question.f_parsing_text</code>. 试题解析纯文本
     */
    public final TableField<QuestionRecord, String> PARSING_TEXT = createField("f_parsing_text", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题解析纯文本");

    /**
     * The column <code>exam.t_question.f_modify_date</code>. 修改时间
     */
    public final TableField<QuestionRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>exam.t_question.f_question_path_encrypt</code>. 试题任意层级目录是否加密（0：否 1：是）
     */
    public final TableField<QuestionRecord, Integer> QUESTION_PATH_ENCRYPT = createField("f_question_path_encrypt", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "试题任意层级目录是否加密（0：否 1：是）");

    /**
     * The column <code>exam.t_question.f_encrypt_name</code>. 加密试题题干名称
     */
    public final TableField<QuestionRecord, String> ENCRYPT_NAME = createField("f_encrypt_name", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "加密试题题干名称");

    /**
     * Create a <code>exam.t_question</code> table reference
     */
    public Question() {
        this("t_question", null);
    }

    /**
     * Create an aliased <code>exam.t_question</code> table reference
     */
    public Question(String alias) {
        this(alias, QUESTION);
    }

    private Question(String alias, Table<QuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private Question(String alias, Table<QuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionRecord>>asList(Keys.KEY_T_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Question as(String alias) {
        return new Question(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Question rename(String name) {
        return new Question(name, null);
    }
}
