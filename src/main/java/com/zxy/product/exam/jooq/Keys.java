/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq;


import com.zxy.product.exam.jooq.tables.Exam;
import com.zxy.product.exam.jooq.tables.*;
import com.zxy.product.exam.jooq.tables.records.ExamRecord;
import com.zxy.product.exam.jooq.tables.records.SignupRecord;
import com.zxy.product.exam.jooq.tables.records.*;
import org.jooq.Identity;
import org.jooq.UniqueKey;
import org.jooq.impl.AbstractKeys;

import javax.annotation.Generated;


/**
 * A class modelling foreign key relationships between tables of the <code>exam</code> 
 * schema
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------
    //todo exam 和 exam-stu 表信息均在此类中按照shema分块进行维护
    //todo 以下是exam schema中的表信息
    public static final UniqueKey<SubAuthenticatedExamGroupRecord> KEY_T_SUB_AUTHENTICATED_EXAM_GROUP_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_EXAM_GROUP_PRIMARY;
    public static final UniqueKey<SubAuthenticatedCancelCertificateRecordRecord> KEY_T_SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD_PRIMARY;
    public static final UniqueKey<ArchivedExamRecord> KEY_T_ARCHIVED_EXAM_PRIMARY = UniqueKeys0.KEY_T_ARCHIVED_EXAM_PRIMARY;
    public static final UniqueKey<ExamRecord> KEY_T_EXAM_PRIMARY = UniqueKeys0.KEY_T_EXAM_PRIMARY;
    public static final UniqueKey<ActivityRecord> KEY_T_ACTIVITY_PRIMARY = UniqueKeys0.KEY_T_ACTIVITY_PRIMARY;
    public static final UniqueKey<AnnouncementRecord> KEY_T_ANNOUNCEMENT_PRIMARY = UniqueKeys0.KEY_T_ANNOUNCEMENT_PRIMARY;
    public static final UniqueKey<AnswerRecordTempRecord> KEY_T_ANSWER_RECORD_TEMP_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_TEMP_PRIMARY;
    public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_ITEM_PRIMARY;
    public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_MEMBER_PRIMARY;
    public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_OBJECT_PRIMARY;
    public static final UniqueKey<BusinessResearchRecord> KEY_T_BUSINESS_RESEARCH_PRIMARY = UniqueKeys0.KEY_T_BUSINESS_RESEARCH_PRIMARY;
    public static final UniqueKey<BusinessTopicRecord> KEY_T_BUSINESS_TOPIC_PRIMARY = UniqueKeys0.KEY_T_BUSINESS_TOPIC_PRIMARY;
    public static final UniqueKey<CertificateHistoryRecord> KEY_T_CERTIFICATE_HISTORY_PRIMARY = UniqueKeys0.KEY_T_CERTIFICATE_HISTORY_PRIMARY;
    public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_PRIMARY = UniqueKeys0.KEY_T_CERTIFICATE_RECORD_PRIMARY;
    public static final UniqueKey<CloudAnnouncementRecord> KEY_T_CLOUD_ANNOUNCEMENT_PRIMARY = UniqueKeys0.KEY_T_CLOUD_ANNOUNCEMENT_PRIMARY;
    public static final UniqueKey<CloudExamRecord> KEY_T_CLOUD_EXAM_PRIMARY = UniqueKeys0.KEY_T_CLOUD_EXAM_PRIMARY;
    public static final UniqueKey<CloudLevelRecord> KEY_T_CLOUD_LEVEL_PRIMARY = UniqueKeys0.KEY_T_CLOUD_LEVEL_PRIMARY;
    public static final UniqueKey<CloudProfessionRecord> KEY_T_CLOUD_PROFESSION_PRIMARY = UniqueKeys0.KEY_T_CLOUD_PROFESSION_PRIMARY;
    public static final UniqueKey<DimensionRecord> KEY_T_DIMENSION_PRIMARY = UniqueKeys0.KEY_T_DIMENSION_PRIMARY;
    public static final UniqueKey<DimensionQuestionRecord> KEY_T_DIMENSION_QUESTION_PRIMARY = UniqueKeys0.KEY_T_DIMENSION_QUESTION_PRIMARY;
    public static final UniqueKey<EquipmentTypeRecord> KEY_T_EQUIPMENT_TYPE_PRIMARY = UniqueKeys0.KEY_T_EQUIPMENT_TYPE_PRIMARY;
    public static final UniqueKey<ErrorReasonRecord> KEY_T_ERROR_REASON_PRIMARY = UniqueKeys0.KEY_T_ERROR_REASON_PRIMARY;
    public static final UniqueKey<ExamNoticeRecord> KEY_T_EXAM_NOTICE_PRIMARY = UniqueKeys0.KEY_T_EXAM_NOTICE_PRIMARY;
    public static final UniqueKey<ExamTopicRecord> KEY_T_EXAM_TOPIC_PRIMARY = UniqueKeys0.KEY_T_EXAM_TOPIC_PRIMARY;
    public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_GRANT_DETAIL_PRIMARY;
    public static final UniqueKey<GridAuditRecord> KEY_T_GRID_AUDIT_PRIMARY = UniqueKeys0.KEY_T_GRID_AUDIT_PRIMARY;
	public static final UniqueKey<GridCourseRecord> KEY_T_GRID_COURSE_PRIMARY = UniqueKeys0.KEY_T_GRID_COURSE_PRIMARY;
    public static final UniqueKey<GridCourseExamRecord> KEY_T_GRID_COURSE_EXAM_PRIMARY = UniqueKeys0.KEY_T_GRID_COURSE_EXAM_PRIMARY;
    public static final UniqueKey<GridFileRecord> KEY_T_GRID_FILE_PRIMARY = UniqueKeys0.KEY_T_GRID_FILE_PRIMARY;
	public static final UniqueKey<GridFormalExamRecord> KEY_T_GRID_FORMAL_EXAM_PRIMARY = UniqueKeys0.KEY_T_GRID_FORMAL_EXAM_PRIMARY;
	public static final UniqueKey<GridLevelRecord> KEY_T_GRID_LEVEL_PRIMARY = UniqueKeys0.KEY_T_GRID_LEVEL_PRIMARY;
	public static final UniqueKey<InvigilatorRecord> KEY_T_INVIGILATOR_PRIMARY = UniqueKeys0.KEY_T_INVIGILATOR_PRIMARY;
    public static final UniqueKey<InvigilatorGrantRecord> KEY_T_INVIGILATOR_GRANT_PRIMARY = UniqueKeys0.KEY_T_INVIGILATOR_GRANT_PRIMARY;
    public static final UniqueKey<InvigilatorGrantDetailRecord> KEY_T_INVIGILATOR_GRANT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_INVIGILATOR_GRANT_DETAIL_PRIMARY;
    public static final UniqueKey<MarkConfigRecord> KEY_T_MARK_CONFIG_PRIMARY = UniqueKeys0.KEY_T_MARK_CONFIG_PRIMARY;
    public static final UniqueKey<MarkRecordRecord> KEY_T_MARK_RECORD_PRIMARY = UniqueKeys0.KEY_T_MARK_RECORD_PRIMARY;
    public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = UniqueKeys0.KEY_T_MEMBER_PRIMARY;
    public static final UniqueKey<ModifyAnswerRecordRecord> KEY_T_MODIFY_ANSWER_RECORD_PRIMARY = UniqueKeys0.KEY_T_MODIFY_ANSWER_RECORD_PRIMARY;
    public static final UniqueKey<OldQuestionRecord> KEY_T_OLD_QUESTION_PRIMARY = UniqueKeys0.KEY_T_OLD_QUESTION_PRIMARY;
    public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_PRIMARY;
    public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_DETAIL_PRIMARY;
    public static final UniqueKey<PaperClassRecord> KEY_T_PAPER_CLASS_PRIMARY = UniqueKeys0.KEY_T_PAPER_CLASS_PRIMARY;
    public static final UniqueKey<PaperClassQuestionRecord> KEY_T_PAPER_CLASS_QUESTION_PRIMARY = UniqueKeys0.KEY_T_PAPER_CLASS_QUESTION_PRIMARY;
    public static final UniqueKey<PaperClassTacticRecord> KEY_T_PAPER_CLASS_TACTIC_PRIMARY = UniqueKeys0.KEY_T_PAPER_CLASS_TACTIC_PRIMARY;
    public static final UniqueKey<PaperInstanceRecord> KEY_T_PAPER_INSTANCE_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopyRecord> KEY_T_PAPER_INSTANCE_QUESTION_COPY_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2017Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2017_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2017_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2018Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2018_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2018_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2019Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2019_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2019_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2020Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2020_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2020_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2021Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2021_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2021_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2022Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2022_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2022_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2023Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2023_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2023_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2024Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2024_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2024_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2025Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2025_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2025_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2026Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2026_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2026_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2027Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2027_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2027_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2028Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2028_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2028_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2029Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2029_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2029_PRIMARY;
    public static final UniqueKey<PaperInstanceQuestionCopy_2030Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2030_PRIMARY = UniqueKeys0.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2030_PRIMARY;
    public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = UniqueKeys0.KEY_T_PCCW_RESULT_PRIMARY;
    public static final UniqueKey<PersonalDepotRecord> KEY_T_PERSONAL_DEPOT_PRIMARY = UniqueKeys0.KEY_T_PERSONAL_DEPOT_PRIMARY;
	public static final UniqueKey<ProfessionRecord> KEY_T_PROFESSION_PRIMARY = UniqueKeys0.KEY_T_PROFESSION_PRIMARY;
    public static final UniqueKey<ProfessionLevelRecord> KEY_T_PROFESSION_LEVEL_PRIMARY = UniqueKeys0.KEY_T_PROFESSION_LEVEL_PRIMARY;
    public static final UniqueKey<QuestionRecord> KEY_T_QUESTION_PRIMARY = UniqueKeys0.KEY_T_QUESTION_PRIMARY;
    public static final UniqueKey<QuestionAttrRecord> KEY_T_QUESTION_ATTR_PRIMARY = UniqueKeys0.KEY_T_QUESTION_ATTR_PRIMARY;
    public static final UniqueKey<QuestionAttrCopyRecord> KEY_T_QUESTION_ATTR_COPY_PRIMARY = UniqueKeys0.KEY_T_QUESTION_ATTR_COPY_PRIMARY;
    public static final UniqueKey<QuestionCopyRecord> KEY_T_QUESTION_COPY_PRIMARY = UniqueKeys0.KEY_T_QUESTION_COPY_PRIMARY;
    public static final UniqueKey<QuestionCountRecord> KEY_T_QUESTION_COUNT_PRIMARY = UniqueKeys0.KEY_T_QUESTION_COUNT_PRIMARY;
    public static final UniqueKey<QuestionDepotRecord> KEY_T_QUESTION_DEPOT_PRIMARY = UniqueKeys0.KEY_T_QUESTION_DEPOT_PRIMARY;
    public static final UniqueKey<QuestionErrorRateRecord> KEY_T_QUESTION_ERROR_RATE_PRIMARY = UniqueKeys0.KEY_T_QUESTION_ERROR_RATE_PRIMARY;
    public static final UniqueKey<QuestionRecoveryRecord> KEY_T_QUESTION_RECOVERY_PRIMARY = UniqueKeys0.KEY_T_QUESTION_RECOVERY_PRIMARY;
    public static final UniqueKey<ResearchAnswerRecordRecord> KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY;
    public static final UniqueKey<ResearchHistoryResultRecord> KEY_T_RESEARCH_HISTORY_RESULT_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_HISTORY_RESULT_PRIMARY;
    public static final UniqueKey<ResearchQuestionaryRecord> KEY_T_RESEARCH_QUESTIONARY_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_QUESTIONARY_PRIMARY;
    public static final UniqueKey<ResearchRecordRecord> KEY_T_RESEARCH_RECORD_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_RECORD_PRIMARY;
    public static final UniqueKey<ResearchSummaryRecord> KEY_T_RESEARCH_SUMMARY_PRIMARY = UniqueKeys0.KEY_T_RESEARCH_SUMMARY_PRIMARY;
    public static final UniqueKey<SyncInfoRecord> KEY_T_SYNC_INFO_PRIMARY = UniqueKeys0.KEY_T_SYNC_INFO_PRIMARY;
    public static final UniqueKey<TodayQuestionRecord> KEY_T_TODAY_QUESTION_PRIMARY = UniqueKeys0.KEY_T_TODAY_QUESTION_PRIMARY;
    public static final UniqueKey<ExamStudyPlanConfigRecord> KEY_T_EXAM_STUDY_PLAN_CONFIG_PRIMARY = UniqueKeys0.KEY_T_EXAM_STUDY_PLAN_CONFIG_PRIMARY;
    public static final UniqueKey<DeleteDataExamRecord> KEY_T_DELETE_DATA_EXAM_PRIMARY = UniqueKeys0.KEY_T_DELETE_DATA_EXAM_PRIMARY;
    public static final UniqueKey<LearningStyleEvaluationRecord> KEY_T_LEARNING_STYLE_EVALUATION_PRIMARY = UniqueKeys0.KEY_T_LEARNING_STYLE_EVALUATION_PRIMARY;
    public static final UniqueKey<LearningStyleEvaluationRecord> KEY_T_LEARNING_STYLE_EVALUATION_IDX_MEMBER_ID = UniqueKeys0.KEY_T_LEARNING_STYLE_EVALUATION_IDX_MEMBER_ID;
    public static final UniqueKey<RelevanceCourseExamRecord> KEY_T_RELEVANCE_COURSE_EXAM_PRIMARY = UniqueKeys0.KEY_T_RELEVANCE_COURSE_EXAM_PRIMARY;


    //todo 以下是exam-stu schema中的表信息
    // -------------------------------------------------------------------------
    // IDENTITY definitions  exam-stu schema中答题流水表自增主键信息
    // -------------------------------------------------------------------------
    public static final Identity<AnswerRecordProcessRecord, Long> IDENTITY_ANSWER_RECORD_PROCESS = Identities0.IDENTITY_ANSWER_RECORD_PROCESS;
    public static final Identity<AnswerRecordProcess_0Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_0 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_0;
    public static final Identity<AnswerRecordProcess_1Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_1 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_1;
    public static final Identity<AnswerRecordProcess_2Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_2 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_2;
    public static final Identity<AnswerRecordProcess_3Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_3 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_3;
    public static final Identity<AnswerRecordProcess_4Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_4 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_4;
    public static final Identity<AnswerRecordProcess_5Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_5 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_5;
    public static final Identity<AnswerRecordProcess_6Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_6 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_6;
    public static final Identity<AnswerRecordProcess_7Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_7 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_7;
    public static final Identity<AnswerRecordProcess_8Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_8 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_8;
    public static final Identity<AnswerRecordProcess_9Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_9 = Identities0.IDENTITY_ANSWER_RECORD_PROCESS_9;

    public static final Identity<ExamPaperAttachmentRecord, Long> IDENTITY_EXAM_PAPER_ATTACHMENT = Identities0.IDENTITY_EXAM_PAPER_ATTACHMENT;
    public static final Identity<ExamOnlineLogRecord, Long> IDENTITY_EXAM_ONLINE_LOG = Identities0.IDENTITY_EXAM_ONLINE_LOG;

    public static final UniqueKey<ExamRecordRecord> KEY_T_EXAM_RECORD_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_PRIMARY;
    public static final UniqueKey<ExamRecord_2017Record> KEY_T_EXAM_RECORD_2017_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2017_PRIMARY;
    public static final UniqueKey<ExamRecord_2018Record> KEY_T_EXAM_RECORD_2018_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2018_PRIMARY;
    public static final UniqueKey<ExamRecord_2019Record> KEY_T_EXAM_RECORD_2019_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2019_PRIMARY;
    public static final UniqueKey<ExamRecord_2020Record> KEY_T_EXAM_RECORD_2020_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2020_PRIMARY;
    public static final UniqueKey<ExamRecord_2021Record> KEY_T_EXAM_RECORD_2021_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2021_PRIMARY;
    public static final UniqueKey<ExamRecord_2022Record> KEY_T_EXAM_RECORD_2022_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2022_PRIMARY;
    public static final UniqueKey<ExamRecord_2023Record> KEY_T_EXAM_RECORD_2023_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2023_PRIMARY;
    public static final UniqueKey<ExamRecord_2024Record> KEY_T_EXAM_RECORD_2024_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2024_PRIMARY;
    public static final UniqueKey<ExamRecord_2025Record> KEY_T_EXAM_RECORD_2025_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2025_PRIMARY;
    public static final UniqueKey<ExamRecord_2026Record> KEY_T_EXAM_RECORD_2026_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2026_PRIMARY;
    public static final UniqueKey<ExamRecord_2027Record> KEY_T_EXAM_RECORD_2027_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2027_PRIMARY;
    public static final UniqueKey<ExamRecord_2028Record> KEY_T_EXAM_RECORD_2028_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2028_PRIMARY;
    public static final UniqueKey<ExamRecord_2029Record> KEY_T_EXAM_RECORD_2029_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2029_PRIMARY;
    public static final UniqueKey<ExamRecord_2030Record> KEY_T_EXAM_RECORD_2030_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_2030_PRIMARY;
    public static final UniqueKey<ExamRecordFaceRecord> KEY_T_EXAM_RECORD_FACE_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2017Record> KEY_T_EXAM_RECORD_FACE_2017_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2017_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2018Record> KEY_T_EXAM_RECORD_FACE_2018_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2018_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2019Record> KEY_T_EXAM_RECORD_FACE_2019_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2019_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2020Record> KEY_T_EXAM_RECORD_FACE_2020_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2020_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2021Record> KEY_T_EXAM_RECORD_FACE_2021_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2021_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2022Record> KEY_T_EXAM_RECORD_FACE_2022_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2022_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2023Record> KEY_T_EXAM_RECORD_FACE_2023_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2023_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2024Record> KEY_T_EXAM_RECORD_FACE_2024_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2024_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2025Record> KEY_T_EXAM_RECORD_FACE_2025_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2025_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2026Record> KEY_T_EXAM_RECORD_FACE_2026_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2026_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2027Record> KEY_T_EXAM_RECORD_FACE_2027_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2027_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2028Record> KEY_T_EXAM_RECORD_FACE_2028_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2028_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2029Record> KEY_T_EXAM_RECORD_FACE_2029_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2029_PRIMARY;
    public static final UniqueKey<ExamRecordFace_2030Record> KEY_T_EXAM_RECORD_FACE_2030_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_FACE_2030_PRIMARY;
    public static final UniqueKey<ExamRegistRecord> KEY_T_EXAM_REGIST_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_PRIMARY;
    public static final UniqueKey<ExamRegist_2017Record> KEY_T_EXAM_REGIST_2017_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2017_PRIMARY;
    public static final UniqueKey<ExamRegist_2018Record> KEY_T_EXAM_REGIST_2018_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2018_PRIMARY;
    public static final UniqueKey<ExamRegist_2019Record> KEY_T_EXAM_REGIST_2019_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2019_PRIMARY;
    public static final UniqueKey<ExamRegist_2020Record> KEY_T_EXAM_REGIST_2020_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2020_PRIMARY;
    public static final UniqueKey<ExamRegist_2021Record> KEY_T_EXAM_REGIST_2021_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2021_PRIMARY;
    public static final UniqueKey<ExamRegist_2022Record> KEY_T_EXAM_REGIST_2022_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2022_PRIMARY;
    public static final UniqueKey<ExamRegist_2023Record> KEY_T_EXAM_REGIST_2023_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2023_PRIMARY;
    public static final UniqueKey<ExamRegist_2024Record> KEY_T_EXAM_REGIST_2024_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2024_PRIMARY;
    public static final UniqueKey<ExamRegist_2025Record> KEY_T_EXAM_REGIST_2025_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2025_PRIMARY;
    public static final UniqueKey<ExamRegist_2026Record> KEY_T_EXAM_REGIST_2026_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2026_PRIMARY;
    public static final UniqueKey<ExamRegist_2027Record> KEY_T_EXAM_REGIST_2027_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2027_PRIMARY;
    public static final UniqueKey<ExamRegist_2028Record> KEY_T_EXAM_REGIST_2028_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2028_PRIMARY;
    public static final UniqueKey<ExamRegist_2029Record> KEY_T_EXAM_REGIST_2029_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2029_PRIMARY;
    public static final UniqueKey<ExamRegist_2030Record> KEY_T_EXAM_REGIST_2030_PRIMARY = UniqueKeys0.KEY_T_EXAM_REGIST_2030_PRIMARY;
    public static final UniqueKey<AnswerRecordRecord> KEY_T_ANSWER_RECORD_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PRIMARY;
    public static final UniqueKey<AnswerRecord_2019Record> KEY_T_ANSWER_RECORD_2019_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2019_PRIMARY;
    public static final UniqueKey<AnswerRecord_2020Record> KEY_T_ANSWER_RECORD_2020_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2020_PRIMARY;
    public static final UniqueKey<AnswerRecord_2021Record> KEY_T_ANSWER_RECORD_2021_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2021_PRIMARY;
    public static final UniqueKey<AnswerRecord_2022Record> KEY_T_ANSWER_RECORD_2022_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2022_PRIMARY;
    public static final UniqueKey<AnswerRecord_2023Record> KEY_T_ANSWER_RECORD_2023_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2023_PRIMARY;
    public static final UniqueKey<AnswerRecord_2024Record> KEY_T_ANSWER_RECORD_2024_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2024_PRIMARY;
    public static final UniqueKey<AnswerRecord_2025Record> KEY_T_ANSWER_RECORD_2025_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2025_PRIMARY;
    public static final UniqueKey<AnswerRecord_2026Record> KEY_T_ANSWER_RECORD_2026_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2026_PRIMARY;
    public static final UniqueKey<AnswerRecord_2027Record> KEY_T_ANSWER_RECORD_2027_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2027_PRIMARY;
    public static final UniqueKey<AnswerRecord_2028Record> KEY_T_ANSWER_RECORD_2028_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2028_PRIMARY;
    public static final UniqueKey<AnswerRecord_2029Record> KEY_T_ANSWER_RECORD_2029_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2029_PRIMARY;
    public static final UniqueKey<AnswerRecord_2030Record> KEY_T_ANSWER_RECORD_2030_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_2030_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY;
//    public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_PRIMARY;
    public static final UniqueKey<ExamPaperAttachmentRecord> KEY_T_EXAM_PAPER_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_EXAM_PAPER_ATTACHMENT_PRIMARY;
    public static final UniqueKey<PersonalTemplateRecord> KEY_T_PERSONAL_TEMPLATE_PRIMARY = UniqueKeys0.KEY_T_PERSONAL_TEMPLATE_PRIMARY;
    public static final UniqueKey<SignupRecord> KEY_T_SIGNUP_PRIMARY = UniqueKeys0.KEY_T_SIGNUP_PRIMARY;
    public static final UniqueKey<SignupRecordRecord> KEY_T_SIGNUP_RECORD_PRIMARY = UniqueKeys0.KEY_T_SIGNUP_RECORD_PRIMARY;
    public static final UniqueKey<SignUpAuthRecord> KEY_T_SIGN_UP_AUTH_PRIMARY = UniqueKeys0.KEY_T_SIGN_UP_AUTH_PRIMARY;
    public static final UniqueKey<CloudSignupRecord> KEY_T_CLOUD_SIGNUP_PRIMARY = UniqueKeys0.KEY_T_CLOUD_SIGNUP_PRIMARY;
    public static final UniqueKey<GridSignupRecord> KEY_T_GRID_SIGNUP_PRIMARY = UniqueKeys0.KEY_T_GRID_SIGNUP_PRIMARY;
    public static final UniqueKey<ToDoRecord> KEY_T_TO_DO_PRIMARY = UniqueKeys0.KEY_T_TO_DO_PRIMARY;
    public static final UniqueKey<ExamOnlineLogRecord> KEY_T_EXAM_ONLINE_LOG_PRIMARY = UniqueKeys0.KEY_T_EXAM_ONLINE_LOG_PRIMARY;
    public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_PRIMARY;
    public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_0_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_1_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_2_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_3_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_4_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_5_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_6_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_7_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_8_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY;
    public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_UNIQ_QUESTION_ID_EXAM_RECORD_ID = UniqueKeys0.KEY_T_ANSWER_RECORD_PROCESS_9_UNIQ_QUESTION_ID_EXAM_RECORD_ID;
    public static final UniqueKey<DigitalIntelligenceResultRecord> KEY_T_DIGITAL_INTELLIGENCE_RESULT_PRIMARY = UniqueKeys0.KEY_T_DIGITAL_INTELLIGENCE_RESULT_PRIMARY;



    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class UniqueKeys0 extends AbstractKeys {
        //todo exam 和 exam-stu 表信息均在此类中按照shema分块进行维护
        //todo 以下是exam schema中的表信息
	    public static final UniqueKey<SubAuthenticatedExamGroupRecord> KEY_T_SUB_AUTHENTICATED_EXAM_GROUP_PRIMARY = createUniqueKey(SubAuthenticatedExamGroup.SUB_AUTHENTICATED_EXAM_GROUP, "KEY_t_sub_authenticated_exam_group_PRIMARY", SubAuthenticatedExamGroup.SUB_AUTHENTICATED_EXAM_GROUP.ID);
        public static final UniqueKey<SubAuthenticatedCancelCertificateRecordRecord> KEY_T_SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD_PRIMARY = createUniqueKey(SubAuthenticatedCancelCertificateRecord.SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD, "KEY_t_sub_authenticated_cancel_certificate_record_PRIMARY", SubAuthenticatedCancelCertificateRecord.SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD.ID);
        public static final UniqueKey<ActivityRecord> KEY_T_ACTIVITY_PRIMARY = createUniqueKey(Activity.ACTIVITY, "KEY_t_activity_PRIMARY", Activity.ACTIVITY.ID);
        public static final UniqueKey<AnnouncementRecord> KEY_T_ANNOUNCEMENT_PRIMARY = createUniqueKey(Announcement.ANNOUNCEMENT, "KEY_t_announcement_PRIMARY", Announcement.ANNOUNCEMENT.ID);
        public static final UniqueKey<AnswerRecordTempRecord> KEY_T_ANSWER_RECORD_TEMP_PRIMARY = createUniqueKey(AnswerRecordTemp.ANSWER_RECORD_TEMP, "KEY_t_answer_record_temp_PRIMARY", AnswerRecordTemp.ANSWER_RECORD_TEMP.ID);
        public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = createUniqueKey(AudienceItem.AUDIENCE_ITEM, "KEY_t_audience_item_PRIMARY", AudienceItem.AUDIENCE_ITEM.ID);
        public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = createUniqueKey(AudienceMember.AUDIENCE_MEMBER, "KEY_t_audience_member_PRIMARY", AudienceMember.AUDIENCE_MEMBER.ID);
        public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = createUniqueKey(AudienceObject.AUDIENCE_OBJECT, "KEY_t_audience_object_PRIMARY", AudienceObject.AUDIENCE_OBJECT.ID);
        public static final UniqueKey<BusinessResearchRecord> KEY_T_BUSINESS_RESEARCH_PRIMARY = createUniqueKey(BusinessResearch.BUSINESS_RESEARCH, "KEY_t_business_research_PRIMARY", BusinessResearch.BUSINESS_RESEARCH.ID);
        public static final UniqueKey<BusinessTopicRecord> KEY_T_BUSINESS_TOPIC_PRIMARY = createUniqueKey(BusinessTopic.BUSINESS_TOPIC, "KEY_t_business_topic_PRIMARY", BusinessTopic.BUSINESS_TOPIC.ID);
        public static final UniqueKey<CertificateHistoryRecord> KEY_T_CERTIFICATE_HISTORY_PRIMARY = createUniqueKey(CertificateHistory.CERTIFICATE_HISTORY, "KEY_t_certificate_history_PRIMARY", CertificateHistory.CERTIFICATE_HISTORY.ID);
        public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_PRIMARY = createUniqueKey(CertificateRecord.CERTIFICATE_RECORD, "KEY_t_certificate_record_PRIMARY", CertificateRecord.CERTIFICATE_RECORD.ID);
        public static final UniqueKey<CloudAnnouncementRecord> KEY_T_CLOUD_ANNOUNCEMENT_PRIMARY = createUniqueKey(CloudAnnouncement.CLOUD_ANNOUNCEMENT, "KEY_t_cloud_announcement_PRIMARY", CloudAnnouncement.CLOUD_ANNOUNCEMENT.ID);
        public static final UniqueKey<CloudExamRecord> KEY_T_CLOUD_EXAM_PRIMARY = createUniqueKey(CloudExam.CLOUD_EXAM, "KEY_t_cloud_exam_PRIMARY", CloudExam.CLOUD_EXAM.ID);
        public static final UniqueKey<CloudLevelRecord> KEY_T_CLOUD_LEVEL_PRIMARY = createUniqueKey(CloudLevel.CLOUD_LEVEL, "KEY_t_cloud_level_PRIMARY", CloudLevel.CLOUD_LEVEL.ID);
        public static final UniqueKey<CloudProfessionRecord> KEY_T_CLOUD_PROFESSION_PRIMARY = createUniqueKey(CloudProfession.CLOUD_PROFESSION, "KEY_t_cloud_profession_PRIMARY", CloudProfession.CLOUD_PROFESSION.ID);
        public static final UniqueKey<DimensionRecord> KEY_T_DIMENSION_PRIMARY = createUniqueKey(Dimension.DIMENSION, "KEY_t_dimension_PRIMARY", Dimension.DIMENSION.ID);
        public static final UniqueKey<DimensionQuestionRecord> KEY_T_DIMENSION_QUESTION_PRIMARY = createUniqueKey(DimensionQuestion.DIMENSION_QUESTION, "KEY_t_dimension_question_PRIMARY", DimensionQuestion.DIMENSION_QUESTION.ID);
        public static final UniqueKey<EquipmentTypeRecord> KEY_T_EQUIPMENT_TYPE_PRIMARY = createUniqueKey(EquipmentType.EQUIPMENT_TYPE, "KEY_t_equipment_type_PRIMARY", EquipmentType.EQUIPMENT_TYPE.ID);
        public static final UniqueKey<ErrorReasonRecord> KEY_T_ERROR_REASON_PRIMARY = createUniqueKey(ErrorReason.ERROR_REASON, "KEY_t_error_reason_PRIMARY", ErrorReason.ERROR_REASON.ID);
        public static final UniqueKey<ExamRecord> KEY_T_EXAM_PRIMARY = createUniqueKey(com.zxy.product.exam.jooq.tables.Exam.EXAM, "KEY_t_exam_PRIMARY", Exam.EXAM.ID);
        public static final UniqueKey<ExamNoticeRecord> KEY_T_EXAM_NOTICE_PRIMARY = createUniqueKey(ExamNotice.EXAM_NOTICE, "KEY_t_exam_notice_PRIMARY", ExamNotice.EXAM_NOTICE.ID);
        public static final UniqueKey<ExamTopicRecord> KEY_T_EXAM_TOPIC_PRIMARY = createUniqueKey(ExamTopic.EXAM_TOPIC, "KEY_t_exam_topic_PRIMARY", ExamTopic.EXAM_TOPIC.ID);
        public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = createUniqueKey(GrantDetail.GRANT_DETAIL, "KEY_t_grant_detail_PRIMARY", GrantDetail.GRANT_DETAIL.ID);
        public static final UniqueKey<GridAuditRecord> KEY_T_GRID_AUDIT_PRIMARY = createUniqueKey(GridAudit.GRID_AUDIT, "KEY_t_grid_audit_PRIMARY", GridAudit.GRID_AUDIT.ID);
		public static final UniqueKey<GridCourseRecord> KEY_T_GRID_COURSE_PRIMARY = createUniqueKey(GridCourse.GRID_COURSE, "KEY_t_grid_course_PRIMARY", GridCourse.GRID_COURSE.ID);
        public static final UniqueKey<GridCourseExamRecord> KEY_T_GRID_COURSE_EXAM_PRIMARY = createUniqueKey(GridCourseExam.GRID_COURSE_EXAM, "KEY_t_grid_course_exam_PRIMARY", GridCourseExam.GRID_COURSE_EXAM.ID);
        public static final UniqueKey<GridFileRecord> KEY_T_GRID_FILE_PRIMARY = createUniqueKey(GridFile.GRID_FILE, "KEY_t_grid_file_PRIMARY", GridFile.GRID_FILE.ID);
		public static final UniqueKey<GridFormalExamRecord> KEY_T_GRID_FORMAL_EXAM_PRIMARY = createUniqueKey(GridFormalExam.GRID_FORMAL_EXAM, "KEY_t_grid_formal_exam_PRIMARY", GridFormalExam.GRID_FORMAL_EXAM.ID);
		public static final UniqueKey<GridLevelRecord> KEY_T_GRID_LEVEL_PRIMARY = createUniqueKey(GridLevel.GRID_LEVEL, "KEY_t_grid_level_PRIMARY", GridLevel.GRID_LEVEL.ID);
		public static final UniqueKey<InvigilatorRecord> KEY_T_INVIGILATOR_PRIMARY = createUniqueKey(Invigilator.INVIGILATOR, "KEY_t_invigilator_PRIMARY", Invigilator.INVIGILATOR.ID);
        public static final UniqueKey<InvigilatorGrantRecord> KEY_T_INVIGILATOR_GRANT_PRIMARY = createUniqueKey(InvigilatorGrant.INVIGILATOR_GRANT, "KEY_t_invigilator_grant_PRIMARY", InvigilatorGrant.INVIGILATOR_GRANT.ID);
        public static final UniqueKey<InvigilatorGrantDetailRecord> KEY_T_INVIGILATOR_GRANT_DETAIL_PRIMARY = createUniqueKey(InvigilatorGrantDetail.INVIGILATOR_GRANT_DETAIL, "KEY_t_invigilator_grant_detail_PRIMARY", InvigilatorGrantDetail.INVIGILATOR_GRANT_DETAIL.ID);
        public static final UniqueKey<MarkConfigRecord> KEY_T_MARK_CONFIG_PRIMARY = createUniqueKey(MarkConfig.MARK_CONFIG, "KEY_t_mark_config_PRIMARY", MarkConfig.MARK_CONFIG.ID);
        public static final UniqueKey<MarkRecordRecord> KEY_T_MARK_RECORD_PRIMARY = createUniqueKey(MarkRecord.MARK_RECORD, "KEY_t_mark_record_PRIMARY", MarkRecord.MARK_RECORD.ID);
        public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = createUniqueKey(Member.MEMBER, "KEY_t_member_PRIMARY", Member.MEMBER.ID);
        public static final UniqueKey<ModifyAnswerRecordRecord> KEY_T_MODIFY_ANSWER_RECORD_PRIMARY = createUniqueKey(ModifyAnswerRecord.MODIFY_ANSWER_RECORD, "KEY_t_modify_answer_record_PRIMARY", ModifyAnswerRecord.MODIFY_ANSWER_RECORD.ID);
        public static final UniqueKey<OldQuestionRecord> KEY_T_OLD_QUESTION_PRIMARY = createUniqueKey(OldQuestion.OLD_QUESTION, "KEY_t_old_question_PRIMARY", OldQuestion.OLD_QUESTION.ID);
        public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = createUniqueKey(Organization.ORGANIZATION, "KEY_t_organization_PRIMARY", Organization.ORGANIZATION.ID);
        public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = createUniqueKey(OrganizationDetail.ORGANIZATION_DETAIL, "KEY_t_organization_detail_PRIMARY", OrganizationDetail.ORGANIZATION_DETAIL.ID);
        public static final UniqueKey<PaperClassRecord> KEY_T_PAPER_CLASS_PRIMARY = createUniqueKey(PaperClass.PAPER_CLASS, "KEY_t_paper_class_PRIMARY", PaperClass.PAPER_CLASS.ID);
        public static final UniqueKey<PaperClassQuestionRecord> KEY_T_PAPER_CLASS_QUESTION_PRIMARY = createUniqueKey(PaperClassQuestion.PAPER_CLASS_QUESTION, "KEY_t_paper_class_question_PRIMARY", PaperClassQuestion.PAPER_CLASS_QUESTION.ID);
        public static final UniqueKey<PaperClassTacticRecord> KEY_T_PAPER_CLASS_TACTIC_PRIMARY = createUniqueKey(PaperClassTactic.PAPER_CLASS_TACTIC, "KEY_t_paper_class_tactic_PRIMARY", PaperClassTactic.PAPER_CLASS_TACTIC.ID);
        public static final UniqueKey<PaperInstanceRecord> KEY_T_PAPER_INSTANCE_PRIMARY = createUniqueKey(PaperInstance.PAPER_INSTANCE, "KEY_t_paper_instance_PRIMARY", PaperInstance.PAPER_INSTANCE.ID);
        public static final UniqueKey<PaperInstanceQuestionCopyRecord> KEY_T_PAPER_INSTANCE_QUESTION_COPY_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy.PAPER_INSTANCE_QUESTION_COPY, "KEY_t_paper_instance_question_copy_PRIMARY", PaperInstanceQuestionCopy.PAPER_INSTANCE_QUESTION_COPY.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2017Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2017_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2017.PAPER_INSTANCE_QUESTION_COPY_2017, "KEY_t_paper_instance_question_copy_2017_PRIMARY", PaperInstanceQuestionCopy_2017.PAPER_INSTANCE_QUESTION_COPY_2017.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2018Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2018_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2018.PAPER_INSTANCE_QUESTION_COPY_2018, "KEY_t_paper_instance_question_copy_2018_PRIMARY", PaperInstanceQuestionCopy_2018.PAPER_INSTANCE_QUESTION_COPY_2018.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2019Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2019_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2019.PAPER_INSTANCE_QUESTION_COPY_2019, "KEY_t_paper_instance_question_copy_2019_PRIMARY", PaperInstanceQuestionCopy_2019.PAPER_INSTANCE_QUESTION_COPY_2019.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2020Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2020_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2020.PAPER_INSTANCE_QUESTION_COPY_2020, "KEY_t_paper_instance_question_copy_2020_PRIMARY", PaperInstanceQuestionCopy_2020.PAPER_INSTANCE_QUESTION_COPY_2020.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2021Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2021_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2021.PAPER_INSTANCE_QUESTION_COPY_2021, "KEY_t_paper_instance_question_copy_2021_PRIMARY", PaperInstanceQuestionCopy_2021.PAPER_INSTANCE_QUESTION_COPY_2021.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2022Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2022_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2022.PAPER_INSTANCE_QUESTION_COPY_2022, "KEY_t_paper_instance_question_copy_2022_PRIMARY", PaperInstanceQuestionCopy_2022.PAPER_INSTANCE_QUESTION_COPY_2022.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2023Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2023_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2023.PAPER_INSTANCE_QUESTION_COPY_2023, "KEY_t_paper_instance_question_copy_2023_PRIMARY", PaperInstanceQuestionCopy_2023.PAPER_INSTANCE_QUESTION_COPY_2023.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2024Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2024_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2024.PAPER_INSTANCE_QUESTION_COPY_2024, "KEY_t_paper_instance_question_copy_2024_PRIMARY", PaperInstanceQuestionCopy_2024.PAPER_INSTANCE_QUESTION_COPY_2024.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2025Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2025_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2025.PAPER_INSTANCE_QUESTION_COPY_2025, "KEY_t_paper_instance_question_copy_2025_PRIMARY", PaperInstanceQuestionCopy_2025.PAPER_INSTANCE_QUESTION_COPY_2025.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2026Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2026_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2026.PAPER_INSTANCE_QUESTION_COPY_2026, "KEY_t_paper_instance_question_copy_2026_PRIMARY", PaperInstanceQuestionCopy_2026.PAPER_INSTANCE_QUESTION_COPY_2026.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2027Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2027_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2027.PAPER_INSTANCE_QUESTION_COPY_2027, "KEY_t_paper_instance_question_copy_2027_PRIMARY", PaperInstanceQuestionCopy_2027.PAPER_INSTANCE_QUESTION_COPY_2027.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2028Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2028_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2028.PAPER_INSTANCE_QUESTION_COPY_2028, "KEY_t_paper_instance_question_copy_2028_PRIMARY", PaperInstanceQuestionCopy_2028.PAPER_INSTANCE_QUESTION_COPY_2028.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2029Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2029_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2029.PAPER_INSTANCE_QUESTION_COPY_2029, "KEY_t_paper_instance_question_copy_2029_PRIMARY", PaperInstanceQuestionCopy_2029.PAPER_INSTANCE_QUESTION_COPY_2029.ID);
        public static final UniqueKey<PaperInstanceQuestionCopy_2030Record> KEY_T_PAPER_INSTANCE_QUESTION_COPY_2030_PRIMARY = createUniqueKey(PaperInstanceQuestionCopy_2030.PAPER_INSTANCE_QUESTION_COPY_2030, "KEY_t_paper_instance_question_copy_2030_PRIMARY", PaperInstanceQuestionCopy_2030.PAPER_INSTANCE_QUESTION_COPY_2030.ID);
        public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = createUniqueKey(PccwResult.PCCW_RESULT, "KEY_t_pccw_result_PRIMARY", PccwResult.PCCW_RESULT.ID);
        public static final UniqueKey<PersonalDepotRecord> KEY_T_PERSONAL_DEPOT_PRIMARY = createUniqueKey(PersonalDepot.PERSONAL_DEPOT, "KEY_t_personal_depot_PRIMARY", PersonalDepot.PERSONAL_DEPOT.ID);
		public static final UniqueKey<ProfessionRecord> KEY_T_PROFESSION_PRIMARY = createUniqueKey(Profession.PROFESSION, "KEY_t_profession_PRIMARY", Profession.PROFESSION.ID);
        public static final UniqueKey<ProfessionLevelRecord> KEY_T_PROFESSION_LEVEL_PRIMARY = createUniqueKey(ProfessionLevel.PROFESSION_LEVEL, "KEY_t_profession_level_PRIMARY", ProfessionLevel.PROFESSION_LEVEL.ID);
        public static final UniqueKey<QuestionRecord> KEY_T_QUESTION_PRIMARY = createUniqueKey(Question.QUESTION, "KEY_t_question_PRIMARY", Question.QUESTION.ID);
        public static final UniqueKey<QuestionAttrRecord> KEY_T_QUESTION_ATTR_PRIMARY = createUniqueKey(QuestionAttr.QUESTION_ATTR, "KEY_t_question_attr_PRIMARY", QuestionAttr.QUESTION_ATTR.ID);
        public static final UniqueKey<QuestionAttrCopyRecord> KEY_T_QUESTION_ATTR_COPY_PRIMARY = createUniqueKey(QuestionAttrCopy.QUESTION_ATTR_COPY, "KEY_t_question_attr_copy_PRIMARY", QuestionAttrCopy.QUESTION_ATTR_COPY.ID);
        public static final UniqueKey<QuestionCopyRecord> KEY_T_QUESTION_COPY_PRIMARY = createUniqueKey(QuestionCopy.QUESTION_COPY, "KEY_t_question_copy_PRIMARY", QuestionCopy.QUESTION_COPY.ID);
        public static final UniqueKey<QuestionCountRecord> KEY_T_QUESTION_COUNT_PRIMARY = createUniqueKey(QuestionCount.QUESTION_COUNT, "KEY_t_question_count_PRIMARY", QuestionCount.QUESTION_COUNT.ID);
        public static final UniqueKey<QuestionDepotRecord> KEY_T_QUESTION_DEPOT_PRIMARY = createUniqueKey(QuestionDepot.QUESTION_DEPOT, "KEY_t_question_depot_PRIMARY", QuestionDepot.QUESTION_DEPOT.ID);
        public static final UniqueKey<QuestionErrorRateRecord> KEY_T_QUESTION_ERROR_RATE_PRIMARY = createUniqueKey(QuestionErrorRate.QUESTION_ERROR_RATE, "KEY_t_question_error_rate_PRIMARY", QuestionErrorRate.QUESTION_ERROR_RATE.ID);
        public static final UniqueKey<QuestionRecoveryRecord> KEY_T_QUESTION_RECOVERY_PRIMARY = createUniqueKey(QuestionRecovery.QUESTION_RECOVERY, "KEY_t_question_recovery_PRIMARY", QuestionRecovery.QUESTION_RECOVERY.ID);
        public static final UniqueKey<ResearchAnswerRecordRecord> KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY = createUniqueKey(ResearchAnswerRecord.RESEARCH_ANSWER_RECORD, "KEY_t_research_answer_record_PRIMARY", ResearchAnswerRecord.RESEARCH_ANSWER_RECORD.ID);
        public static final UniqueKey<ResearchHistoryResultRecord> KEY_T_RESEARCH_HISTORY_RESULT_PRIMARY = createUniqueKey(ResearchHistoryResult.RESEARCH_HISTORY_RESULT, "KEY_t_research_history_result_PRIMARY", ResearchHistoryResult.RESEARCH_HISTORY_RESULT.ID);
        public static final UniqueKey<ResearchQuestionaryRecord> KEY_T_RESEARCH_QUESTIONARY_PRIMARY = createUniqueKey(ResearchQuestionary.RESEARCH_QUESTIONARY, "KEY_t_research_questionary_PRIMARY", ResearchQuestionary.RESEARCH_QUESTIONARY.ID);
        public static final UniqueKey<ResearchRecordRecord> KEY_T_RESEARCH_RECORD_PRIMARY = createUniqueKey(ResearchRecord.RESEARCH_RECORD, "KEY_t_research_record_PRIMARY", ResearchRecord.RESEARCH_RECORD.ID);
        public static final UniqueKey<ResearchSummaryRecord> KEY_T_RESEARCH_SUMMARY_PRIMARY = createUniqueKey(ResearchSummary.RESEARCH_SUMMARY, "KEY_t_research_summary_PRIMARY", ResearchSummary.RESEARCH_SUMMARY.ID);
        public static final UniqueKey<SyncInfoRecord> KEY_T_SYNC_INFO_PRIMARY = createUniqueKey(SyncInfo.SYNC_INFO, "KEY_t_sync_info_PRIMARY", SyncInfo.SYNC_INFO.ID);
        public static final UniqueKey<TodayQuestionRecord> KEY_T_TODAY_QUESTION_PRIMARY = createUniqueKey(TodayQuestion.TODAY_QUESTION, "KEY_t_today_question_PRIMARY", TodayQuestion.TODAY_QUESTION.ID);
        public static final UniqueKey<ExamStudyPlanConfigRecord> KEY_T_EXAM_STUDY_PLAN_CONFIG_PRIMARY = createUniqueKey(ExamStudyPlanConfig.EXAM_STUDY_PLAN_CONFIG, "KEY_t_exam_study_plan_config_PRIMARY", ExamStudyPlanConfig.EXAM_STUDY_PLAN_CONFIG.ID);
        public static final UniqueKey<ArchivedExamRecord> KEY_T_ARCHIVED_EXAM_PRIMARY = createUniqueKey(ArchivedExam.ARCHIVED_EXAM, "KEY_t_archived_exam_PRIMARY", ArchivedExam.ARCHIVED_EXAM.ID);
        public static final UniqueKey<DeleteDataExamRecord> KEY_T_DELETE_DATA_EXAM_PRIMARY = createUniqueKey(DeleteDataExam.DELETE_DATA_EXAM, "KEY_t_delete_data_exam_PRIMARY", DeleteDataExam.DELETE_DATA_EXAM.ID);
        public static final UniqueKey<LearningStyleEvaluationRecord> KEY_T_LEARNING_STYLE_EVALUATION_PRIMARY = createUniqueKey(LearningStyleEvaluation.LEARNING_STYLE_EVALUATION, "KEY_t_learning_style_evaluation_PRIMARY", LearningStyleEvaluation.LEARNING_STYLE_EVALUATION.ID);
        public static final UniqueKey<LearningStyleEvaluationRecord> KEY_T_LEARNING_STYLE_EVALUATION_IDX_MEMBER_ID = createUniqueKey(LearningStyleEvaluation.LEARNING_STYLE_EVALUATION, "KEY_t_learning_style_evaluation_idx_member_id", LearningStyleEvaluation.LEARNING_STYLE_EVALUATION.MEMBER_ID);
        public static final UniqueKey<RelevanceCourseExamRecord> KEY_T_RELEVANCE_COURSE_EXAM_PRIMARY = createUniqueKey(RelevanceCourseExam.RELEVANCE_COURSE_EXAM, "KEY_t_relevance_course_exam_PRIMARY", RelevanceCourseExam.RELEVANCE_COURSE_EXAM.ID);

        //todo  以下是exam-stu schema中的表信息
        public static final UniqueKey<AnswerRecordRecord> KEY_T_ANSWER_RECORD_PRIMARY = createUniqueKey(AnswerRecord.ANSWER_RECORD, "KEY_t_answer_record_PRIMARY", AnswerRecord.ANSWER_RECORD.ID);
        public static final UniqueKey<AnswerRecord_2019Record> KEY_T_ANSWER_RECORD_2019_PRIMARY = createUniqueKey(AnswerRecord_2019.ANSWER_RECORD_2019, "KEY_t_answer_record_2019_PRIMARY", AnswerRecord_2019.ANSWER_RECORD_2019.ID);
        public static final UniqueKey<AnswerRecord_2020Record> KEY_T_ANSWER_RECORD_2020_PRIMARY = createUniqueKey(AnswerRecord_2020.ANSWER_RECORD_2020, "KEY_t_answer_record_2020_PRIMARY", AnswerRecord_2020.ANSWER_RECORD_2020.ID);
        public static final UniqueKey<AnswerRecord_2021Record> KEY_T_ANSWER_RECORD_2021_PRIMARY = createUniqueKey(AnswerRecord_2021.ANSWER_RECORD_2021, "KEY_t_answer_record_2021_PRIMARY", AnswerRecord_2021.ANSWER_RECORD_2021.ID);
        public static final UniqueKey<AnswerRecord_2022Record> KEY_T_ANSWER_RECORD_2022_PRIMARY = createUniqueKey(AnswerRecord_2022.ANSWER_RECORD_2022, "KEY_t_answer_record_2022_PRIMARY", AnswerRecord_2022.ANSWER_RECORD_2022.ID);
        public static final UniqueKey<AnswerRecord_2023Record> KEY_T_ANSWER_RECORD_2023_PRIMARY = createUniqueKey(AnswerRecord_2023.ANSWER_RECORD_2023, "KEY_t_answer_record_2023_PRIMARY", AnswerRecord_2023.ANSWER_RECORD_2023.ID);
        public static final UniqueKey<AnswerRecord_2024Record> KEY_T_ANSWER_RECORD_2024_PRIMARY = createUniqueKey(AnswerRecord_2024.ANSWER_RECORD_2024, "KEY_t_answer_record_2024_PRIMARY", AnswerRecord_2024.ANSWER_RECORD_2024.ID);
        public static final UniqueKey<AnswerRecord_2025Record> KEY_T_ANSWER_RECORD_2025_PRIMARY = createUniqueKey(AnswerRecord_2025.ANSWER_RECORD_2025, "KEY_t_answer_record_2025_PRIMARY", AnswerRecord_2025.ANSWER_RECORD_2025.ID);
        public static final UniqueKey<AnswerRecord_2026Record> KEY_T_ANSWER_RECORD_2026_PRIMARY = createUniqueKey(AnswerRecord_2026.ANSWER_RECORD_2026, "KEY_t_answer_record_2026_PRIMARY", AnswerRecord_2026.ANSWER_RECORD_2026.ID);
        public static final UniqueKey<AnswerRecord_2027Record> KEY_T_ANSWER_RECORD_2027_PRIMARY = createUniqueKey(AnswerRecord_2027.ANSWER_RECORD_2027, "KEY_t_answer_record_2027_PRIMARY", AnswerRecord_2027.ANSWER_RECORD_2027.ID);
        public static final UniqueKey<AnswerRecord_2028Record> KEY_T_ANSWER_RECORD_2028_PRIMARY = createUniqueKey(AnswerRecord_2028.ANSWER_RECORD_2028, "KEY_t_answer_record_2028_PRIMARY", AnswerRecord_2028.ANSWER_RECORD_2028.ID);
        public static final UniqueKey<AnswerRecord_2029Record> KEY_T_ANSWER_RECORD_2029_PRIMARY = createUniqueKey(AnswerRecord_2029.ANSWER_RECORD_2029, "KEY_t_answer_record_2029_PRIMARY", AnswerRecord_2029.ANSWER_RECORD_2029.ID);
        public static final UniqueKey<AnswerRecord_2030Record> KEY_T_ANSWER_RECORD_2030_PRIMARY = createUniqueKey(AnswerRecord_2030.ANSWER_RECORD_2030, "KEY_t_answer_record_2030_PRIMARY", AnswerRecord_2030.ANSWER_RECORD_2030.ID);
//        public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY = createUniqueKey(AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0, "KEY_t_answer_record_process_0_PRIMARY", AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0.ID);
//        public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY = createUniqueKey(AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1, "KEY_t_answer_record_process_1_PRIMARY", AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1.ID);
//        public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY = createUniqueKey(AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2, "KEY_t_answer_record_process_2_PRIMARY", AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2.ID);
//        public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY = createUniqueKey(AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3, "KEY_t_answer_record_process_3_PRIMARY", AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3.ID);
//        public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY = createUniqueKey(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4, "KEY_t_answer_record_process_4_PRIMARY", AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.ID);
//        public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY = createUniqueKey(AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5, "KEY_t_answer_record_process_5_PRIMARY", AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5.ID);
//        public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY = createUniqueKey(AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6, "KEY_t_answer_record_process_6_PRIMARY", AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6.ID);
//        public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY = createUniqueKey(AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7, "KEY_t_answer_record_process_7_PRIMARY", AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7.ID);
//        public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY = createUniqueKey(AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8, "KEY_t_answer_record_process_8_PRIMARY", AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8.ID);
//        public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY = createUniqueKey(AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9, "KEY_t_answer_record_process_9_PRIMARY", AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9.ID);
//        public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_PRIMARY = createUniqueKey(AnswerRecordProcess.ANSWER_RECORD_PROCESS, "KEY_t_answer_record_process_PRIMARY", AnswerRecordProcess.ANSWER_RECORD_PROCESS.ID);
        public static final UniqueKey<ExamRecordRecord> KEY_T_EXAM_RECORD_PRIMARY = createUniqueKey(com.zxy.product.exam.jooq.tables.ExamRecord.EXAM_RECORD, "KEY_t_exam_record_PRIMARY", com.zxy.product.exam.jooq.tables.ExamRecord.EXAM_RECORD.ID);
        public static final UniqueKey<ExamRecord_2017Record> KEY_T_EXAM_RECORD_2017_PRIMARY = createUniqueKey(ExamRecord_2017.EXAM_RECORD_2017, "KEY_t_exam_record_2017_PRIMARY", ExamRecord_2017.EXAM_RECORD_2017.ID);
        public static final UniqueKey<ExamRecord_2018Record> KEY_T_EXAM_RECORD_2018_PRIMARY = createUniqueKey(ExamRecord_2018.EXAM_RECORD_2018, "KEY_t_exam_record_2018_PRIMARY", ExamRecord_2018.EXAM_RECORD_2018.ID);
        public static final UniqueKey<ExamRecord_2019Record> KEY_T_EXAM_RECORD_2019_PRIMARY = createUniqueKey(ExamRecord_2019.EXAM_RECORD_2019, "KEY_t_exam_record_2019_PRIMARY", ExamRecord_2019.EXAM_RECORD_2019.ID);
        public static final UniqueKey<ExamRecord_2020Record> KEY_T_EXAM_RECORD_2020_PRIMARY = createUniqueKey(ExamRecord_2020.EXAM_RECORD_2020, "KEY_t_exam_record_2020_PRIMARY", ExamRecord_2020.EXAM_RECORD_2020.ID);
        public static final UniqueKey<ExamRecord_2021Record> KEY_T_EXAM_RECORD_2021_PRIMARY = createUniqueKey(ExamRecord_2021.EXAM_RECORD_2021, "KEY_t_exam_record_2021_PRIMARY", ExamRecord_2021.EXAM_RECORD_2021.ID);
        public static final UniqueKey<ExamRecord_2022Record> KEY_T_EXAM_RECORD_2022_PRIMARY = createUniqueKey(ExamRecord_2022.EXAM_RECORD_2022, "KEY_t_exam_record_2022_PRIMARY", ExamRecord_2022.EXAM_RECORD_2022.ID);
        public static final UniqueKey<ExamRecord_2023Record> KEY_T_EXAM_RECORD_2023_PRIMARY = createUniqueKey(ExamRecord_2023.EXAM_RECORD_2023, "KEY_t_exam_record_2023_PRIMARY", ExamRecord_2023.EXAM_RECORD_2023.ID);
        public static final UniqueKey<ExamRecord_2024Record> KEY_T_EXAM_RECORD_2024_PRIMARY = createUniqueKey(ExamRecord_2024.EXAM_RECORD_2024, "KEY_t_exam_record_2024_PRIMARY", ExamRecord_2024.EXAM_RECORD_2024.ID);
        public static final UniqueKey<ExamRecord_2025Record> KEY_T_EXAM_RECORD_2025_PRIMARY = createUniqueKey(ExamRecord_2025.EXAM_RECORD_2025, "KEY_t_exam_record_2025_PRIMARY", ExamRecord_2025.EXAM_RECORD_2025.ID);
        public static final UniqueKey<ExamRecord_2026Record> KEY_T_EXAM_RECORD_2026_PRIMARY = createUniqueKey(ExamRecord_2026.EXAM_RECORD_2026, "KEY_t_exam_record_2026_PRIMARY", ExamRecord_2026.EXAM_RECORD_2026.ID);
        public static final UniqueKey<ExamRecord_2027Record> KEY_T_EXAM_RECORD_2027_PRIMARY = createUniqueKey(ExamRecord_2027.EXAM_RECORD_2027, "KEY_t_exam_record_2027_PRIMARY", ExamRecord_2027.EXAM_RECORD_2027.ID);
        public static final UniqueKey<ExamRecord_2028Record> KEY_T_EXAM_RECORD_2028_PRIMARY = createUniqueKey(ExamRecord_2028.EXAM_RECORD_2028, "KEY_t_exam_record_2028_PRIMARY", ExamRecord_2028.EXAM_RECORD_2028.ID);
        public static final UniqueKey<ExamRecord_2029Record> KEY_T_EXAM_RECORD_2029_PRIMARY = createUniqueKey(ExamRecord_2029.EXAM_RECORD_2029, "KEY_t_exam_record_2029_PRIMARY", ExamRecord_2029.EXAM_RECORD_2029.ID);
        public static final UniqueKey<ExamRecord_2030Record> KEY_T_EXAM_RECORD_2030_PRIMARY = createUniqueKey(ExamRecord_2030.EXAM_RECORD_2030, "KEY_t_exam_record_2030_PRIMARY", ExamRecord_2030.EXAM_RECORD_2030.ID);
        public static final UniqueKey<ExamRecordFaceRecord> KEY_T_EXAM_RECORD_FACE_PRIMARY = createUniqueKey(ExamRecordFace.EXAM_RECORD_FACE, "KEY_t_exam_record_face_PRIMARY", ExamRecordFace.EXAM_RECORD_FACE.ID);
        public static final UniqueKey<ExamRecordFace_2017Record> KEY_T_EXAM_RECORD_FACE_2017_PRIMARY = createUniqueKey(ExamRecordFace_2017.EXAM_RECORD_FACE_2017, "KEY_t_exam_record_face_2017_PRIMARY", ExamRecordFace_2017.EXAM_RECORD_FACE_2017.ID);
        public static final UniqueKey<ExamRecordFace_2018Record> KEY_T_EXAM_RECORD_FACE_2018_PRIMARY = createUniqueKey(ExamRecordFace_2018.EXAM_RECORD_FACE_2018, "KEY_t_exam_record_face_2018_PRIMARY", ExamRecordFace_2018.EXAM_RECORD_FACE_2018.ID);
        public static final UniqueKey<ExamRecordFace_2019Record> KEY_T_EXAM_RECORD_FACE_2019_PRIMARY = createUniqueKey(ExamRecordFace_2019.EXAM_RECORD_FACE_2019, "KEY_t_exam_record_face_2019_PRIMARY", ExamRecordFace_2019.EXAM_RECORD_FACE_2019.ID);
        public static final UniqueKey<ExamRecordFace_2020Record> KEY_T_EXAM_RECORD_FACE_2020_PRIMARY = createUniqueKey(ExamRecordFace_2020.EXAM_RECORD_FACE_2020, "KEY_t_exam_record_face_2020_PRIMARY", ExamRecordFace_2020.EXAM_RECORD_FACE_2020.ID);
        public static final UniqueKey<ExamRecordFace_2021Record> KEY_T_EXAM_RECORD_FACE_2021_PRIMARY = createUniqueKey(ExamRecordFace_2021.EXAM_RECORD_FACE_2021, "KEY_t_exam_record_face_2021_PRIMARY", ExamRecordFace_2021.EXAM_RECORD_FACE_2021.ID);
        public static final UniqueKey<ExamRecordFace_2022Record> KEY_T_EXAM_RECORD_FACE_2022_PRIMARY = createUniqueKey(ExamRecordFace_2022.EXAM_RECORD_FACE_2022, "KEY_t_exam_record_face_2022_PRIMARY", ExamRecordFace_2022.EXAM_RECORD_FACE_2022.ID);
        public static final UniqueKey<ExamRecordFace_2023Record> KEY_T_EXAM_RECORD_FACE_2023_PRIMARY = createUniqueKey(ExamRecordFace_2023.EXAM_RECORD_FACE_2023, "KEY_t_exam_record_face_2023_PRIMARY", ExamRecordFace_2023.EXAM_RECORD_FACE_2023.ID);
        public static final UniqueKey<ExamRecordFace_2024Record> KEY_T_EXAM_RECORD_FACE_2024_PRIMARY = createUniqueKey(ExamRecordFace_2024.EXAM_RECORD_FACE_2024, "KEY_t_exam_record_face_2024_PRIMARY", ExamRecordFace_2024.EXAM_RECORD_FACE_2024.ID);
        public static final UniqueKey<ExamRecordFace_2025Record> KEY_T_EXAM_RECORD_FACE_2025_PRIMARY = createUniqueKey(ExamRecordFace_2025.EXAM_RECORD_FACE_2025, "KEY_t_exam_record_face_2025_PRIMARY", ExamRecordFace_2025.EXAM_RECORD_FACE_2025.ID);
        public static final UniqueKey<ExamRecordFace_2026Record> KEY_T_EXAM_RECORD_FACE_2026_PRIMARY = createUniqueKey(ExamRecordFace_2026.EXAM_RECORD_FACE_2026, "KEY_t_exam_record_face_2026_PRIMARY", ExamRecordFace_2026.EXAM_RECORD_FACE_2026.ID);
        public static final UniqueKey<ExamRecordFace_2027Record> KEY_T_EXAM_RECORD_FACE_2027_PRIMARY = createUniqueKey(ExamRecordFace_2027.EXAM_RECORD_FACE_2027, "KEY_t_exam_record_face_2027_PRIMARY", ExamRecordFace_2027.EXAM_RECORD_FACE_2027.ID);
        public static final UniqueKey<ExamRecordFace_2028Record> KEY_T_EXAM_RECORD_FACE_2028_PRIMARY = createUniqueKey(ExamRecordFace_2028.EXAM_RECORD_FACE_2028, "KEY_t_exam_record_face_2028_PRIMARY", ExamRecordFace_2028.EXAM_RECORD_FACE_2028.ID);
        public static final UniqueKey<ExamRecordFace_2029Record> KEY_T_EXAM_RECORD_FACE_2029_PRIMARY = createUniqueKey(ExamRecordFace_2029.EXAM_RECORD_FACE_2029, "KEY_t_exam_record_face_2029_PRIMARY", ExamRecordFace_2029.EXAM_RECORD_FACE_2029.ID);
        public static final UniqueKey<ExamRecordFace_2030Record> KEY_T_EXAM_RECORD_FACE_2030_PRIMARY = createUniqueKey(ExamRecordFace_2030.EXAM_RECORD_FACE_2030, "KEY_t_exam_record_face_2030_PRIMARY", ExamRecordFace_2030.EXAM_RECORD_FACE_2030.ID);
        public static final UniqueKey<ExamRegistRecord> KEY_T_EXAM_REGIST_PRIMARY = createUniqueKey(ExamRegist.EXAM_REGIST, "KEY_t_exam_regist_PRIMARY", ExamRegist.EXAM_REGIST.ID);
        public static final UniqueKey<ExamRegist_2017Record> KEY_T_EXAM_REGIST_2017_PRIMARY = createUniqueKey(ExamRegist_2017.EXAM_REGIST_2017, "KEY_t_exam_regist_2017_PRIMARY", ExamRegist_2017.EXAM_REGIST_2017.ID);
        public static final UniqueKey<ExamRegist_2018Record> KEY_T_EXAM_REGIST_2018_PRIMARY = createUniqueKey(ExamRegist_2018.EXAM_REGIST_2018, "KEY_t_exam_regist_2018_PRIMARY", ExamRegist_2018.EXAM_REGIST_2018.ID);
        public static final UniqueKey<ExamRegist_2019Record> KEY_T_EXAM_REGIST_2019_PRIMARY = createUniqueKey(ExamRegist_2019.EXAM_REGIST_2019, "KEY_t_exam_regist_2019_PRIMARY", ExamRegist_2019.EXAM_REGIST_2019.ID);
        public static final UniqueKey<ExamRegist_2020Record> KEY_T_EXAM_REGIST_2020_PRIMARY = createUniqueKey(ExamRegist_2020.EXAM_REGIST_2020, "KEY_t_exam_regist_2020_PRIMARY", ExamRegist_2020.EXAM_REGIST_2020.ID);
        public static final UniqueKey<ExamRegist_2021Record> KEY_T_EXAM_REGIST_2021_PRIMARY = createUniqueKey(ExamRegist_2021.EXAM_REGIST_2021, "KEY_t_exam_regist_2021_PRIMARY", ExamRegist_2021.EXAM_REGIST_2021.ID);
        public static final UniqueKey<ExamRegist_2022Record> KEY_T_EXAM_REGIST_2022_PRIMARY = createUniqueKey(ExamRegist_2022.EXAM_REGIST_2022, "KEY_t_exam_regist_2022_PRIMARY", ExamRegist_2022.EXAM_REGIST_2022.ID);
        public static final UniqueKey<ExamRegist_2023Record> KEY_T_EXAM_REGIST_2023_PRIMARY = createUniqueKey(ExamRegist_2023.EXAM_REGIST_2023, "KEY_t_exam_regist_2023_PRIMARY", ExamRegist_2023.EXAM_REGIST_2023.ID);
        public static final UniqueKey<ExamRegist_2024Record> KEY_T_EXAM_REGIST_2024_PRIMARY = createUniqueKey(ExamRegist_2024.EXAM_REGIST_2024, "KEY_t_exam_regist_2024_PRIMARY", ExamRegist_2024.EXAM_REGIST_2024.ID);
        public static final UniqueKey<ExamRegist_2025Record> KEY_T_EXAM_REGIST_2025_PRIMARY = createUniqueKey(ExamRegist_2025.EXAM_REGIST_2025, "KEY_t_exam_regist_2025_PRIMARY", ExamRegist_2025.EXAM_REGIST_2025.ID);
        public static final UniqueKey<ExamRegist_2026Record> KEY_T_EXAM_REGIST_2026_PRIMARY = createUniqueKey(ExamRegist_2026.EXAM_REGIST_2026, "KEY_t_exam_regist_2026_PRIMARY", ExamRegist_2026.EXAM_REGIST_2026.ID);
        public static final UniqueKey<ExamRegist_2027Record> KEY_T_EXAM_REGIST_2027_PRIMARY = createUniqueKey(ExamRegist_2027.EXAM_REGIST_2027, "KEY_t_exam_regist_2027_PRIMARY", ExamRegist_2027.EXAM_REGIST_2027.ID);
        public static final UniqueKey<ExamRegist_2028Record> KEY_T_EXAM_REGIST_2028_PRIMARY = createUniqueKey(ExamRegist_2028.EXAM_REGIST_2028, "KEY_t_exam_regist_2028_PRIMARY", ExamRegist_2028.EXAM_REGIST_2028.ID);
        public static final UniqueKey<ExamRegist_2029Record> KEY_T_EXAM_REGIST_2029_PRIMARY = createUniqueKey(ExamRegist_2029.EXAM_REGIST_2029, "KEY_t_exam_regist_2029_PRIMARY", ExamRegist_2029.EXAM_REGIST_2029.ID);
        public static final UniqueKey<ExamRegist_2030Record> KEY_T_EXAM_REGIST_2030_PRIMARY = createUniqueKey(ExamRegist_2030.EXAM_REGIST_2030, "KEY_t_exam_regist_2030_PRIMARY", ExamRegist_2030.EXAM_REGIST_2030.ID);
        public static final UniqueKey<ExamPaperAttachmentRecord> KEY_T_EXAM_PAPER_ATTACHMENT_PRIMARY = createUniqueKey(ExamPaperAttachment.EXAM_PAPER_ATTACHMENT, "KEY_t_exam_paper_attachment_PRIMARY", ExamPaperAttachment.EXAM_PAPER_ATTACHMENT.ID);
        public static final UniqueKey<PersonalTemplateRecord> KEY_T_PERSONAL_TEMPLATE_PRIMARY = createUniqueKey(PersonalTemplate.PERSONAL_TEMPLATE, "KEY_t_personal_template_PRIMARY", PersonalTemplate.PERSONAL_TEMPLATE.ID);
        public static final UniqueKey<SignupRecord> KEY_T_SIGNUP_PRIMARY = createUniqueKey(Signup.SIGNUP, "KEY_t_signup_PRIMARY", Signup.SIGNUP.ID);
        public static final UniqueKey<SignupRecordRecord> KEY_T_SIGNUP_RECORD_PRIMARY = createUniqueKey(com.zxy.product.exam.jooq.tables.SignupRecord.SIGNUP_RECORD, "KEY_t_signup_record_PRIMARY", com.zxy.product.exam.jooq.tables.SignupRecord.SIGNUP_RECORD.ID);
        public static final UniqueKey<SignUpAuthRecord> KEY_T_SIGN_UP_AUTH_PRIMARY = createUniqueKey(SignUpAuth.SIGN_UP_AUTH, "KEY_t_sign_up_auth_PRIMARY", SignUpAuth.SIGN_UP_AUTH.ID);
        public static final UniqueKey<CloudSignupRecord> KEY_T_CLOUD_SIGNUP_PRIMARY = createUniqueKey(CloudSignup.CLOUD_SIGNUP, "KEY_t_cloud_signup_PRIMARY", CloudSignup.CLOUD_SIGNUP.ID);
        public static final UniqueKey<GridSignupRecord> KEY_T_GRID_SIGNUP_PRIMARY = createUniqueKey(GridSignup.GRID_SIGNUP, "KEY_t_grid_signup_PRIMARY", GridSignup.GRID_SIGNUP.ID);
        public static final UniqueKey<ToDoRecord> KEY_T_TO_DO_PRIMARY = createUniqueKey(ToDo.TO_DO, "KEY_t_to_do_PRIMARY", ToDo.TO_DO.ID);
        public static final UniqueKey<ExamOnlineLogRecord> KEY_T_EXAM_ONLINE_LOG_PRIMARY = createUniqueKey(ExamOnlineLog.EXAM_ONLINE_LOG, "KEY_t_exam_online_log_PRIMARY", ExamOnlineLog.EXAM_ONLINE_LOG.ID);

        public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_PRIMARY = createUniqueKey(AnswerRecordProcess.ANSWER_RECORD_PROCESS, "KEY_t_answer_record_process_PRIMARY", AnswerRecordProcess.ANSWER_RECORD_PROCESS.ID);
        public static final UniqueKey<AnswerRecordProcessRecord> KEY_T_ANSWER_RECORD_PROCESS_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess.ANSWER_RECORD_PROCESS, "KEY_t_answer_record_process_uniq_question_id_exam_record_id", AnswerRecordProcess.ANSWER_RECORD_PROCESS.EXAM_RECORD_ID, AnswerRecordProcess.ANSWER_RECORD_PROCESS.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_PRIMARY = createUniqueKey(AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0, "KEY_t_answer_record_process_0_PRIMARY", AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0.ID);
        public static final UniqueKey<AnswerRecordProcess_0Record> KEY_T_ANSWER_RECORD_PROCESS_0_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0, "KEY_t_answer_record_process_0_uniq_question_id_exam_record_id", AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0.EXAM_RECORD_ID, AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_PRIMARY = createUniqueKey(AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1, "KEY_t_answer_record_process_1_PRIMARY", AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1.ID);
        public static final UniqueKey<AnswerRecordProcess_1Record> KEY_T_ANSWER_RECORD_PROCESS_1_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1, "KEY_t_answer_record_process_1_uniq_question_id_exam_record_id", AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1.EXAM_RECORD_ID, AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_PRIMARY = createUniqueKey(AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2, "KEY_t_answer_record_process_2_PRIMARY", AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2.ID);
        public static final UniqueKey<AnswerRecordProcess_2Record> KEY_T_ANSWER_RECORD_PROCESS_2_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2, "KEY_t_answer_record_process_2_uniq_question_id_exam_record_id", AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2.EXAM_RECORD_ID, AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_PRIMARY = createUniqueKey(AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3, "KEY_t_answer_record_process_3_PRIMARY", AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3.ID);
        public static final UniqueKey<AnswerRecordProcess_3Record> KEY_T_ANSWER_RECORD_PROCESS_3_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3, "KEY_t_answer_record_process_3_uniq_question_id_exam_record_id", AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3.EXAM_RECORD_ID, AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY = createUniqueKey(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4, "KEY_t_answer_record_process_4_PRIMARY", AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.ID);
        public static final UniqueKey<AnswerRecordProcess_4Record> KEY_T_ANSWER_RECORD_PROCESS_4_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4, "KEY_t_answer_record_process_4_uniq_question_id_exam_record_id", AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.EXAM_RECORD_ID, AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY = createUniqueKey(AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5, "KEY_t_answer_record_process_5_PRIMARY", AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5.ID);
        public static final UniqueKey<AnswerRecordProcess_5Record> KEY_T_ANSWER_RECORD_PROCESS_5_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5, "KEY_t_answer_record_process_5_uniq_question_id_exam_record_id", AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5.EXAM_RECORD_ID, AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_PRIMARY = createUniqueKey(AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6, "KEY_t_answer_record_process_6_PRIMARY", AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6.ID);
        public static final UniqueKey<AnswerRecordProcess_6Record> KEY_T_ANSWER_RECORD_PROCESS_6_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6, "KEY_t_answer_record_process_6_uniq_question_id_exam_record_id", AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6.EXAM_RECORD_ID, AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_PRIMARY = createUniqueKey(AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7, "KEY_t_answer_record_process_7_PRIMARY", AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7.ID);
        public static final UniqueKey<AnswerRecordProcess_7Record> KEY_T_ANSWER_RECORD_PROCESS_7_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7, "KEY_t_answer_record_process_7_uniq_question_id_exam_record_id", AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7.EXAM_RECORD_ID, AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_PRIMARY = createUniqueKey(AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8, "KEY_t_answer_record_process_8_PRIMARY", AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8.ID);
        public static final UniqueKey<AnswerRecordProcess_8Record> KEY_T_ANSWER_RECORD_PROCESS_8_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8, "KEY_t_answer_record_process_8_uniq_question_id_exam_record_id", AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8.EXAM_RECORD_ID, AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8.QUESTION_ID);
        public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_PRIMARY = createUniqueKey(AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9, "KEY_t_answer_record_process_9_PRIMARY", AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9.ID);
        public static final UniqueKey<AnswerRecordProcess_9Record> KEY_T_ANSWER_RECORD_PROCESS_9_UNIQ_QUESTION_ID_EXAM_RECORD_ID = createUniqueKey(AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9, "KEY_t_answer_record_process_9_uniq_question_id_exam_record_id", AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9.EXAM_RECORD_ID, AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9.QUESTION_ID);
        public static final UniqueKey<DigitalIntelligenceResultRecord> KEY_T_DIGITAL_INTELLIGENCE_RESULT_PRIMARY = createUniqueKey(DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT, "KEY_t_digital_intelligence_result_PRIMARY", DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT.ID);

    }
    //todo  以下是exam-stu schema中 答题流水表自增主键信息
    private static class Identities0 extends AbstractKeys {
        public static Identity<AnswerRecordProcessRecord, Long> IDENTITY_ANSWER_RECORD_PROCESS = createIdentity(AnswerRecordProcess.ANSWER_RECORD_PROCESS, AnswerRecordProcess.ANSWER_RECORD_PROCESS.ID);
        public static Identity<AnswerRecordProcess_0Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_0 = createIdentity(AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0, AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0.ID);
        public static Identity<AnswerRecordProcess_1Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_1 = createIdentity(AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1, AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1.ID);
        public static Identity<AnswerRecordProcess_2Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_2 = createIdentity(AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2, AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2.ID);
        public static Identity<AnswerRecordProcess_3Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_3 = createIdentity(AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3, AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3.ID);
        public static Identity<AnswerRecordProcess_4Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_4 = createIdentity(AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4, AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4.ID);
        public static Identity<AnswerRecordProcess_5Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_5 = createIdentity(AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5, AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5.ID);
        public static Identity<AnswerRecordProcess_6Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_6 = createIdentity(AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6, AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6.ID);
        public static Identity<AnswerRecordProcess_7Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_7 = createIdentity(AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7, AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7.ID);
        public static Identity<AnswerRecordProcess_8Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_8 = createIdentity(AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8, AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8.ID);
        public static Identity<AnswerRecordProcess_9Record, Long> IDENTITY_ANSWER_RECORD_PROCESS_9 = createIdentity(AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9, AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9.ID);
        public static Identity<ExamPaperAttachmentRecord, Long> IDENTITY_EXAM_PAPER_ATTACHMENT = createIdentity(ExamPaperAttachment.EXAM_PAPER_ATTACHMENT, ExamPaperAttachment.EXAM_PAPER_ATTACHMENT.ID);
        public static Identity<ExamOnlineLogRecord, Long> IDENTITY_EXAM_ONLINE_LOG = createIdentity(ExamOnlineLog.EXAM_ONLINE_LOG, ExamOnlineLog.EXAM_ONLINE_LOG.ID);

    }
}
