/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridCourseExamRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 网格长各级别对应的考试和学习
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridCourseExam extends TableImpl<GridCourseExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_course_exam</code>
     */
    public static final GridCourseExam GRID_COURSE_EXAM = new GridCourseExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridCourseExamRecord> getRecordType() {
        return GridCourseExamRecord.class;
    }

    /**
     * The column <code>exam.t_grid_course_exam.f_id</code>. 主键
     */
    public final TableField<GridCourseExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_grid_course_exam.f_create_time</code>. 创建时间
     */
    public final TableField<GridCourseExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_course_exam.f_course_id</code>. 学习id
     */
    public final TableField<GridCourseExamRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "学习id");

    /**
     * The column <code>exam.t_grid_course_exam.f_mock_exam_id</code>. 模拟考试id
     */
    public final TableField<GridCourseExamRecord, String> MOCK_EXAM_ID = createField("f_mock_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "模拟考试id");

    /**
     * The column <code>exam.t_grid_course_exam.f_type</code>. 难度类型，1简单，2中等，3困难
     */
    public final TableField<GridCourseExamRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "难度类型，1简单，2中等，3困难");

    /**
     * The column <code>exam.t_grid_course_exam.f_level</code>. 级别，1初级，2中级，3高级
     */
    public final TableField<GridCourseExamRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "级别，1初级，2中级，3高级");

    /**
     * Create a <code>exam.t_grid_course_exam</code> table reference
     */
    public GridCourseExam() {
        this("t_grid_course_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_course_exam</code> table reference
     */
    public GridCourseExam(String alias) {
        this(alias, GRID_COURSE_EXAM);
    }

    private GridCourseExam(String alias, Table<GridCourseExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridCourseExam(String alias, Table<GridCourseExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长各级别对应的考试和学习");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridCourseExamRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_COURSE_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridCourseExamRecord>> getKeys() {
        return Arrays.<UniqueKey<GridCourseExamRecord>>asList(Keys.KEY_T_GRID_COURSE_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridCourseExam as(String alias) {
        return new GridCourseExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridCourseExam rename(String name) {
        return new GridCourseExam(name, null);
    }
}
