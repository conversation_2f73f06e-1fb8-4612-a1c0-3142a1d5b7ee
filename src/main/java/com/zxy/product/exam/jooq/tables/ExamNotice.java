/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamNoticeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;



/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamNotice extends TableImpl<ExamNoticeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_exam_notice</code>
     */
    public static final ExamNotice EXAM_NOTICE = new ExamNotice();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamNoticeRecord> getRecordType() {
        return ExamNoticeRecord.class;
    }

    /**
     * The column <code>exam.t_exam_notice.f_id</code>. 主键
     */
    public final TableField<ExamNoticeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_exam_notice.f_create_time</code>.
     */
    public final TableField<ExamNoticeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_exam_notice.f_notice_user</code>. 是否通知用户,0否 1是
     */
    public final TableField<ExamNoticeRecord, Integer> NOTICE_USER = createField("f_notice_user", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否通知用户,0否 1是");

    /**
     * The column <code>exam.t_exam_notice.f_notice_user_text</code>.
     */
    public final TableField<ExamNoticeRecord, String> NOTICE_USER_TEXT = createField("f_notice_user_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_exam_notice.f_business_id</code>.
     */
    public final TableField<ExamNoticeRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_notice.f_business_type</code>. 1:考试2：调研
     */
    public final TableField<ExamNoticeRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:考试2：调研");

    /**
     * The column <code>exam.t_exam_notice.f_notice_type</code>. 通知类型，0发布通知，1催办，2通知评卷，3报名，4取消报名
     */
    public final TableField<ExamNoticeRecord, Integer> NOTICE_TYPE = createField("f_notice_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "通知类型，0发布通知，1催办，2通知评卷，3报名，4取消报名");

    /**
     * The column <code>exam.t_exam_notice.f_create_member_id</code>. 创建人
     */
    public final TableField<ExamNoticeRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>exam.t_exam_notice.f_templet_code</code>. 通知模板编码
     */
    public final TableField<ExamNoticeRecord, String> TEMPLET_CODE = createField("f_templet_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "通知模板编码");

    /**
     * The column <code>exam.t_exam_notice.f_organization_id</code>. 组织id
     */
    public final TableField<ExamNoticeRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织id");

    /**
     * The column <code>exam.t_exam_notice.f_notice_user_content</code>. 消息内容（带格式）
     */
    public final TableField<ExamNoticeRecord, String> NOTICE_USER_CONTENT = createField("f_notice_user_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "消息内容（带格式）");

    /**
     * The column <code>exam.t_exam_notice.f_receivers</code>. 接收人id，用逗号隔开
     */
    public final TableField<ExamNoticeRecord, String> RECEIVERS = createField("f_receivers", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "接收人id，用逗号隔开");

    /**
     * Create a <code>exam.t_exam_notice</code> table reference
     */
    public ExamNotice() {
        this("t_exam_notice", null);
    }

    /**
     * Create an aliased <code>exam.t_exam_notice</code> table reference
     */
    public ExamNotice(String alias) {
        this(alias, EXAM_NOTICE);
    }

    private ExamNotice(String alias, Table<ExamNoticeRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamNotice(String alias, Table<ExamNoticeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamNoticeRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_NOTICE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamNoticeRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamNoticeRecord>>asList(Keys.KEY_T_EXAM_NOTICE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamNotice as(String alias) {
        return new ExamNotice(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamNotice rename(String name) {
        return new ExamNotice(name, null);
    }
}
