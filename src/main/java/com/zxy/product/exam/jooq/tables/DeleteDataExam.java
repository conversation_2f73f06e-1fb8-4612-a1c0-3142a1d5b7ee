/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.DeleteDataExamRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeleteDataExam extends TableImpl<DeleteDataExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_delete_data_exam</code>
     */
    public static final DeleteDataExam DELETE_DATA_EXAM = new DeleteDataExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DeleteDataExamRecord> getRecordType() {
        return DeleteDataExamRecord.class;
    }

    /**
     * The column <code>exam.t_delete_data_exam.f_id</code>. 主键
     */
    public final TableField<DeleteDataExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_delete_data_exam.f_database_name</code>. 数据库名称
     */
    public final TableField<DeleteDataExamRecord, String> DATABASE_NAME = createField("f_database_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("'human-resource'", org.jooq.impl.SQLDataType.VARCHAR)), this, "数据库名称");

    /**
     * The column <code>exam.t_delete_data_exam.f_table_name</code>. 表名称
     */
    public final TableField<DeleteDataExamRecord, String> TABLE_NAME = createField("f_table_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "表名称");

    /**
     * The column <code>exam.t_delete_data_exam.f_business_id</code>. 业务id
     */
    public final TableField<DeleteDataExamRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务id");

    /**
     * The column <code>exam.t_delete_data_exam.f_create_time</code>. 创建时间
     */
    public final TableField<DeleteDataExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_delete_data_exam.f_motify_date</code>. 更新时间
     */
    public final TableField<DeleteDataExamRecord, Timestamp> MOTIFY_DATE = createField("f_motify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>exam.t_delete_data_exam.f_company_id</code>. 企业id
     */
    public final TableField<DeleteDataExamRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "企业id");

    /**
     * Create a <code>exam.t_delete_data_exam</code> table reference
     */
    public DeleteDataExam() {
        this("t_delete_data_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_delete_data_exam</code> table reference
     */
    public DeleteDataExam(String alias) {
        this(alias, DELETE_DATA_EXAM);
    }

    private DeleteDataExam(String alias, Table<DeleteDataExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private DeleteDataExam(String alias, Table<DeleteDataExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DeleteDataExamRecord> getPrimaryKey() {
        return Keys.KEY_T_DELETE_DATA_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DeleteDataExamRecord>> getKeys() {
        return Arrays.<UniqueKey<DeleteDataExamRecord>>asList(Keys.KEY_T_DELETE_DATA_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataExam as(String alias) {
        return new DeleteDataExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DeleteDataExam rename(String name) {
        return new DeleteDataExam(name, null);
    }
}
