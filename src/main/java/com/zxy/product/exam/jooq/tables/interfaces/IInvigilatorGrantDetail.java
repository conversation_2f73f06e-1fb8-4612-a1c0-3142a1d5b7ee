/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 监考范围详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IInvigilatorGrantDetail extends Serializable {

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_invigilator_id</code>. 监考老师表记录id
     */
    public void setInvigilatorId(String value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_invigilator_id</code>. 监考老师表记录id
     */
    public String getInvigilatorId();

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_exam_id</code>. 考试id
     */
    public void setExamId(String value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_exam_id</code>. 考试id
     */
    public String getExamId();

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>exam.t_invigilator_grant_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_invigilator_grant_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IInvigilatorGrantDetail
     */
    public void from(IInvigilatorGrantDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IInvigilatorGrantDetail
     */
    public <E extends IInvigilatorGrantDetail> E into(E into);
}
