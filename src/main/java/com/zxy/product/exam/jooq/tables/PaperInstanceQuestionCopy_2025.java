/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperInstanceQuestionCopy_2025Record;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 试卷实例与试题副本关联_2025
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperInstanceQuestionCopy_2025 extends TableImpl<PaperInstanceQuestionCopy_2025Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_instance_question_copy_2025</code>
     */
    public static final PaperInstanceQuestionCopy_2025 PAPER_INSTANCE_QUESTION_COPY_2025 = new PaperInstanceQuestionCopy_2025();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperInstanceQuestionCopy_2025Record> getRecordType() {
        return PaperInstanceQuestionCopy_2025Record.class;
    }

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_id</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_paper_instance_id</code>. 所属试卷实例
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, String> PAPER_INSTANCE_ID = createField("f_paper_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试卷实例");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_question_copy_id</code>. 所属试题副本
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, String> QUESTION_COPY_ID = createField("f_question_copy_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试题副本");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_create_time</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_score</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_sequence</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2025.f_modify_date</code>. 修改时间
     */
    public final TableField<PaperInstanceQuestionCopy_2025Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_paper_instance_question_copy_2025</code> table reference
     */
    public PaperInstanceQuestionCopy_2025() {
        this("t_paper_instance_question_copy_2025", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_instance_question_copy_2025</code> table reference
     */
    public PaperInstanceQuestionCopy_2025(String alias) {
        this(alias, PAPER_INSTANCE_QUESTION_COPY_2025);
    }

    private PaperInstanceQuestionCopy_2025(String alias, Table<PaperInstanceQuestionCopy_2025Record> aliased) {
        this(alias, aliased, null);
    }

    private PaperInstanceQuestionCopy_2025(String alias, Table<PaperInstanceQuestionCopy_2025Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试卷实例与试题副本关联_2025");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperInstanceQuestionCopy_2025Record> getPrimaryKey() {
        return Keys.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2025_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperInstanceQuestionCopy_2025Record>> getKeys() {
        return Arrays.<UniqueKey<PaperInstanceQuestionCopy_2025Record>>asList(Keys.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2025_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperInstanceQuestionCopy_2025 as(String alias) {
        return new PaperInstanceQuestionCopy_2025(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperInstanceQuestionCopy_2025 rename(String name) {
        return new PaperInstanceQuestionCopy_2025(name, null);
    }
}
