/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionAttrCopyRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionAttrCopy extends TableImpl<QuestionAttrCopyRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_attr_copy</code>
     */
    public static final QuestionAttrCopy QUESTION_ATTR_COPY = new QuestionAttrCopy();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionAttrCopyRecord> getRecordType() {
        return QuestionAttrCopyRecord.class;
    }

    /**
     * The column <code>exam.t_question_attr_copy.f_id</code>.
     */
    public final TableField<QuestionAttrCopyRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_attr_copy.f_create_time</code>.
     */
    public final TableField<QuestionAttrCopyRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_attr_copy.f_type</code>. 类型
     */
    public final TableField<QuestionAttrCopyRecord, String> TYPE = createField("f_type", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "类型");

    /**
     * The column <code>exam.t_question_attr_copy.f_name</code>. 属性名称
     */
    public final TableField<QuestionAttrCopyRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "属性名称");

    /**
     * The column <code>exam.t_question_attr_copy.f_value</code>. 属性值
     */
    public final TableField<QuestionAttrCopyRecord, String> VALUE = createField("f_value", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "属性值");

    /**
     * The column <code>exam.t_question_attr_copy.f_question_copy_id</code>.
     */
    public final TableField<QuestionAttrCopyRecord, String> QUESTION_COPY_ID = createField("f_question_copy_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_attr_copy.f_old_type</code>. 旧版本类型
     */
    public final TableField<QuestionAttrCopyRecord, String> OLD_TYPE = createField("f_old_type", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "旧版本类型");

    /**
     * Create a <code>exam.t_question_attr_copy</code> table reference
     */
    public QuestionAttrCopy() {
        this("t_question_attr_copy", null);
    }

    /**
     * Create an aliased <code>exam.t_question_attr_copy</code> table reference
     */
    public QuestionAttrCopy(String alias) {
        this(alias, QUESTION_ATTR_COPY);
    }

    private QuestionAttrCopy(String alias, Table<QuestionAttrCopyRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionAttrCopy(String alias, Table<QuestionAttrCopyRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionAttrCopyRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_ATTR_COPY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionAttrCopyRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionAttrCopyRecord>>asList(Keys.KEY_T_QUESTION_ATTR_COPY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionAttrCopy as(String alias) {
        return new QuestionAttrCopy(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionAttrCopy rename(String name) {
        return new QuestionAttrCopy(name, null);
    }
}
