/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SignUpAuthRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 报名认证信息表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SignUpAuth extends TableImpl<SignUpAuthRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_sign_up_auth</code>
     */
    public static final SignUpAuth SIGN_UP_AUTH = new SignUpAuth();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignUpAuthRecord> getRecordType() {
        return SignUpAuthRecord.class;
    }

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_id</code>.
     */
    public final TableField<SignUpAuthRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_create_time</code>. 创建时间
     */
    public final TableField<SignUpAuthRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_sign_up_id</code>. 报名主表id
     */
    public final TableField<SignUpAuthRecord, String> SIGN_UP_ID = createField("f_sign_up_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "报名主表id");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_exam_id</code>. 考试ID
     */
    public final TableField<SignUpAuthRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试ID");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_member_id</code>. 员工编号
     */
    public final TableField<SignUpAuthRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工编号");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_profession_id</code>. 专业
     */
    public final TableField<SignUpAuthRecord, String> PROFESSION_ID = createField("f_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_sub_profession_id</code>. 子专业
     */
    public final TableField<SignUpAuthRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_equipment_type_id</code>. 设备型号id
     */
    public final TableField<SignUpAuthRecord, String> EQUIPMENT_TYPE_ID = createField("f_equipment_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备型号id");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_work_depart</code>. 工作部们
     */
    public final TableField<SignUpAuthRecord, String> WORK_DEPART = createField("f_work_depart", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "工作部们");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_work_time</code>. 工作年限
     */
    public final TableField<SignUpAuthRecord, String> WORK_TIME = createField("f_work_time", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "工作年限");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_is_group_expert</code>. 是否集团专家 0:否，1:是
     */
    public final TableField<SignUpAuthRecord, Integer> IS_GROUP_EXPERT = createField("f_is_group_expert", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否集团专家 0:否，1:是");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_is_provin_expert</code>. 是否是省级专家 0:否 1:是
     */
    public final TableField<SignUpAuthRecord, Integer> IS_PROVIN_EXPERT = createField("f_is_provin_expert", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否是省级专家 0:否 1:是");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_other_exam_appraisal</code>.
     */
    public final TableField<SignUpAuthRecord, String> OTHER_EXAM_APPRAISAL = createField("f_other_exam_appraisal", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_award_situation</code>.
     */
    public final TableField<SignUpAuthRecord, String> AWARD_SITUATION = createField("f_award_situation", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_cross_condition</code>.
     */
    public final TableField<SignUpAuthRecord, String> CROSS_CONDITION = createField("f_cross_condition", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_apply_level</code>. 本次申报等级
     */
    public final TableField<SignUpAuthRecord, String> APPLY_LEVEL = createField("f_apply_level", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报等级");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_apply_profession</code>. 本次申报专业
     */
    public final TableField<SignUpAuthRecord, String> APPLY_PROFESSION = createField("f_apply_profession", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报专业");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_apply_sub_profession</code>. 本次申报子专业
     */
    public final TableField<SignUpAuthRecord, String> APPLY_SUB_PROFESSION = createField("f_apply_sub_profession", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报子专业");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_apply_supplier</code>. 本次申报厂商
     */
    public final TableField<SignUpAuthRecord, String> APPLY_SUPPLIER = createField("f_apply_supplier", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报厂商");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_province</code>. 所在省
     */
    public final TableField<SignUpAuthRecord, String> PROVINCE = createField("f_province", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所在省");

    /**
     * The column <code>exam-stu.t_sign_up_auth.f_city</code>. 所在市
     */
    public final TableField<SignUpAuthRecord, String> CITY = createField("f_city", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所在市");

    /**
     * Create a <code>exam-stu.t_sign_up_auth</code> table reference
     */
    public SignUpAuth() {
        this("t_sign_up_auth", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_sign_up_auth</code> table reference
     */
    public SignUpAuth(String alias) {
        this(alias, SIGN_UP_AUTH);
    }

    private SignUpAuth(String alias, Table<SignUpAuthRecord> aliased) {
        this(alias, aliased, null);
    }

    private SignUpAuth(String alias, Table<SignUpAuthRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "报名认证信息表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignUpAuthRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGN_UP_AUTH_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignUpAuthRecord>> getKeys() {
        return Arrays.<UniqueKey<SignUpAuthRecord>>asList(Keys.KEY_T_SIGN_UP_AUTH_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SignUpAuth as(String alias) {
        return new SignUpAuth(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SignUpAuth rename(String name) {
        return new SignUpAuth(name, null);
    }
}
