/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SyncInfoRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;


/**
 * t_sync_info同步试卷临时信息存储
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SyncInfo extends TableImpl<SyncInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_sync_info</code>
     */
    public static final SyncInfo SYNC_INFO = new SyncInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SyncInfoRecord> getRecordType() {
        return SyncInfoRecord.class;
    }

    /**
     * The column <code>exam.t_sync_info.f_id</code>.
     */
    public final TableField<SyncInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("'主键'", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_sync_info.f_create_time</code>. 创建时间
     */
    public final TableField<SyncInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_sync_info.f_paper_class_password</code>. 同步加密后json字符串
     */
    public final TableField<SyncInfoRecord, String> PAPER_CLASS_PASSWORD = createField("f_paper_class_password", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "同步加密后json字符串");

    /**
     * The column <code>exam.t_sync_info.f_paper_class</code>. 同步解密后字符串
     */
    public final TableField<SyncInfoRecord, String> PAPER_CLASS = createField("f_paper_class", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "同步解密后字符串");

    /**
     * The column <code>exam.t_sync_info.f_dynamic_key</code>. 动态秘钥
     */
    public final TableField<SyncInfoRecord, String> DYNAMIC_KEY = createField("f_dynamic_key", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "动态秘钥");

    /**
     * The column <code>exam.t_sync_info.f_ip</code>. 请求ip
     */
    public final TableField<SyncInfoRecord, String> IP = createField("f_ip", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "请求ip");

    /**
     * The column <code>exam.t_sync_info.f_correct_json</code>. 正确json
     */
    public final TableField<SyncInfoRecord, String> CORRECT_JSON = createField("f_correct_json", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "正确json");

    /**
     * The column <code>exam.t_sync_info.f_exam_ids</code>. 考试ids
     */
    public final TableField<SyncInfoRecord, String> EXAM_IDS = createField("f_exam_ids", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试ids");

    /**
     * Create a <code>exam.t_sync_info</code> table reference
     */
    public SyncInfo() {
        this("t_sync_info", null);
    }

    /**
     * Create an aliased <code>exam.t_sync_info</code> table reference
     */
    public SyncInfo(String alias) {
        this(alias, SYNC_INFO);
    }

    private SyncInfo(String alias, Table<SyncInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private SyncInfo(String alias, Table<SyncInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "t_sync_info同步试卷临时信息存储");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SyncInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_SYNC_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SyncInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<SyncInfoRecord>>asList(Keys.KEY_T_SYNC_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SyncInfo as(String alias) {
        return new SyncInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SyncInfo rename(String name) {
        return new SyncInfo(name, null);
    }
}
