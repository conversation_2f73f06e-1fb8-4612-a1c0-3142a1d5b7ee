/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CloudProfessionRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 移动云考试专业表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CloudProfession extends TableImpl<CloudProfessionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_cloud_profession</code>
     */
    public static final CloudProfession CLOUD_PROFESSION = new CloudProfession();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CloudProfessionRecord> getRecordType() {
        return CloudProfessionRecord.class;
    }

    /**
     * The column <code>exam.t_cloud_profession.f_id</code>. 主键
     */
    public final TableField<CloudProfessionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_cloud_profession.f_create_time</code>.
     */
    public final TableField<CloudProfessionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_cloud_profession.f_code</code>. 专业编码
     */
    public final TableField<CloudProfessionRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业编码");

    /**
     * The column <code>exam.t_cloud_profession.f_name</code>. 专业名称
     */
    public final TableField<CloudProfessionRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业名称");

    /**
     * The column <code>exam.t_cloud_profession.f_parent_id</code>. 父专业id
     */
    public final TableField<CloudProfessionRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "父专业id");

    /**
     * The column <code>exam.t_cloud_profession.f_organization_id</code>. 部门ID
     */
    public final TableField<CloudProfessionRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门ID");

    /**
     * The column <code>exam.t_cloud_profession.f_status</code>. 状态，启用1，禁用0
     */
    public final TableField<CloudProfessionRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "状态，启用1，禁用0");

    /**
     * The column <code>exam.t_cloud_profession.f_sort</code>. 排序
     */
    public final TableField<CloudProfessionRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>exam.t_cloud_profession.f_delete</code>. 是否已删除，0:否，1：是
     */
    public final TableField<CloudProfessionRecord, Integer> DELETE = createField("f_delete", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否已删除，0:否，1：是");

    /**
     * The column <code>exam.t_cloud_profession.f_select</code>. 是否查询历史专业，0:否，1：是
     */
    public final TableField<CloudProfessionRecord, Integer> SELECT = createField("f_select", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否查询历史专业，0:否，1：是");

    /**
     * Create a <code>exam.t_cloud_profession</code> table reference
     */
    public CloudProfession() {
        this("t_cloud_profession", null);
    }

    /**
     * Create an aliased <code>exam.t_cloud_profession</code> table reference
     */
    public CloudProfession(String alias) {
        this(alias, CLOUD_PROFESSION);
    }

    private CloudProfession(String alias, Table<CloudProfessionRecord> aliased) {
        this(alias, aliased, null);
    }

    private CloudProfession(String alias, Table<CloudProfessionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "移动云考试专业表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CloudProfessionRecord> getPrimaryKey() {
        return Keys.KEY_T_CLOUD_PROFESSION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CloudProfessionRecord>> getKeys() {
        return Arrays.<UniqueKey<CloudProfessionRecord>>asList(Keys.KEY_T_CLOUD_PROFESSION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CloudProfession as(String alias) {
        return new CloudProfession(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CloudProfession rename(String name) {
        return new CloudProfession(name, null);
    }
}
