/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperClassRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 试卷类
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperClass extends TableImpl<PaperClassRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_class</code>
     */
    public static final PaperClass PAPER_CLASS = new PaperClass();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperClassRecord> getRecordType() {
        return PaperClassRecord.class;
    }

    /**
     * The column <code>exam.t_paper_class.f_id</code>. ID
     */
    public final TableField<PaperClassRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_paper_class.f_create_time</code>. 创建时间
     */
    public final TableField<PaperClassRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_paper_class.f_organization_id</code>. 组织ID
     */
    public final TableField<PaperClassRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织ID");

    /**
     * The column <code>exam.t_paper_class.f_name</code>. 试卷名称
     */
    public final TableField<PaperClassRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试卷名称");

    /**
     * The column <code>exam.t_paper_class.f_status</code>. 状态(0：未发布 1：已发布)
     */
    public final TableField<PaperClassRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态(0：未发布 1：已发布)");

    /**
     * The column <code>exam.t_paper_class.f_is_subjective</code>. 是否存在主观题(0：否 1：是)
     */
    public final TableField<PaperClassRecord, Integer> IS_SUBJECTIVE = createField("f_is_subjective", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否存在主观题(0：否 1：是)");

    /**
     * The column <code>exam.t_paper_class.f_total_score</code>. 总得分
     */
    public final TableField<PaperClassRecord, Integer> TOTAL_SCORE = createField("f_total_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "总得分");

    /**
     * The column <code>exam.t_paper_class.f_question_num</code>. 试题数量
     */
    public final TableField<PaperClassRecord, Integer> QUESTION_NUM = createField("f_question_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "试题数量");

    /**
     * The column <code>exam.t_paper_class.f_type</code>. 1.普通试卷，2.临时试卷，3.随机组卷
     */
    public final TableField<PaperClassRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1.普通试卷，2.临时试卷，3.随机组卷");

    /**
     * The column <code>exam.t_paper_class.f_associated_state</code>. 关联状态，1表示同步过来的试卷，表示已关联；null表示未关联
     */
    public final TableField<PaperClassRecord, Integer> ASSOCIATED_STATE = createField("f_associated_state", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "关联状态，1表示同步过来的试卷，表示已关联；null表示未关联");

    /**
     * Create a <code>exam.t_paper_class</code> table reference
     */
    public PaperClass() {
        this("t_paper_class", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_class</code> table reference
     */
    public PaperClass(String alias) {
        this(alias, PAPER_CLASS);
    }

    private PaperClass(String alias, Table<PaperClassRecord> aliased) {
        this(alias, aliased, null);
    }

    private PaperClass(String alias, Table<PaperClassRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试卷类");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperClassRecord> getPrimaryKey() {
        return Keys.KEY_T_PAPER_CLASS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperClassRecord>> getKeys() {
        return Arrays.<UniqueKey<PaperClassRecord>>asList(Keys.KEY_T_PAPER_CLASS_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperClass as(String alias) {
        return new PaperClass(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperClass rename(String name) {
        return new PaperClass(name, null);
    }
}
