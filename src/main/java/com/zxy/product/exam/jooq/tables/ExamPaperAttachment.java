/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamPaperAttachmentRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 考试试卷附件表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamPaperAttachment extends TableImpl<ExamPaperAttachmentRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_exam_paper_attachment</code>
     */
    public static final ExamPaperAttachment EXAM_PAPER_ATTACHMENT = new ExamPaperAttachment();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamPaperAttachmentRecord> getRecordType() {
        return ExamPaperAttachmentRecord.class;
    }

    /**
     * The column <code>exam.t_exam_paper_attachment.f_id</code>.
     */
    public final TableField<ExamPaperAttachmentRecord, Long> ID = createField("f_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>exam.t_exam_paper_attachment.f_exam_id</code>. 考试id
     */
    public final TableField<ExamPaperAttachmentRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "考试id");

    /**
     * The column <code>exam.t_exam_paper_attachment.f_paper_instance_id</code>. 试卷实例id
     */
    public final TableField<ExamPaperAttachmentRecord, String> PAPER_INSTANCE_ID = createField("f_paper_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "试卷实例id");

    /**
     * The column <code>exam.t_exam_paper_attachment.f_paper_path</code>. 试卷路径
     */
    public final TableField<ExamPaperAttachmentRecord, String> PAPER_PATH = createField("f_paper_path", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false), this, "试卷路径");

    /**
     * The column <code>exam.t_exam_paper_attachment.f_create_time</code>.
     */
    public final TableField<ExamPaperAttachmentRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>exam.t_exam_paper_attachment</code> table reference
     */
    public ExamPaperAttachment() {
        this("t_exam_paper_attachment", null);
    }

    /**
     * Create an aliased <code>exam.t_exam_paper_attachment</code> table reference
     */
    public ExamPaperAttachment(String alias) {
        this(alias, EXAM_PAPER_ATTACHMENT);
    }

    private ExamPaperAttachment(String alias, Table<ExamPaperAttachmentRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamPaperAttachment(String alias, Table<ExamPaperAttachmentRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试试卷附件表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Identity<ExamPaperAttachmentRecord, Long> getIdentity() {
        return Keys.IDENTITY_EXAM_PAPER_ATTACHMENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamPaperAttachmentRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_PAPER_ATTACHMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamPaperAttachmentRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamPaperAttachmentRecord>>asList(Keys.KEY_T_EXAM_PAPER_ATTACHMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamPaperAttachment as(String alias) {
        return new ExamPaperAttachment(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamPaperAttachment rename(String name) {
        return new ExamPaperAttachment(name, null);
    }
}
