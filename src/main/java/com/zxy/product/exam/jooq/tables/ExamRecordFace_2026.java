/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamRecordFace_2026Record;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 人脸监考记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamRecordFace_2026 extends TableImpl<ExamRecordFace_2026Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_exam_record_face_2026</code>
     */
    public static final ExamRecordFace_2026 EXAM_RECORD_FACE_2026 = new ExamRecordFace_2026();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamRecordFace_2026Record> getRecordType() {
        return ExamRecordFace_2026Record.class;
    }

    /**
     * The column <code>exam.t_exam_record_face_2026.f_id</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_exam_record_id</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_type</code>. 0：人脸进入 1：人脸监考 2：二机位认证进入 3：二机位认证中
     */
    public final TableField<ExamRecordFace_2026Record, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0：人脸进入 1：人脸监考 2：二机位认证进入 3：二机位认证中");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_face_img_url</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> FACE_IMG_URL = createField("f_face_img_url", org.jooq.impl.SQLDataType.VARCHAR.length(256).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_status</code>. 0：正常 1：异常 2：标记正常 3：标记异常 4：未检测
     */
    public final TableField<ExamRecordFace_2026Record, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0：正常 1：异常 2：标记正常 3：标记异常 4：未检测");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_resp_msg</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> RESP_MSG = createField("f_resp_msg", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_create_time</code>.
     */
    public final TableField<ExamRecordFace_2026Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_member_id</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_exam_id</code>.
     */
    public final TableField<ExamRecordFace_2026Record, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_exam_record_face_2026.f_num</code>. 第几次采集（二机位认证用）
     */
    public final TableField<ExamRecordFace_2026Record, Integer> NUM = createField("f_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第几次采集（二机位认证用）");

    /**
     * Create a <code>exam.t_exam_record_face_2026</code> table reference
     */
    public ExamRecordFace_2026() {
        this("t_exam_record_face_2026", null);
    }

    /**
     * Create an aliased <code>exam.t_exam_record_face_2026</code> table reference
     */
    public ExamRecordFace_2026(String alias) {
        this(alias, EXAM_RECORD_FACE_2026);
    }

    private ExamRecordFace_2026(String alias, Table<ExamRecordFace_2026Record> aliased) {
        this(alias, aliased, null);
    }

    private ExamRecordFace_2026(String alias, Table<ExamRecordFace_2026Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "人脸监考记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamRecordFace_2026Record> getPrimaryKey() {
        return Keys.KEY_T_EXAM_RECORD_FACE_2026_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamRecordFace_2026Record>> getKeys() {
        return Arrays.<UniqueKey<ExamRecordFace_2026Record>>asList(Keys.KEY_T_EXAM_RECORD_FACE_2026_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamRecordFace_2026 as(String alias) {
        return new ExamRecordFace_2026(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamRecordFace_2026 rename(String name) {
        return new ExamRecordFace_2026(name, null);
    }
}
