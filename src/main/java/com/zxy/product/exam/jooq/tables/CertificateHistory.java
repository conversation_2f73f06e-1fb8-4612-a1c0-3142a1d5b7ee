/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CertificateHistoryRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 证书迁移记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CertificateHistory extends TableImpl<CertificateHistoryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_certificate_history</code>
     */
    public static final CertificateHistory CERTIFICATE_HISTORY = new CertificateHistory();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CertificateHistoryRecord> getRecordType() {
        return CertificateHistoryRecord.class;
    }

    /**
     * The column <code>exam.t_certificate_history.f_id</code>. ID
     */
    public final TableField<CertificateHistoryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_certificate_history.f_member_name</code>. 用户员工编号
     */
    public final TableField<CertificateHistoryRecord, String> MEMBER_NAME = createField("f_member_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户员工编号");

    /**
     * The column <code>exam.t_certificate_history.f_num</code>. 证书编号
     */
    public final TableField<CertificateHistoryRecord, String> NUM = createField("f_num", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书编号");

    /**
     * The column <code>exam.t_certificate_history.f_profession_name</code>. 专业名称
     */
    public final TableField<CertificateHistoryRecord, String> PROFESSION_NAME = createField("f_profession_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业名称");

    /**
     * The column <code>exam.t_certificate_history.f_sub_profession_name</code>. 子专业名称
     */
    public final TableField<CertificateHistoryRecord, String> SUB_PROFESSION_NAME = createField("f_sub_profession_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业名称");

    /**
     * The column <code>exam.t_certificate_history.f_equipment_type_name</code>. 设备名称
     */
    public final TableField<CertificateHistoryRecord, String> EQUIPMENT_TYPE_NAME = createField("f_equipment_type_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备名称");

    /**
     * The column <code>exam.t_certificate_history.f_level_name</code>. 等级名称
     */
    public final TableField<CertificateHistoryRecord, String> LEVEL_NAME = createField("f_level_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级名称");

    /**
     * The column <code>exam.t_certificate_history.f_reason</code>. 发放原因
     */
    public final TableField<CertificateHistoryRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发放原因");

    /**
     * The column <code>exam.t_certificate_history.f_member_id</code>. 用户id
     */
    public final TableField<CertificateHistoryRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_certificate_history.f_profession_id</code>. 专业
     */
    public final TableField<CertificateHistoryRecord, String> PROFESSION_ID = createField("f_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业");

    /**
     * The column <code>exam.t_certificate_history.f_sub_profession_id</code>. 子专业
     */
    public final TableField<CertificateHistoryRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业");

    /**
     * The column <code>exam.t_certificate_history.f_equipment_type_id</code>. 设备
     */
    public final TableField<CertificateHistoryRecord, String> EQUIPMENT_TYPE_ID = createField("f_equipment_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备");

    /**
     * The column <code>exam.t_certificate_history.f_profession_level_id</code>. 专业等级id
     */
    public final TableField<CertificateHistoryRecord, String> PROFESSION_LEVEL_ID = createField("f_profession_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业等级id");

    /**
     * The column <code>exam.t_certificate_history.f_issue_time_str</code>. 签发日期字符串
     */
    public final TableField<CertificateHistoryRecord, String> ISSUE_TIME_STR = createField("f_issue_time_str", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "签发日期字符串");

    /**
     * The column <code>exam.t_certificate_history.f_issue_time</code>. 签发日期
     */
    public final TableField<CertificateHistoryRecord, Long> ISSUE_TIME = createField("f_issue_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "签发日期");

    /**
     * The column <code>exam.t_certificate_history.f_vaild_date</code>. 证书有效期
     */
    public final TableField<CertificateHistoryRecord, Long> VAILD_DATE = createField("f_vaild_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "证书有效期");

    /**
     * The column <code>exam.t_certificate_history.f_status</code>. 是否成功同步，0否 1是
     */
    public final TableField<CertificateHistoryRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否成功同步，0否 1是");

    /**
     * Create a <code>exam.t_certificate_history</code> table reference
     */
    public CertificateHistory() {
        this("t_certificate_history", null);
    }

    /**
     * Create an aliased <code>exam.t_certificate_history</code> table reference
     */
    public CertificateHistory(String alias) {
        this(alias, CERTIFICATE_HISTORY);
    }

    private CertificateHistory(String alias, Table<CertificateHistoryRecord> aliased) {
        this(alias, aliased, null);
    }

    private CertificateHistory(String alias, Table<CertificateHistoryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "证书迁移记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CertificateHistoryRecord> getPrimaryKey() {
        return Keys.KEY_T_CERTIFICATE_HISTORY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CertificateHistoryRecord>> getKeys() {
        return Arrays.<UniqueKey<CertificateHistoryRecord>>asList(Keys.KEY_T_CERTIFICATE_HISTORY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CertificateHistory as(String alias) {
        return new CertificateHistory(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CertificateHistory rename(String name) {
        return new CertificateHistory(name, null);
    }
}
