/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 子认证-考试证书取消证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubAuthenticatedCancelCertificateRecord extends Serializable {

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_id</code>. id
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_id</code>. id
     */
    public String getId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public void setSubAuthenticatedId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public String getSubAuthenticatedId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_member_id</code>. 学员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_member_id</code>. 学员id
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_operator_id</code>. 操作人id
     */
    public void setOperatorId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_operator_id</code>. 操作人id
     */
    public String getOperatorId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_exam_id</code>. 考试id
     */
    public void setExamId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_exam_id</code>. 考试id
     */
    public String getExamId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_cancel_time</code>. 取消时间
     */
    public void setCancelTime(Long value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_cancel_time</code>. 取消时间
     */
    public Long getCancelTime();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_publish_time</code>. 发证时间
     */
    public void setPublishTime(Long value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_publish_time</code>. 发证时间
     */
    public Long getPublishTime();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_id</code>. 证明材料id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_id</code>. 证明材料id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_name</code>. 证明材料名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_name</code>. 证明材料名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_reason</code>. 原因描述
     */
    public void setReason(String value);

    /**
     * Getter for <code>exam.t_sub_authenticated_cancel_certificate_record.f_reason</code>. 原因描述
     */
    public String getReason();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubAuthenticatedCancelCertificateRecord
     */
    public void from(ISubAuthenticatedCancelCertificateRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubAuthenticatedCancelCertificateRecord
     */
    public <E extends ISubAuthenticatedCancelCertificateRecord> E into(E into);
}
