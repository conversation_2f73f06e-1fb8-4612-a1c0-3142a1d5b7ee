/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecordProcess_5Record;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 问题记录流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecordProcess_5 extends TableImpl<AnswerRecordProcess_5Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_answer_record_process_5</code>
     */
    public static final AnswerRecordProcess_5 ANSWER_RECORD_PROCESS_5 = new AnswerRecordProcess_5();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecordProcess_5Record> getRecordType() {
        return AnswerRecordProcess_5Record.class;
    }

    /**
     * The column <code>exam-stu.t_answer_record_process_5.id</code>.
     */
    public final TableField<AnswerRecordProcess_5Record, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_5.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecordProcess_5Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_5.f_question_id</code>.
     */
    public final TableField<AnswerRecordProcess_5Record, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_5.f_answer</code>.
     */
    public final TableField<AnswerRecordProcess_5Record, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_5.f_create_time</code>.
     */
    public final TableField<AnswerRecordProcess_5Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>exam-stu.t_answer_record_process_5</code> table reference
     */
    public AnswerRecordProcess_5() {
        this("t_answer_record_process_5", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_answer_record_process_5</code> table reference
     */
    public AnswerRecordProcess_5(String alias) {
        this(alias, ANSWER_RECORD_PROCESS_5);
    }

    private AnswerRecordProcess_5(String alias, Table<AnswerRecordProcess_5Record> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecordProcess_5(String alias, Table<AnswerRecordProcess_5Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "问题记录流水表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Identity<AnswerRecordProcess_5Record, Long> getIdentity() {
        return Keys.IDENTITY_ANSWER_RECORD_PROCESS_5;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecordProcess_5Record> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecordProcess_5Record>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecordProcess_5Record>>asList(Keys.KEY_T_ANSWER_RECORD_PROCESS_5_PRIMARY, Keys.KEY_T_ANSWER_RECORD_PROCESS_5_UNIQ_QUESTION_ID_EXAM_RECORD_ID);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_5 as(String alias) {
        return new AnswerRecordProcess_5(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecordProcess_5 rename(String name) {
        return new AnswerRecordProcess_5(name, null);
    }
}
