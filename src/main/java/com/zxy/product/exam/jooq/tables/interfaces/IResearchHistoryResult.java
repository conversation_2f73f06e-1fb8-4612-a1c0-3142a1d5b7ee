/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IResearchHistoryResult extends Serializable {

    /**
     * Setter for <code>exam.t_research_history_result.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_research_history_result.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>exam.t_research_history_result.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_research_history_result.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_research_history_result.f_study_total_time</code>. 个人年度学习总时长
     */
    public void setStudyTotalTime(Double value);

    /**
     * Getter for <code>exam.t_research_history_result.f_study_total_time</code>. 个人年度学习总时长
     */
    public Double getStudyTotalTime();

    /**
     * Setter for <code>exam.t_research_history_result.f_type</code>. 学习占比最多的课程类型：1：管理，2：综合，3：技术，4：业务
     */
    public void setType(Integer value);

    /**
     * Getter for <code>exam.t_research_history_result.f_type</code>. 学习占比最多的课程类型：1：管理，2：综合，3：技术，4：业务
     */
    public Integer getType();

    /**
     * Setter for <code>exam.t_research_history_result.f_study_max_time_name</code>. 学习时长最长的课程名称
     */
    public void setStudyMaxTimeName(String value);

    /**
     * Getter for <code>exam.t_research_history_result.f_study_max_time_name</code>. 学习时长最长的课程名称
     */
    public String getStudyMaxTimeName();

    /**
     * Setter for <code>exam.t_research_history_result.f_study_latest_day</code>. 学习时间最晚的一天
     */
    public void setStudyLatestDay(String value);

    /**
     * Getter for <code>exam.t_research_history_result.f_study_latest_day</code>. 学习时间最晚的一天
     */
    public String getStudyLatestDay();

    /**
     * Setter for <code>exam.t_research_history_result.f_ranking</code>. 学习时长在组织中排名
     */
    public void setRanking(Integer value);

    /**
     * Getter for <code>exam.t_research_history_result.f_ranking</code>. 学习时长在组织中排名
     */
    public Integer getRanking();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IResearchHistoryResult
     */
    public void from(IResearchHistoryResult from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IResearchHistoryResult
     */
    public <E extends IResearchHistoryResult> E into(E into);
}
