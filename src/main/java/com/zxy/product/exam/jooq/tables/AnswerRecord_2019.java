/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecord_2019Record;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 答案记录表_2019
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecord_2019 extends TableImpl<AnswerRecord_2019Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_answer_record_2019</code>
     */
    public static final AnswerRecord_2019 ANSWER_RECORD_2019 = new AnswerRecord_2019();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecord_2019Record> getRecordType() {
        return AnswerRecord_2019Record.class;
    }

    /**
     * The column <code>exam.t_answer_record_2019.f_id</code>.
     */
    public final TableField<AnswerRecord_2019Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_create_time</code>.
     */
    public final TableField<AnswerRecord_2019Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecord_2019Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_question_id</code>.
     */
    public final TableField<AnswerRecord_2019Record, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_answer</code>.
     */
    public final TableField<AnswerRecord_2019Record, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_is_right</code>.
     */
    public final TableField<AnswerRecord_2019Record, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_score</code>.
     */
    public final TableField<AnswerRecord_2019Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2019.f_modify_date</code>. 修改时间
     */
    public final TableField<AnswerRecord_2019Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_answer_record_2019</code> table reference
     */
    public AnswerRecord_2019() {
        this("t_answer_record_2019", null);
    }

    /**
     * Create an aliased <code>exam.t_answer_record_2019</code> table reference
     */
    public AnswerRecord_2019(String alias) {
        this(alias, ANSWER_RECORD_2019);
    }

    private AnswerRecord_2019(String alias, Table<AnswerRecord_2019Record> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecord_2019(String alias, Table<AnswerRecord_2019Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "答案记录表_2019");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecord_2019Record> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_2019_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecord_2019Record>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecord_2019Record>>asList(Keys.KEY_T_ANSWER_RECORD_2019_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecord_2019 as(String alias) {
        return new AnswerRecord_2019(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecord_2019 rename(String name) {
        return new AnswerRecord_2019(name, null);
    }
}
