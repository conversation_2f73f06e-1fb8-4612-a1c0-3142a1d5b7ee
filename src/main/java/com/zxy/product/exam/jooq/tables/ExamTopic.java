/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamTopicRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 业务关联话题
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamTopic extends TableImpl<ExamTopicRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_exam_topic</code>
     */
    public static final ExamTopic EXAM_TOPIC = new ExamTopic();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamTopicRecord> getRecordType() {
        return ExamTopicRecord.class;
    }

    /**
     * The column <code>exam.t_exam_topic.f_id</code>. ID
     */
    public final TableField<ExamTopicRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_exam_topic.f_exam_id</code>. 考试ID
     */
    public final TableField<ExamTopicRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试ID");

    /**
     * The column <code>exam.t_exam_topic.f_topic_name</code>. 话题名称
     */
    public final TableField<ExamTopicRecord, String> TOPIC_NAME = createField("f_topic_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "话题名称");

    /**
     * The column <code>exam.t_exam_topic.f_topic_id</code>. 话题ID
     */
    public final TableField<ExamTopicRecord, String> TOPIC_ID = createField("f_topic_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "话题ID");

    /**
     * The column <code>exam.t_exam_topic.f_create_time</code>.
     */
    public final TableField<ExamTopicRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>exam.t_exam_topic</code> table reference
     */
    public ExamTopic() {
        this("t_exam_topic", null);
    }

    /**
     * Create an aliased <code>exam.t_exam_topic</code> table reference
     */
    public ExamTopic(String alias) {
        this(alias, EXAM_TOPIC);
    }

    private ExamTopic(String alias, Table<ExamTopicRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamTopic(String alias, Table<ExamTopicRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "业务关联话题");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamTopicRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_TOPIC_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamTopicRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamTopicRecord>>asList(Keys.KEY_T_EXAM_TOPIC_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamTopic as(String alias) {
        return new ExamTopic(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamTopic rename(String name) {
        return new ExamTopic(name, null);
    }
}
