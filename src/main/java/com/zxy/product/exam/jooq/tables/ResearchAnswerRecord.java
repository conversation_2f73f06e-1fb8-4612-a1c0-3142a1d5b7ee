/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ResearchAnswerRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchAnswerRecord extends TableImpl<ResearchAnswerRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_research_answer_record</code>
     */
    public static final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = new ResearchAnswerRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchAnswerRecordRecord> getRecordType() {
        return ResearchAnswerRecordRecord.class;
    }

    /**
     * The column <code>exam.t_research_answer_record.f_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_create_time</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_research_record_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> RESEARCH_RECORD_ID = createField("f_research_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_question_id</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_answer</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_is_right</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_score</code>.
     */
    public final TableField<ResearchAnswerRecordRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_research_answer_record.f_remark</code>. 附加意见（选择题附加意见）
     */
    public final TableField<ResearchAnswerRecordRecord, String> REMARK = createField("f_remark", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "附加意见（选择题附加意见）");

    /**
     * Create a <code>exam.t_research_answer_record</code> table reference
     */
    public ResearchAnswerRecord() {
        this("t_research_answer_record", null);
    }

    /**
     * Create an aliased <code>exam.t_research_answer_record</code> table reference
     */
    public ResearchAnswerRecord(String alias) {
        this(alias, RESEARCH_ANSWER_RECORD);
    }

    private ResearchAnswerRecord(String alias, Table<ResearchAnswerRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchAnswerRecord(String alias, Table<ResearchAnswerRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchAnswerRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchAnswerRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchAnswerRecordRecord>>asList(Keys.KEY_T_RESEARCH_ANSWER_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchAnswerRecord as(String alias) {
        return new ResearchAnswerRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchAnswerRecord rename(String name) {
        return new ResearchAnswerRecord(name, null);
    }
}
