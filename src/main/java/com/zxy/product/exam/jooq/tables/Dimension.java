/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.DimensionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Dimension extends TableImpl<DimensionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_dimension</code>
     */
    public static final Dimension DIMENSION = new Dimension();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DimensionRecord> getRecordType() {
        return DimensionRecord.class;
    }

    /**
     * The column <code>exam.t_dimension.f_id</code>. 主键
     */
    public final TableField<DimensionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_dimension.f_name</code>. 维度名称
     */
    public final TableField<DimensionRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(300).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "维度名称");

    /**
     * The column <code>exam.t_dimension.f_create_time</code>.
     */
    public final TableField<DimensionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_dimension.f_description</code>. 描述
     */
    public final TableField<DimensionRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "描述");

    /**
     * The column <code>exam.t_dimension.f_research_questionary_id</code>. 调研问卷
     */
    public final TableField<DimensionRecord, String> RESEARCH_QUESTIONARY_ID = createField("f_research_questionary_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "调研问卷");

    /**
     * The column <code>exam.t_dimension.f_order</code>. 排序
     */
    public final TableField<DimensionRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>exam.t_dimension.f_is_empty</code>. 是否为空维度，1是 0否
     */
    public final TableField<DimensionRecord, Integer> IS_EMPTY = createField("f_is_empty", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为空维度，1是 0否");

    /**
     * The column <code>exam.t_dimension.f_code</code>. 维度编码
     */
    public final TableField<DimensionRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "维度编码");

    /**
     * Create a <code>exam.t_dimension</code> table reference
     */
    public Dimension() {
        this("t_dimension", null);
    }

    /**
     * Create an aliased <code>exam.t_dimension</code> table reference
     */
    public Dimension(String alias) {
        this(alias, DIMENSION);
    }

    private Dimension(String alias, Table<DimensionRecord> aliased) {
        this(alias, aliased, null);
    }

    private Dimension(String alias, Table<DimensionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DimensionRecord> getPrimaryKey() {
        return Keys.KEY_T_DIMENSION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DimensionRecord>> getKeys() {
        return Arrays.<UniqueKey<DimensionRecord>>asList(Keys.KEY_T_DIMENSION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Dimension as(String alias) {
        return new Dimension(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Dimension rename(String name) {
        return new Dimension(name, null);
    }
}
