/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.OrganizationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Organization extends TableImpl<OrganizationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_organization</code>
     */
    public static final Organization ORGANIZATION = new Organization();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganizationRecord> getRecordType() {
        return OrganizationRecord.class;
    }

    /**
     * The column <code>exam.t_organization.f_id</code>. ID
     */
    public final TableField<OrganizationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_organization.f_create_time</code>. 创建时间
     */
    public final TableField<OrganizationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_organization.f_name</code>.
     */
    public final TableField<OrganizationRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_organization.f_short_name</code>.
     */
    public final TableField<OrganizationRecord, String> SHORT_NAME = createField("f_short_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_organization.f_code</code>. 组织编码
     */
    public final TableField<OrganizationRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织编码");

    /**
     * The column <code>exam.t_organization.f_ihr_code</code>. ihr新组织编码
     */
    public final TableField<OrganizationRecord, String> IHR_CODE = createField("f_ihr_code", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "ihr新组织编码");

    /**
     * The column <code>exam.t_organization.f_parent_id</code>. 上级组织
     */
    public final TableField<OrganizationRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "上级组织");

    /**
     * The column <code>exam.t_organization.f_path</code>. 当前节点的所有父节点
     */
    public final TableField<OrganizationRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "当前节点的所有父节点");

    /**
     * The column <code>exam.t_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    public final TableField<OrganizationRecord, Integer> CMCC_LEVEL = createField("f_cmcc_level", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "公司分类(移动项目)");

    /**
     * The column <code>exam.t_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    public final TableField<OrganizationRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)");

    /**
     * The column <code>exam.t_organization.f_order</code>. 排序
     */
    public final TableField<OrganizationRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>exam.t_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    public final TableField<OrganizationRecord, String> CMCC_ATTRIBUTE = createField("f_cmcc_attribute", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织机构属性(移动项目)");

    /**
     * The column <code>exam.t_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    public final TableField<OrganizationRecord, String> CMCC_CATEGORY = createField("f_cmcc_category", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织机构类型(移动项目)");

    /**
     * The column <code>exam.t_organization.f_status</code>. 组织状态
     */
    public final TableField<OrganizationRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "组织状态");

    /**
     * The column <code>exam.t_organization.f_company_id</code>. 所属机构
     */
    public final TableField<OrganizationRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属机构");

    /**
     * The column <code>exam.t_organization.f_site_id</code>. 站点id
     */
    public final TableField<OrganizationRecord, String> SITE_ID = createField("f_site_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "站点id");

    /**
     * The column <code>exam.t_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    public final TableField<OrganizationRecord, String> MIS_CODE = createField("f_mis_code", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "MIS省公司简称，用于同步数据");

    /**
     * The column <code>exam.t_organization.f_depth</code>. 表示当前组织深度
     */
    public final TableField<OrganizationRecord, Integer> DEPTH = createField("f_depth", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "表示当前组织深度");

    /**
     * The column <code>exam.t_organization.f_type</code>. 组织类型:0非内外部 1内部组织 2外部组织
     */
    public final TableField<OrganizationRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "组织类型:0非内外部 1内部组织 2外部组织");

    /**
     * The column <code>exam.t_organization.f_area_code</code>. 区号
     */
    public final TableField<OrganizationRecord, String> AREA_CODE = createField("f_area_code", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "区号");

    /**
     * Create a <code>exam.t_organization</code> table reference
     */
    public Organization() {
        this("t_organization", null);
    }

    /**
     * Create an aliased <code>exam.t_organization</code> table reference
     */
    public Organization(String alias) {
        this(alias, ORGANIZATION);
    }

    private Organization(String alias, Table<OrganizationRecord> aliased) {
        this(alias, aliased, null);
    }

    private Organization(String alias, Table<OrganizationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OrganizationRecord> getPrimaryKey() {
        return Keys.KEY_T_ORGANIZATION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OrganizationRecord>> getKeys() {
        return Arrays.<UniqueKey<OrganizationRecord>>asList(Keys.KEY_T_ORGANIZATION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Organization as(String alias) {
        return new Organization(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Organization rename(String name) {
        return new Organization(name, null);
    }
}
