/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.EquipmentTypeRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;


/**
 * 认证考试设备表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EquipmentType extends TableImpl<EquipmentTypeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_equipment_type</code>
     */
    public static final EquipmentType EQUIPMENT_TYPE = new EquipmentType();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<EquipmentTypeRecord> getRecordType() {
        return EquipmentTypeRecord.class;
    }

    /**
     * The column <code>exam.t_equipment_type.f_id</code>. 主键
     */
    public final TableField<EquipmentTypeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_equipment_type.f_create_time</code>.
     */
    public final TableField<EquipmentTypeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_equipment_type.f_code</code>. 设备编码
     */
    public final TableField<EquipmentTypeRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备编码");

    /**
     * The column <code>exam.t_equipment_type.f_name</code>. 设备型号
     */
    public final TableField<EquipmentTypeRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备型号");

    /**
     * The column <code>exam.t_equipment_type.f_delete</code>. 是否已删除，0:否，1：是
     */
    public final TableField<EquipmentTypeRecord, Integer> DELETE = createField("f_delete", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否已删除，0:否，1：是");

    /**
     * Create a <code>exam.t_equipment_type</code> table reference
     */
    public EquipmentType() {
        this("t_equipment_type", null);
    }

    /**
     * Create an aliased <code>exam.t_equipment_type</code> table reference
     */
    public EquipmentType(String alias) {
        this(alias, EQUIPMENT_TYPE);
    }

    private EquipmentType(String alias, Table<EquipmentTypeRecord> aliased) {
        this(alias, aliased, null);
    }

    private EquipmentType(String alias, Table<EquipmentTypeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "认证考试设备表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<EquipmentTypeRecord> getPrimaryKey() {
        return Keys.KEY_T_EQUIPMENT_TYPE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<EquipmentTypeRecord>> getKeys() {
        return Arrays.<UniqueKey<EquipmentTypeRecord>>asList(Keys.KEY_T_EQUIPMENT_TYPE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public EquipmentType as(String alias) {
        return new EquipmentType(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public EquipmentType rename(String name) {
        return new EquipmentType(name, null);
    }
}
