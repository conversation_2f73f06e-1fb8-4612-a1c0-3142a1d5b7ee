/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamStudyPlanConfigRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 考试调研-任务关联表（学习计划）
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamStudyPlanConfig extends TableImpl<ExamStudyPlanConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_exam_study_plan_config</code>
     */
    public static final ExamStudyPlanConfig EXAM_STUDY_PLAN_CONFIG = new ExamStudyPlanConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamStudyPlanConfigRecord> getRecordType() {
        return ExamStudyPlanConfigRecord.class;
    }

    /**
     * The column <code>exam.t_exam_study_plan_config.f_id</code>. ID
     */
    public final TableField<ExamStudyPlanConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_business_id</code>. 业务id
     */
    public final TableField<ExamStudyPlanConfigRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务id");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_business_type</code>. 业务类型：3:考试 4:调研
     */
    public final TableField<ExamStudyPlanConfigRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "业务类型：3:考试 4:调研");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_push_learning_plan</code>. 是否推送学习计划 1是，0否
     */
    public final TableField<ExamStudyPlanConfigRecord, Integer> PUSH_LEARNING_PLAN = createField("f_push_learning_plan", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否推送学习计划 1是，0否");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_remind_type</code>. 提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开
     */
    public final TableField<ExamStudyPlanConfigRecord, String> REMIND_TYPE = createField("f_remind_type", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "提醒类型  1:站内信  2:邮件 3:APP  4:短信  5:OA  以，隔开");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_remind_time</code>. 提醒时间
     */
    public final TableField<ExamStudyPlanConfigRecord, Long> REMIND_TIME = createField("f_remind_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提醒时间");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_create_time</code>. 创建时间
     */
    public final TableField<ExamStudyPlanConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_is_push</code>. 是否已经推送 1是，0否
     */
    public final TableField<ExamStudyPlanConfigRecord, Integer> IS_PUSH = createField("f_is_push", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否已经推送 1是，0否");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_handle_status</code>. 0: 未处理 1:推送处理成功 2：撤销处理成功
     */
    public final TableField<ExamStudyPlanConfigRecord, Integer> HANDLE_STATUS = createField("f_handle_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "0: 未处理 1:推送处理成功 2：撤销处理成功");

    /**
     * The column <code>exam.t_exam_study_plan_config.f_modify_date</code>. 修改时间
     */
    public final TableField<ExamStudyPlanConfigRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_exam_study_plan_config</code> table reference
     */
    public ExamStudyPlanConfig() {
        this("t_exam_study_plan_config", null);
    }

    /**
     * Create an aliased <code>exam.t_exam_study_plan_config</code> table reference
     */
    public ExamStudyPlanConfig(String alias) {
        this(alias, EXAM_STUDY_PLAN_CONFIG);
    }

    private ExamStudyPlanConfig(String alias, Table<ExamStudyPlanConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamStudyPlanConfig(String alias, Table<ExamStudyPlanConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试调研-任务关联表（学习计划）");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamStudyPlanConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_STUDY_PLAN_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamStudyPlanConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamStudyPlanConfigRecord>>asList(Keys.KEY_T_EXAM_STUDY_PLAN_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamStudyPlanConfig as(String alias) {
        return new ExamStudyPlanConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamStudyPlanConfig rename(String name) {
        return new ExamStudyPlanConfig(name, null);
    }
}
