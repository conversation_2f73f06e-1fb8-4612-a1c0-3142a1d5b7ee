/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionDepotRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionDepot extends TableImpl<QuestionDepotRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_depot</code>
     */
    public static final QuestionDepot QUESTION_DEPOT = new QuestionDepot();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionDepotRecord> getRecordType() {
        return QuestionDepotRecord.class;
    }

    /**
     * The column <code>exam.t_question_depot.f_id</code>.
     */
    public final TableField<QuestionDepotRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_depot.f_create_time</code>.
     */
    public final TableField<QuestionDepotRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_name</code>.
     */
    public final TableField<QuestionDepotRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_parent_id</code>.
     */
    public final TableField<QuestionDepotRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_sequence</code>.
     */
    public final TableField<QuestionDepotRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_code</code>.
     */
    public final TableField<QuestionDepotRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(50).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_state</code>.
     */
    public final TableField<QuestionDepotRecord, Integer> STATE = createField("f_state", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_auth_to_lower</code>.
     */
    public final TableField<QuestionDepotRecord, Integer> AUTH_TO_LOWER = createField("f_auth_to_lower", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_path</code>.
     */
    public final TableField<QuestionDepotRecord, String> PATH = createField("f_path", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_organization_id</code>.
     */
    public final TableField<QuestionDepotRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_depot.f_type</code>. 目录类型，1：普通 2：认证 3：同步
     */
    public final TableField<QuestionDepotRecord, Integer> TYPE = createField("f_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "目录类型，1：普通 2：认证 3：同步");

    /**
     * The column <code>exam.t_question_depot.f_create_member</code>. 创建人
     */
    public final TableField<QuestionDepotRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>exam.t_question_depot.f_is_set_encrypt</code>. 试题目录是否加密，0：否，1：是
     */
    public final TableField<QuestionDepotRecord, Integer> IS_SET_ENCRYPT = createField("f_is_set_encrypt", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "试题目录是否加密，0：否，1：是");

    /**
     * The column <code>exam.t_question_depot.f_encrypt</code>. 试题目录密码
     */
    public final TableField<QuestionDepotRecord, String> ENCRYPT = createField("f_encrypt", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题目录密码");

    /**
     * The column <code>exam.t_question_depot.f_modify_date</code>. 修改时间
     */
    public final TableField<QuestionDepotRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_question_depot</code> table reference
     */
    public QuestionDepot() {
        this("t_question_depot", null);
    }

    /**
     * Create an aliased <code>exam.t_question_depot</code> table reference
     */
    public QuestionDepot(String alias) {
        this(alias, QUESTION_DEPOT);
    }

    private QuestionDepot(String alias, Table<QuestionDepotRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionDepot(String alias, Table<QuestionDepotRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionDepotRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_DEPOT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionDepotRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionDepotRecord>>asList(Keys.KEY_T_QUESTION_DEPOT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionDepot as(String alias) {
        return new QuestionDepot(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionDepot rename(String name) {
        return new QuestionDepot(name, null);
    }
}
