/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionAttrRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionAttr extends TableImpl<QuestionAttrRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_attr</code>
     */
    public static final QuestionAttr QUESTION_ATTR = new QuestionAttr();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionAttrRecord> getRecordType() {
        return QuestionAttrRecord.class;
    }

    /**
     * The column <code>exam.t_question_attr.f_id</code>.
     */
    public final TableField<QuestionAttrRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_attr.f_create_time</code>.
     */
    public final TableField<QuestionAttrRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_attr.f_type</code>. 类型
     */
    public final TableField<QuestionAttrRecord, String> TYPE = createField("f_type", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "类型");

    /**
     * The column <code>exam.t_question_attr.f_name</code>. 属性名称
     */
    public final TableField<QuestionAttrRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "属性名称");

    /**
     * The column <code>exam.t_question_attr.f_value</code>. 属性值，富文本格式
     */
    public final TableField<QuestionAttrRecord, String> VALUE = createField("f_value", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "属性值，富文本格式");

    /**
     * The column <code>exam.t_question_attr.f_question_id</code>. 所属试题
     */
    public final TableField<QuestionAttrRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试题");

    /**
     * The column <code>exam.t_question_attr.f_score</code>. 评估问卷的题目选项分数
     */
    public final TableField<QuestionAttrRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "评估问卷的题目选项分数");

    /**
     * The column <code>exam.t_question_attr.f_value_text</code>. 属性值，纯文本格式
     */
    public final TableField<QuestionAttrRecord, String> VALUE_TEXT = createField("f_value_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "属性值，纯文本格式");

    /**
     * The column <code>exam.t_question_attr.f_old_type</code>. 旧版本类型
     */
    public final TableField<QuestionAttrRecord, String> OLD_TYPE = createField("f_old_type", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "旧版本类型");

    /**
     * Create a <code>exam.t_question_attr</code> table reference
     */
    public QuestionAttr() {
        this("t_question_attr", null);
    }

    /**
     * Create an aliased <code>exam.t_question_attr</code> table reference
     */
    public QuestionAttr(String alias) {
        this(alias, QUESTION_ATTR);
    }

    private QuestionAttr(String alias, Table<QuestionAttrRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionAttr(String alias, Table<QuestionAttrRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionAttrRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_ATTR_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionAttrRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionAttrRecord>>asList(Keys.KEY_T_QUESTION_ATTR_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionAttr as(String alias) {
        return new QuestionAttr(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionAttr rename(String name) {
        return new QuestionAttr(name, null);
    }
}
