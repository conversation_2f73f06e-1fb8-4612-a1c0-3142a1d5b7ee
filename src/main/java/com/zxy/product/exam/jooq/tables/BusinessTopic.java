/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.BusinessTopicRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 资源话题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BusinessTopic extends TableImpl<BusinessTopicRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_business_topic</code>
     */
    public static final BusinessTopic BUSINESS_TOPIC = new BusinessTopic();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<BusinessTopicRecord> getRecordType() {
        return BusinessTopicRecord.class;
    }

    /**
     * The column <code>exam.t_business_topic.f_id</code>.
     */
    public final TableField<BusinessTopicRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_business_topic.f_business_id</code>.
     */
    public final TableField<BusinessTopicRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_business_topic.f_business_type</code>. 资源类型  1:考试；2-调研
     */
    public final TableField<BusinessTopicRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "资源类型  1:考试；2-调研");

    /**
     * The column <code>exam.t_business_topic.f_topic_id</code>.
     */
    public final TableField<BusinessTopicRecord, String> TOPIC_ID = createField("f_topic_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_business_topic.f_create_time</code>.
     */
    public final TableField<BusinessTopicRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_business_topic.f_modify_date</code>. 修改时间
     */
    public final TableField<BusinessTopicRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_business_topic</code> table reference
     */
    public BusinessTopic() {
        this("t_business_topic", null);
    }

    /**
     * Create an aliased <code>exam.t_business_topic</code> table reference
     */
    public BusinessTopic(String alias) {
        this(alias, BUSINESS_TOPIC);
    }

    private BusinessTopic(String alias, Table<BusinessTopicRecord> aliased) {
        this(alias, aliased, null);
    }

    private BusinessTopic(String alias, Table<BusinessTopicRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "资源话题表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<BusinessTopicRecord> getPrimaryKey() {
        return Keys.KEY_T_BUSINESS_TOPIC_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<BusinessTopicRecord>> getKeys() {
        return Arrays.<UniqueKey<BusinessTopicRecord>>asList(Keys.KEY_T_BUSINESS_TOPIC_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public BusinessTopic as(String alias) {
        return new BusinessTopic(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public BusinessTopic rename(String name) {
        return new BusinessTopic(name, null);
    }
}
