/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionRecoveryRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionRecovery extends TableImpl<QuestionRecoveryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_recovery</code>
     */
    public static final QuestionRecovery QUESTION_RECOVERY = new QuestionRecovery();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionRecoveryRecord> getRecordType() {
        return QuestionRecoveryRecord.class;
    }

    /**
     * The column <code>exam.t_question_recovery.f_id</code>.
     */
    public final TableField<QuestionRecoveryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_recovery.f_create_time</code>.
     */
    public final TableField<QuestionRecoveryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_recovery.f_member_id</code>. 纠错用户
     */
    public final TableField<QuestionRecoveryRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "纠错用户");

    /**
     * The column <code>exam.t_question_recovery.f_reason</code>. 原因
     */
    public final TableField<QuestionRecoveryRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "原因");

    /**
     * The column <code>exam.t_question_recovery.f_question_id</code>. 所属试题
     */
    public final TableField<QuestionRecoveryRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试题");

    /**
     * Create a <code>exam.t_question_recovery</code> table reference
     */
    public QuestionRecovery() {
        this("t_question_recovery", null);
    }

    /**
     * Create an aliased <code>exam.t_question_recovery</code> table reference
     */
    public QuestionRecovery(String alias) {
        this(alias, QUESTION_RECOVERY);
    }

    private QuestionRecovery(String alias, Table<QuestionRecoveryRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionRecovery(String alias, Table<QuestionRecoveryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionRecoveryRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_RECOVERY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionRecoveryRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionRecoveryRecord>>asList(Keys.KEY_T_QUESTION_RECOVERY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionRecovery as(String alias) {
        return new QuestionRecovery(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionRecovery rename(String name) {
        return new QuestionRecovery(name, null);
    }
}
