/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionCountRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionCount extends TableImpl<QuestionCountRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_count</code>
     */
    public static final QuestionCount QUESTION_COUNT = new QuestionCount();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionCountRecord> getRecordType() {
        return QuestionCountRecord.class;
    }

    /**
     * The column <code>exam.t_question_count.f_id</code>.
     */
    public final TableField<QuestionCountRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_count.f_create_time</code>.
     */
    public final TableField<QuestionCountRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_count.f_organization_id</code>.
     */
    public final TableField<QuestionCountRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_count.f_question_depot_id</code>.
     */
    public final TableField<QuestionCountRecord, String> QUESTION_DEPOT_ID = createField("f_question_depot_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_count.f_count_content</code>. 按试题类型 难度统计数目json
     */
    public final TableField<QuestionCountRecord, String> COUNT_CONTENT = createField("f_count_content", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "按试题类型 难度统计数目json");

    /**
     * Create a <code>exam.t_question_count</code> table reference
     */
    public QuestionCount() {
        this("t_question_count", null);
    }

    /**
     * Create an aliased <code>exam.t_question_count</code> table reference
     */
    public QuestionCount(String alias) {
        this(alias, QUESTION_COUNT);
    }

    private QuestionCount(String alias, Table<QuestionCountRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionCount(String alias, Table<QuestionCountRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionCountRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_COUNT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionCountRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionCountRecord>>asList(Keys.KEY_T_QUESTION_COUNT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionCount as(String alias) {
        return new QuestionCount(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionCount rename(String name) {
        return new QuestionCount(name, null);
    }
}
