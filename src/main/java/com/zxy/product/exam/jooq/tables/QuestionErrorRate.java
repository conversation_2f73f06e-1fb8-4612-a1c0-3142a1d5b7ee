/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionErrorRateRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 试题易错率统计
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionErrorRate extends TableImpl<QuestionErrorRateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_error_rate</code>
     */
    public static final QuestionErrorRate QUESTION_ERROR_RATE = new QuestionErrorRate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionErrorRateRecord> getRecordType() {
        return QuestionErrorRateRecord.class;
    }

    /**
     * The column <code>exam.t_question_error_rate.f_id</code>.
     */
    public final TableField<QuestionErrorRateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_error_rate.f_create_time</code>. 创建时间
     */
    public final TableField<QuestionErrorRateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_question_error_rate.f_question_id</code>. 试题id
     */
    public final TableField<QuestionErrorRateRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题id");

    /**
     * The column <code>exam.t_question_error_rate.f_all_count</code>. 总数量
     */
    public final TableField<QuestionErrorRateRecord, Integer> ALL_COUNT = createField("f_all_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "总数量");

    /**
     * The column <code>exam.t_question_error_rate.f_wrong_count</code>. 答错的数量
     */
    public final TableField<QuestionErrorRateRecord, Integer> WRONG_COUNT = createField("f_wrong_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "答错的数量");

    /**
     * Create a <code>exam.t_question_error_rate</code> table reference
     */
    public QuestionErrorRate() {
        this("t_question_error_rate", null);
    }

    /**
     * Create an aliased <code>exam.t_question_error_rate</code> table reference
     */
    public QuestionErrorRate(String alias) {
        this(alias, QUESTION_ERROR_RATE);
    }

    private QuestionErrorRate(String alias, Table<QuestionErrorRateRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionErrorRate(String alias, Table<QuestionErrorRateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试题易错率统计");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionErrorRateRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_ERROR_RATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionErrorRateRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionErrorRateRecord>>asList(Keys.KEY_T_QUESTION_ERROR_RATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionErrorRate as(String alias) {
        return new QuestionErrorRate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionErrorRate rename(String name) {
        return new QuestionErrorRate(name, null);
    }
}
