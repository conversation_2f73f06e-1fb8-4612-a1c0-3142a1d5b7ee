/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 数智测评结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDigitalIntelligenceResult extends Serializable {

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_id</code>. 记录ID
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_id</code>. 记录ID
     */
    public String getId();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_name</code>. 用户名称
     */
    public void setName(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_name</code>. 用户名称
     */
    public String getName();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_ability_tags</code>. 能力标签，多个中间用 | 隔开
     */
    public void setAbilityTags(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_ability_tags</code>. 能力标签，多个中间用 | 隔开
     */
    public String getAbilityTags();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_strength_enhancement</code>. 优势强化，多个中间用 | 隔开
     */
    public void setStrengthEnhancement(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_strength_enhancement</code>. 优势强化，多个中间用 | 隔开
     */
    public String getStrengthEnhancement();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_skill_improvement</code>. 短板提升，多个中间用 | 隔开
     */
    public void setSkillImprovement(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_skill_improvement</code>. 短板提升，多个中间用 | 隔开
     */
    public String getSkillImprovement();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_short_term_plan</code>. 推荐发展路径--短期，多个中间用 | 隔开
     */
    public void setShortTermPlan(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_short_term_plan</code>. 推荐发展路径--短期，多个中间用 | 隔开
     */
    public String getShortTermPlan();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_medium_term_plan</code>. 推荐发展路径--长期，多个中间用 | 隔开
     */
    public void setMediumTermPlan(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_medium_term_plan</code>. 推荐发展路径--长期，多个中间用 | 隔开
     */
    public String getMediumTermPlan();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_recommended_courses_id</code>. 推荐课程，多个 id 中间用 , 隔开
     */
    public void setRecommendedCoursesId(String value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_recommended_courses_id</code>. 推荐课程，多个 id 中间用 , 隔开
     */
    public String getRecommendedCoursesId();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_is_view</code>. 是否查看过结果:0 没有；1 已经查看
     */
    public void setIsView(Integer value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_is_view</code>. 是否查看过结果:0 没有；1 已经查看
     */
    public Integer getIsView();

    /**
     * Setter for <code>exam.t_digital_intelligence_result.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_digital_intelligence_result.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDigitalIntelligenceResult
     */
    public void from(IDigitalIntelligenceResult from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDigitalIntelligenceResult
     */
    public <E extends IDigitalIntelligenceResult> E into(E into);
}
