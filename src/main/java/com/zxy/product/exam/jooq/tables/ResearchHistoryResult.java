/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ResearchHistoryResultRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchHistoryResult extends TableImpl<ResearchHistoryResultRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_research_history_result</code>
     */
    public static final ResearchHistoryResult RESEARCH_HISTORY_RESULT = new ResearchHistoryResult();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchHistoryResultRecord> getRecordType() {
        return ResearchHistoryResultRecord.class;
    }

    /**
     * The column <code>exam.t_research_history_result.f_id</code>. 主键
     */
    public final TableField<ResearchHistoryResultRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "主键");

    /**
     * The column <code>exam.t_research_history_result.f_member_id</code>.
     */
    public final TableField<ResearchHistoryResultRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_research_history_result.f_study_total_time</code>. 个人年度学习总时长
     */
    public final TableField<ResearchHistoryResultRecord, Double> STUDY_TOTAL_TIME = createField("f_study_total_time", org.jooq.impl.SQLDataType.FLOAT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.FLOAT)), this, "个人年度学习总时长");

    /**
     * The column <code>exam.t_research_history_result.f_type</code>. 学习占比最多的课程类型：1：管理，2：综合，3：技术，4：业务
     */
    public final TableField<ResearchHistoryResultRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习占比最多的课程类型：1：管理，2：综合，3：技术，4：业务");

    /**
     * The column <code>exam.t_research_history_result.f_study_max_time_name</code>. 学习时长最长的课程名称
     */
    public final TableField<ResearchHistoryResultRecord, String> STUDY_MAX_TIME_NAME = createField("f_study_max_time_name", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "学习时长最长的课程名称");

    /**
     * The column <code>exam.t_research_history_result.f_study_latest_day</code>. 学习时间最晚的一天
     */
    public final TableField<ResearchHistoryResultRecord, String> STUDY_LATEST_DAY = createField("f_study_latest_day", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "学习时间最晚的一天");

    /**
     * The column <code>exam.t_research_history_result.f_ranking</code>. 学习时长在组织中排名
     */
    public final TableField<ResearchHistoryResultRecord, Integer> RANKING = createField("f_ranking", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习时长在组织中排名");

    /**
     * Create a <code>exam.t_research_history_result</code> table reference
     */
    public ResearchHistoryResult() {
        this("t_research_history_result", null);
    }

    /**
     * Create an aliased <code>exam.t_research_history_result</code> table reference
     */
    public ResearchHistoryResult(String alias) {
        this(alias, RESEARCH_HISTORY_RESULT);
    }

    private ResearchHistoryResult(String alias, Table<ResearchHistoryResultRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchHistoryResult(String alias, Table<ResearchHistoryResultRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchHistoryResultRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_HISTORY_RESULT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchHistoryResultRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchHistoryResultRecord>>asList(Keys.KEY_T_RESEARCH_HISTORY_RESULT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchHistoryResult as(String alias) {
        return new ResearchHistoryResult(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchHistoryResult rename(String name) {
        return new ResearchHistoryResult(name, null);
    }
}
