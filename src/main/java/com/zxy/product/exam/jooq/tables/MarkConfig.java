/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.MarkConfigRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MarkConfig extends TableImpl<MarkConfigRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_mark_config</code>
     */
    public static final MarkConfig MARK_CONFIG = new MarkConfig();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MarkConfigRecord> getRecordType() {
        return MarkConfigRecord.class;
    }

    /**
     * The column <code>exam.t_mark_config.f_id</code>. id
     */
    public final TableField<MarkConfigRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>exam.t_mark_config.f_create_time</code>.
     */
    public final TableField<MarkConfigRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_mark_config.f_exam_id</code>. 考试
     */
    public final TableField<MarkConfigRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试");

    /**
     * The column <code>exam.t_mark_config.f_member_id</code>. 评卷老师
     */
    public final TableField<MarkConfigRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评卷老师");

    /**
     * The column <code>exam.t_mark_config.f_type</code>. 评卷方式
     */
    public final TableField<MarkConfigRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "评卷方式");

    /**
     * The column <code>exam.t_mark_config.f_type_id</code>. 类型ID （type:1,paperClassId)(type:2,questionType)(type3:questionId)
     */
    public final TableField<MarkConfigRecord, String> TYPE_ID = createField("f_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "类型ID （type:1,paperClassId)(type:2,questionType)(type3:questionId)");

    /**
     * Create a <code>exam.t_mark_config</code> table reference
     */
    public MarkConfig() {
        this("t_mark_config", null);
    }

    /**
     * Create an aliased <code>exam.t_mark_config</code> table reference
     */
    public MarkConfig(String alias) {
        this(alias, MARK_CONFIG);
    }

    private MarkConfig(String alias, Table<MarkConfigRecord> aliased) {
        this(alias, aliased, null);
    }

    private MarkConfig(String alias, Table<MarkConfigRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MarkConfigRecord> getPrimaryKey() {
        return Keys.KEY_T_MARK_CONFIG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MarkConfigRecord>> getKeys() {
        return Arrays.<UniqueKey<MarkConfigRecord>>asList(Keys.KEY_T_MARK_CONFIG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkConfig as(String alias) {
        return new MarkConfig(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MarkConfig rename(String name) {
        return new MarkConfig(name, null);
    }
}
