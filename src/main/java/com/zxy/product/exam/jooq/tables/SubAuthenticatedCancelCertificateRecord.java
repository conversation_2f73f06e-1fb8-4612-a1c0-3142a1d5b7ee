/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SubAuthenticatedCancelCertificateRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;


/**
 * 子认证-考试证书取消证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedCancelCertificateRecord extends TableImpl<SubAuthenticatedCancelCertificateRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_sub_authenticated_cancel_certificate_record</code>
     */
    public static final SubAuthenticatedCancelCertificateRecord SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD = new SubAuthenticatedCancelCertificateRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubAuthenticatedCancelCertificateRecordRecord> getRecordType() {
        return SubAuthenticatedCancelCertificateRecordRecord.class;
    }

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_id</code>. id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_create_time</code>. 创建时间
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> SUB_AUTHENTICATED_ID = createField("f_sub_authenticated_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子认证id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_member_id</code>. 学员id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "学员id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_operator_id</code>. 操作人id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> OPERATOR_ID = createField("f_operator_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "操作人id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_exam_id</code>. 考试id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_cancel_time</code>. 取消时间
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, Long> CANCEL_TIME = createField("f_cancel_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "取消时间");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_publish_time</code>. 发证时间
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, Long> PUBLISH_TIME = createField("f_publish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "发证时间");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_id</code>. 证明材料id
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证明材料id");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_attachment_name</code>. 证明材料名称
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证明材料名称");

    /**
     * The column <code>exam.t_sub_authenticated_cancel_certificate_record.f_reason</code>. 原因描述
     */
    public final TableField<SubAuthenticatedCancelCertificateRecordRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(64).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "原因描述");

    /**
     * Create a <code>exam.t_sub_authenticated_cancel_certificate_record</code> table reference
     */
    public SubAuthenticatedCancelCertificateRecord() {
        this("t_sub_authenticated_cancel_certificate_record", null);
    }

    /**
     * Create an aliased <code>exam.t_sub_authenticated_cancel_certificate_record</code> table reference
     */
    public SubAuthenticatedCancelCertificateRecord(String alias) {
        this(alias, SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD);
    }

    private SubAuthenticatedCancelCertificateRecord(String alias, Table<SubAuthenticatedCancelCertificateRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubAuthenticatedCancelCertificateRecord(String alias, Table<SubAuthenticatedCancelCertificateRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "子认证-考试证书取消证书记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubAuthenticatedCancelCertificateRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubAuthenticatedCancelCertificateRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<SubAuthenticatedCancelCertificateRecordRecord>>asList(Keys.KEY_T_SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCancelCertificateRecord as(String alias) {
        return new SubAuthenticatedCancelCertificateRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubAuthenticatedCancelCertificateRecord rename(String name) {
        return new SubAuthenticatedCancelCertificateRecord(name, null);
    }
}
