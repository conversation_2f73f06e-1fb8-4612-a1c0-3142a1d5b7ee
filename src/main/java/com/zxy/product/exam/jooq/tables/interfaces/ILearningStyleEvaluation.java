/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 学习风格测评表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILearningStyleEvaluation extends Serializable {

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_hearing</code>. 听觉型
     */
    public void setHearing(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_hearing</code>. 听觉型
     */
    public String getHearing();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_viewing</code>. 视觉型
     */
    public void setViewing(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_viewing</code>. 视觉型
     */
    public String getViewing();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_rw</code>. 读写型
     */
    public void setRw(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_rw</code>. 读写型
     */
    public String getRw();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_action</code>. 动作型
     */
    public void setAction(String value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_action</code>. 动作型
     */
    public String getAction();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_result</code>. 测评结果(视觉型4,听觉型3,读写型2,动觉型1)
     */
    public void setResult(Integer value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_result</code>. 测评结果(视觉型4,听觉型3,读写型2,动觉型1)
     */
    public Integer getResult();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_learning_style_evaluation.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>exam.t_learning_style_evaluation.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILearningStyleEvaluation
     */
    public void from(ILearningStyleEvaluation from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILearningStyleEvaluation
     */
    public <E extends ILearningStyleEvaluation> E into(E into);
}
