/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperInstanceQuestionCopy_2028Record;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 试卷实例与试题副本关联_2028
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperInstanceQuestionCopy_2028 extends TableImpl<PaperInstanceQuestionCopy_2028Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_instance_question_copy_2028</code>
     */
    public static final PaperInstanceQuestionCopy_2028 PAPER_INSTANCE_QUESTION_COPY_2028 = new PaperInstanceQuestionCopy_2028();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperInstanceQuestionCopy_2028Record> getRecordType() {
        return PaperInstanceQuestionCopy_2028Record.class;
    }

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_id</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_paper_instance_id</code>. 所属试卷实例
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, String> PAPER_INSTANCE_ID = createField("f_paper_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试卷实例");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_question_copy_id</code>. 所属试题副本
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, String> QUESTION_COPY_ID = createField("f_question_copy_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试题副本");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_create_time</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_score</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_sequence</code>.
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_paper_instance_question_copy_2028.f_modify_date</code>. 修改时间
     */
    public final TableField<PaperInstanceQuestionCopy_2028Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_paper_instance_question_copy_2028</code> table reference
     */
    public PaperInstanceQuestionCopy_2028() {
        this("t_paper_instance_question_copy_2028", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_instance_question_copy_2028</code> table reference
     */
    public PaperInstanceQuestionCopy_2028(String alias) {
        this(alias, PAPER_INSTANCE_QUESTION_COPY_2028);
    }

    private PaperInstanceQuestionCopy_2028(String alias, Table<PaperInstanceQuestionCopy_2028Record> aliased) {
        this(alias, aliased, null);
    }

    private PaperInstanceQuestionCopy_2028(String alias, Table<PaperInstanceQuestionCopy_2028Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试卷实例与试题副本关联_2028");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperInstanceQuestionCopy_2028Record> getPrimaryKey() {
        return Keys.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2028_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperInstanceQuestionCopy_2028Record>> getKeys() {
        return Arrays.<UniqueKey<PaperInstanceQuestionCopy_2028Record>>asList(Keys.KEY_T_PAPER_INSTANCE_QUESTION_COPY_2028_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperInstanceQuestionCopy_2028 as(String alias) {
        return new PaperInstanceQuestionCopy_2028(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperInstanceQuestionCopy_2028 rename(String name) {
        return new PaperInstanceQuestionCopy_2028(name, null);
    }
}
