/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridSignupRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 网格长考试报名表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridSignup extends TableImpl<GridSignupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_grid_signup</code>
     */
    public static final GridSignup GRID_SIGNUP = new GridSignup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridSignupRecord> getRecordType() {
        return GridSignupRecord.class;
    }

    /**
     * The column <code>exam-stu.t_grid_signup.f_id</code>. 主键
     */
    public final TableField<GridSignupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam-stu.t_grid_signup.f_create_time</code>. 创建时间
     */
    public final TableField<GridSignupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_grid_signup.f_exam_id</code>. 考试id
     */
    public final TableField<GridSignupRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "考试id");

    /**
     * The column <code>exam-stu.t_grid_signup.f_member_id</code>. 人员id
     */
    public final TableField<GridSignupRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员id");

    /**
     * The column <code>exam-stu.t_grid_signup.f_status</code>. 审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名
     */
    public final TableField<GridSignupRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名");

    /**
     * The column <code>exam-stu.t_grid_signup.f_audit_member_id</code>. 报名审核人
     */
    public final TableField<GridSignupRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "报名审核人");

    /**
     * Create a <code>exam-stu.t_grid_signup</code> table reference
     */
    public GridSignup() {
        this("t_grid_signup", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_grid_signup</code> table reference
     */
    public GridSignup(String alias) {
        this(alias, GRID_SIGNUP);
    }

    private GridSignup(String alias, Table<GridSignupRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridSignup(String alias, Table<GridSignupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长考试报名表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridSignupRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_SIGNUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridSignupRecord>> getKeys() {
        return Arrays.<UniqueKey<GridSignupRecord>>asList(Keys.KEY_T_GRID_SIGNUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridSignup as(String alias) {
        return new GridSignup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridSignup rename(String name) {
        return new GridSignup(name, null);
    }
}
