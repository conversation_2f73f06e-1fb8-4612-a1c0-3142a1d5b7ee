/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnnouncementRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;


/**
 * 通知公告
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Announcement extends TableImpl<AnnouncementRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_announcement</code>
     */
    public static final Announcement ANNOUNCEMENT = new Announcement();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnnouncementRecord> getRecordType() {
        return AnnouncementRecord.class;
    }

    /**
     * The column <code>exam.t_announcement.f_id</code>. ID
     */
    public final TableField<AnnouncementRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_announcement.f_create_time</code>.
     */
    public final TableField<AnnouncementRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_announcement.f_title</code>. 公告标题
     */
    public final TableField<AnnouncementRecord, String> TITLE = createField("f_title", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "公告标题");

    /**
     * The column <code>exam.t_announcement.f_description</code>. 公告描述
     */
    public final TableField<AnnouncementRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "公告描述");

    /**
     * The column <code>exam.t_announcement.f_start_time</code>. 公告开放时间
     */
    public final TableField<AnnouncementRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "公告开放时间");

    /**
     * The column <code>exam.t_announcement.f_end_time</code>. 公告关闭时间
     */
    public final TableField<AnnouncementRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "公告关闭时间");

    /**
     * The column <code>exam.t_announcement.f_create_member_id</code>. 创建者
     */
    public final TableField<AnnouncementRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建者");

    /**
     * The column <code>exam.t_announcement.f_top_status</code>. 顶置状态 0未项置 1已顶置
     */
    public final TableField<AnnouncementRecord, Integer> TOP_STATUS = createField("f_top_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "顶置状态 0未项置 1已顶置");

    /**
     * The column <code>exam.t_announcement.f_organization_id</code>. 部门id
     */
    public final TableField<AnnouncementRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门id");

    /**
     * Create a <code>exam.t_announcement</code> table reference
     */
    public Announcement() {
        this("t_announcement", null);
    }

    /**
     * Create an aliased <code>exam.t_announcement</code> table reference
     */
    public Announcement(String alias) {
        this(alias, ANNOUNCEMENT);
    }

    private Announcement(String alias, Table<AnnouncementRecord> aliased) {
        this(alias, aliased, null);
    }

    private Announcement(String alias, Table<AnnouncementRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "通知公告");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnnouncementRecord> getPrimaryKey() {
        return Keys.KEY_T_ANNOUNCEMENT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnnouncementRecord>> getKeys() {
        return Arrays.<UniqueKey<AnnouncementRecord>>asList(Keys.KEY_T_ANNOUNCEMENT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Announcement as(String alias) {
        return new Announcement(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Announcement rename(String name) {
        return new Announcement(name, null);
    }
}
