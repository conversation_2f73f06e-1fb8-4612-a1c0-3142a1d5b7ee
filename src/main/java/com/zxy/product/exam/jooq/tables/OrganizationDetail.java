/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.OrganizationDetailRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationDetail extends TableImpl<OrganizationDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_organization_detail</code>
     */
    public static final OrganizationDetail ORGANIZATION_DETAIL = new OrganizationDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganizationDetailRecord> getRecordType() {
        return OrganizationDetailRecord.class;
    }

    /**
     * The column <code>exam.t_organization_detail.f_id</code>. ID
     */
    public final TableField<OrganizationDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_organization_detail.f_root</code>.  父节点
     */
    public final TableField<OrganizationDetailRecord, String> ROOT = createField("f_root", org.jooq.impl.SQLDataType.VARCHAR.length(45).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, " 父节点");

    /**
     * The column <code>exam.t_organization_detail.f_sub</code>. 子节点
     */
    public final TableField<OrganizationDetailRecord, String> SUB = createField("f_sub", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子节点");

    /**
     * The column <code>exam.t_organization_detail.f_create_time</code>. 创建时间
     */
    public final TableField<OrganizationDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>exam.t_organization_detail</code> table reference
     */
    public OrganizationDetail() {
        this("t_organization_detail", null);
    }

    /**
     * Create an aliased <code>exam.t_organization_detail</code> table reference
     */
    public OrganizationDetail(String alias) {
        this(alias, ORGANIZATION_DETAIL);
    }

    private OrganizationDetail(String alias, Table<OrganizationDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private OrganizationDetail(String alias, Table<OrganizationDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OrganizationDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_ORGANIZATION_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OrganizationDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<OrganizationDetailRecord>>asList(Keys.KEY_T_ORGANIZATION_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationDetail as(String alias) {
        return new OrganizationDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationDetail rename(String name) {
        return new OrganizationDetail(name, null);
    }
}
