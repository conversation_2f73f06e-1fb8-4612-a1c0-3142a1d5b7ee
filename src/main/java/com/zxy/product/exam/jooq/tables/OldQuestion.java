/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.OldQuestionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 原试题表中存在易错率的数据
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OldQuestion extends TableImpl<OldQuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_old_question</code>
     */
    public static final OldQuestion OLD_QUESTION = new OldQuestion();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OldQuestionRecord> getRecordType() {
        return OldQuestionRecord.class;
    }

    /**
     * The column <code>exam.t_old_question.f_id</code>.
     */
    public final TableField<OldQuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_old_question.f_create_time</code>. 创建时间
     */
    public final TableField<OldQuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_old_question.f_question_id</code>. 试题id
     */
    public final TableField<OldQuestionRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题id");

    /**
     * The column <code>exam.t_old_question.f_error_rate</code>. 易错率
     */
    public final TableField<OldQuestionRecord, Integer> ERROR_RATE = createField("f_error_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "易错率");

    /**
     * Create a <code>exam.t_old_question</code> table reference
     */
    public OldQuestion() {
        this("t_old_question", null);
    }

    /**
     * Create an aliased <code>exam.t_old_question</code> table reference
     */
    public OldQuestion(String alias) {
        this(alias, OLD_QUESTION);
    }

    private OldQuestion(String alias, Table<OldQuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private OldQuestion(String alias, Table<OldQuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "原试题表中存在易错率的数据");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OldQuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_OLD_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OldQuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<OldQuestionRecord>>asList(Keys.KEY_T_OLD_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OldQuestion as(String alias) {
        return new OldQuestion(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OldQuestion rename(String name) {
        return new OldQuestion(name, null);
    }
}
