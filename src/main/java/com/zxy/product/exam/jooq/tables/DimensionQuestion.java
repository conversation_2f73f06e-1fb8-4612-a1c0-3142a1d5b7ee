/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.DimensionQuestionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DimensionQuestion extends TableImpl<DimensionQuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_dimension_question</code>
     */
    public static final DimensionQuestion DIMENSION_QUESTION = new DimensionQuestion();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DimensionQuestionRecord> getRecordType() {
        return DimensionQuestionRecord.class;
    }

    /**
     * The column <code>exam.t_dimension_question.f_id</code>.
     */
    public final TableField<DimensionQuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_dimension_question.f_create_time</code>. 时间
     */
    public final TableField<DimensionQuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "时间");

    /**
     * The column <code>exam.t_dimension_question.f_dimension_id</code>. 维度
     */
    public final TableField<DimensionQuestionRecord, String> DIMENSION_ID = createField("f_dimension_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "维度");

    /**
     * The column <code>exam.t_dimension_question.f_question_id</code>. 题目
     */
    public final TableField<DimensionQuestionRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "题目");

    /**
     * The column <code>exam.t_dimension_question.f_score</code>. 分数
     */
    public final TableField<DimensionQuestionRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分数");

    /**
     * The column <code>exam.t_dimension_question.f_idea</code>. 意见
     */
    public final TableField<DimensionQuestionRecord, String> IDEA = createField("f_idea", org.jooq.impl.SQLDataType.VARCHAR.length(300).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "意见");

    /**
     * The column <code>exam.t_dimension_question.f_order</code>. 排序
     */
    public final TableField<DimensionQuestionRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * Create a <code>exam.t_dimension_question</code> table reference
     */
    public DimensionQuestion() {
        this("t_dimension_question", null);
    }

    /**
     * Create an aliased <code>exam.t_dimension_question</code> table reference
     */
    public DimensionQuestion(String alias) {
        this(alias, DIMENSION_QUESTION);
    }

    private DimensionQuestion(String alias, Table<DimensionQuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private DimensionQuestion(String alias, Table<DimensionQuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DimensionQuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_DIMENSION_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DimensionQuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<DimensionQuestionRecord>>asList(Keys.KEY_T_DIMENSION_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DimensionQuestion as(String alias) {
        return new DimensionQuestion(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DimensionQuestion rename(String name) {
        return new DimensionQuestion(name, null);
    }
}
