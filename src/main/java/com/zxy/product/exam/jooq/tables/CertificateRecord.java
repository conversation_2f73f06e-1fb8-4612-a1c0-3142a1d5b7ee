/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CertificateRecordRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 证书发放记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CertificateRecord extends TableImpl<CertificateRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_certificate_record</code>
     */
    public static final CertificateRecord CERTIFICATE_RECORD = new CertificateRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CertificateRecordRecord> getRecordType() {
        return CertificateRecordRecord.class;
    }

    /**
     * The column <code>exam.t_certificate_record.f_id</code>. ID
     */
    public final TableField<CertificateRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_certificate_record.f_exam_id</code>. 考试id
     */
    public final TableField<CertificateRecordRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_certificate_record.f_member_id</code>. 用户id
     */
    public final TableField<CertificateRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_certificate_record.f_num</code>. 证书编号
     */
    public final TableField<CertificateRecordRecord, String> NUM = createField("f_num", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书编号");

    /**
     * The column <code>exam.t_certificate_record.f_profession_id</code>. 专业
     */
    public final TableField<CertificateRecordRecord, String> PROFESSION_ID = createField("f_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业");

    /**
     * The column <code>exam.t_certificate_record.f_sub_profession_id</code>. 子专业
     */
    public final TableField<CertificateRecordRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业");

    /**
     * The column <code>exam.t_certificate_record.f_equipment_type_id</code>. 设备
     */
    public final TableField<CertificateRecordRecord, String> EQUIPMENT_TYPE_ID = createField("f_equipment_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备");

    /**
     * The column <code>exam.t_certificate_record.f_profession_level_id</code>. 专业等级id
     */
    public final TableField<CertificateRecordRecord, String> PROFESSION_LEVEL_ID = createField("f_profession_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业等级id");

    /**
     * The column <code>exam.t_certificate_record.f_score</code>. 考试分数
     */
    public final TableField<CertificateRecordRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "考试分数");

    /**
     * The column <code>exam.t_certificate_record.f_score_level</code>. 成绩级别，0不及格 1及格 2良好 3优秀
     */
    public final TableField<CertificateRecordRecord, Integer> SCORE_LEVEL = createField("f_score_level", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "成绩级别，0不及格 1及格 2良好 3优秀");

    /**
     * The column <code>exam.t_certificate_record.f_access_type</code>. 获取方式，0自考 1手动发放
     */
    public final TableField<CertificateRecordRecord, Integer> ACCESS_TYPE = createField("f_access_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "获取方式，0自考 1手动发放");

    /**
     * The column <code>exam.t_certificate_record.f_pass_status</code>. 通过状态，0不通过 1通过
     */
    public final TableField<CertificateRecordRecord, Integer> PASS_STATUS = createField("f_pass_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "通过状态，0不通过 1通过");

    /**
     * The column <code>exam.t_certificate_record.f_issue_time</code>. 签发日期
     */
    public final TableField<CertificateRecordRecord, Long> ISSUE_TIME = createField("f_issue_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "签发日期");

    /**
     * The column <code>exam.t_certificate_record.f_valid_date</code>. 证书有效期
     */
    public final TableField<CertificateRecordRecord, Long> VALID_DATE = createField("f_valid_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "证书有效期");

    /**
     * The column <code>exam.t_certificate_record.f_reason</code>. 发放原因
     */
    public final TableField<CertificateRecordRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发放原因");

    /**
     * The column <code>exam.t_certificate_record.f_create_time</code>. 创建时间
     */
    public final TableField<CertificateRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_certificate_record.f_is_current</code>. 是否最新的证书，0否 1是
     */
    public final TableField<CertificateRecordRecord, Integer> IS_CURRENT = createField("f_is_current", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否最新的证书，0否 1是");

    /**
     * The column <code>exam.t_certificate_record.f_template_id</code>. 证书模板
     */
    public final TableField<CertificateRecordRecord, String> TEMPLATE_ID = createField("f_template_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书模板");

    /**
     * The column <code>exam.t_certificate_record.f_history_flag</code>. 是否历史导入的数据，是1，否0
     */
    public final TableField<CertificateRecordRecord, Integer> HISTORY_FLAG = createField("f_history_flag", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否历史导入的数据，是1，否0");

    /**
     * The column <code>exam.t_certificate_record.f_history_id</code>. 历史数据id
     */
    public final TableField<CertificateRecordRecord, String> HISTORY_ID = createField("f_history_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "历史数据id");

    /**
     * The column <code>exam.t_certificate_record.f_name</code>. 证书名称
     */
    public final TableField<CertificateRecordRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(2000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书名称");

    /**
     * The column <code>exam.t_certificate_record.f_pass_time</code>. 考试通过时间
     */
    public final TableField<CertificateRecordRecord, Long> PASS_TIME = createField("f_pass_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "考试通过时间");

    /**
     * The column <code>exam.t_certificate_record.f_cloud</code>. 是否为云改考试证书，0：否，1：是
     */
    public final TableField<CertificateRecordRecord, Integer> CLOUD = createField("f_cloud", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为云改考试证书，0：否，1：是");

    /**
     * The column <code>exam.t_certificate_record.f_grid</code>. 是否为网格考试证书，0：否，1：是
     */
    public final TableField<CertificateRecordRecord, Integer> GRID = createField("f_grid", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为网格考试证书，0：否，1：是");

    /**
     * The column <code>exam.t_certificate_record.f_modify_date</code>. 修改时间
     */
    public final TableField<CertificateRecordRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_certificate_record</code> table reference
     */
    public CertificateRecord() {
        this("t_certificate_record", null);
    }

    /**
     * Create an aliased <code>exam.t_certificate_record</code> table reference
     */
    public CertificateRecord(String alias) {
        this(alias, CERTIFICATE_RECORD);
    }

    private CertificateRecord(String alias, Table<CertificateRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private CertificateRecord(String alias, Table<CertificateRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "证书发放记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CertificateRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_CERTIFICATE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CertificateRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<CertificateRecordRecord>>asList(Keys.KEY_T_CERTIFICATE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CertificateRecord as(String alias) {
        return new CertificateRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CertificateRecord rename(String name) {
        return new CertificateRecord(name, null);
    }
}
