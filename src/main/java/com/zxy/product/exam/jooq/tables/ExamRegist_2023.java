/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamRegist_2023Record;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 考试注册表_2023
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamRegist_2023 extends TableImpl<ExamRegist_2023Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_exam_regist_2023</code>
     */
    public static final ExamRegist_2023 EXAM_REGIST_2023 = new ExamRegist_2023();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamRegist_2023Record> getRecordType() {
        return ExamRegist_2023Record.class;
    }

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_id</code>.
     */
    public final TableField<ExamRegist_2023Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_create_time</code>.
     */
    public final TableField<ExamRegist_2023Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_exam_id</code>.
     */
    public final TableField<ExamRegist_2023Record, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_member_id</code>.
     */
    public final TableField<ExamRegist_2023Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_status</code>. 状态
     */
    public final TableField<ExamRegist_2023Record, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_top_score</code>. 最高分数
     */
    public final TableField<ExamRegist_2023Record, Integer> TOP_SCORE = createField("f_top_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "最高分数");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_type</code>. 注册类型
     */
    public final TableField<ExamRegist_2023Record, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "注册类型");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_exam_times</code>. 考试次数
     */
    public final TableField<ExamRegist_2023Record, Integer> EXAM_TIMES = createField("f_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试次数");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_top_score_record_id</code>. 最高分的exam_record_id
     */
    public final TableField<ExamRegist_2023Record, String> TOP_SCORE_RECORD_ID = createField("f_top_score_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "最高分的exam_record_id");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_certificate_issue</code>. 是否已发放证书：0否 1是
     */
    public final TableField<ExamRegist_2023Record, Integer> CERTIFICATE_ISSUE = createField("f_certificate_issue", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否已发放证书：0否 1是");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_ticket</code>. 准考证
     */
    public final TableField<ExamRegist_2023Record, String> TICKET = createField("f_ticket", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "准考证");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_pass_status</code>. 及格状态，1：不及格，0：及格，2：已完成
     */
    public final TableField<ExamRegist_2023Record, Integer> PASS_STATUS = createField("f_pass_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "及格状态，1：不及格，0：及格，2：已完成");

    /**
     * The column <code>exam-stu.t_exam_regist_2023.f_modify_date</code>. 修改时间
     */
    public final TableField<ExamRegist_2023Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam-stu.t_exam_regist_2023</code> table reference
     */
    public ExamRegist_2023() {
        this("t_exam_regist_2023", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_exam_regist_2023</code> table reference
     */
    public ExamRegist_2023(String alias) {
        this(alias, EXAM_REGIST_2023);
    }

    private ExamRegist_2023(String alias, Table<ExamRegist_2023Record> aliased) {
        this(alias, aliased, null);
    }

    private ExamRegist_2023(String alias, Table<ExamRegist_2023Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试注册表_2023");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamRegist_2023Record> getPrimaryKey() {
        return Keys.KEY_T_EXAM_REGIST_2023_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamRegist_2023Record>> getKeys() {
        return Arrays.<UniqueKey<ExamRegist_2023Record>>asList(Keys.KEY_T_EXAM_REGIST_2023_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamRegist_2023 as(String alias) {
        return new ExamRegist_2023(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamRegist_2023 rename(String name) {
        return new ExamRegist_2023(name, null);
    }
}
