/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CloudSignupRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 移动云考试报名表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CloudSignup extends TableImpl<CloudSignupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_cloud_signup</code>
     */
    public static final CloudSignup CLOUD_SIGNUP = new CloudSignup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CloudSignupRecord> getRecordType() {
        return CloudSignupRecord.class;
    }

    /**
     * The column <code>exam-stu.t_cloud_signup.f_id</code>.
     */
    public final TableField<CloudSignupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_create_time</code>.
     */
    public final TableField<CloudSignupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_exam_id</code>.
     */
    public final TableField<CloudSignupRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_member_id</code>.
     */
    public final TableField<CloudSignupRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_organization_id</code>.
     */
    public final TableField<CloudSignupRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_position</code>. 职位
     */
    public final TableField<CloudSignupRecord, String> POSITION = createField("f_position", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "职位");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_work_time</code>. 工作年限
     */
    public final TableField<CloudSignupRecord, String> WORK_TIME = createField("f_work_time", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "工作年限");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_status</code>. 审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名
     */
    public final TableField<CloudSignupRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名");

    /**
     * The column <code>exam-stu.t_cloud_signup.f_audit_member_id</code>. 报名审核人
     */
    public final TableField<CloudSignupRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "报名审核人");

    /**
     * Create a <code>exam-stu.t_cloud_signup</code> table reference
     */
    public CloudSignup() {
        this("t_cloud_signup", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_cloud_signup</code> table reference
     */
    public CloudSignup(String alias) {
        this(alias, CLOUD_SIGNUP);
    }

    private CloudSignup(String alias, Table<CloudSignupRecord> aliased) {
        this(alias, aliased, null);
    }

    private CloudSignup(String alias, Table<CloudSignupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "移动云考试报名表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CloudSignupRecord> getPrimaryKey() {
        return Keys.KEY_T_CLOUD_SIGNUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CloudSignupRecord>> getKeys() {
        return Arrays.<UniqueKey<CloudSignupRecord>>asList(Keys.KEY_T_CLOUD_SIGNUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CloudSignup as(String alias) {
        return new CloudSignup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CloudSignup rename(String name) {
        return new CloudSignup(name, null);
    }
}
