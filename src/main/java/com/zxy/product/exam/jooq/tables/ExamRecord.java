/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 考试记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamRecord extends TableImpl<ExamRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_exam_record</code>
     */
    public static final ExamRecord EXAM_RECORD = new ExamRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamRecordRecord> getRecordType() {
        return ExamRecordRecord.class;
    }

    /**
     * The column <code>exam-stu.t_exam_record.f_id</code>.
     */
    public final TableField<ExamRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_exam_record.f_member_id</code>. 员工id
     */
    public final TableField<ExamRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工id");

    /**
     * The column <code>exam-stu.t_exam_record.f_organization_id</code>. 所属组织id
     */
    public final TableField<ExamRecordRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属组织id");

    /**
     * The column <code>exam-stu.t_exam_record.f_start_time</code>. 进入考试时间
     */
    public final TableField<ExamRecordRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "进入考试时间");

    /**
     * The column <code>exam-stu.t_exam_record.f_end_time</code>. 交卷截止时间
     */
    public final TableField<ExamRecordRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "交卷截止时间");

    /**
     * The column <code>exam-stu.t_exam_record.f_last_submit_time</code>. 最后提交时间
     */
    public final TableField<ExamRecordRecord, Long> LAST_SUBMIT_TIME = createField("f_last_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最后提交时间");

    /**
     * The column <code>exam-stu.t_exam_record.f_score</code>. 成绩
     */
    public final TableField<ExamRecordRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "成绩");

    /**
     * The column <code>exam-stu.t_exam_record.f_client_type</code>. 客户端类型 1:pc,2:app
     */
    public final TableField<ExamRecordRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "客户端类型 1:pc,2:app");

    /**
     * The column <code>exam-stu.t_exam_record.f_status</code>. 状态  1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成
     */
    public final TableField<ExamRecordRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态  1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成");

    /**
     * The column <code>exam-stu.t_exam_record.f_exam_id</code>. 关联考试id
     */
    public final TableField<ExamRecordRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "关联考试id");

    /**
     * The column <code>exam-stu.t_exam_record.f_paper_instance_id</code>. 试卷实例id
     */
    public final TableField<ExamRecordRecord, String> PAPER_INSTANCE_ID = createField("f_paper_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试卷实例id");

    /**
     * The column <code>exam-stu.t_exam_record.f_exam_number</code>. 第几次考试
     */
    public final TableField<ExamRecordRecord, Integer> EXAM_NUMBER = createField("f_exam_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第几次考试");

    /**
     * The column <code>exam-stu.t_exam_record.f_create_time</code>. 创建时间
     */
    public final TableField<ExamRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_exam_record.f_submit_time</code>. 交卷时间
     */
    public final TableField<ExamRecordRecord, Long> SUBMIT_TIME = createField("f_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "交卷时间");

    /**
     * The column <code>exam-stu.t_exam_record.f_duration</code>. 考试时长(分钟)
     */
    public final TableField<ExamRecordRecord, Long> DURATION = createField("f_duration", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "考试时长(分钟)");

    /**
     * The column <code>exam-stu.t_exam_record.f_is_reset</code>. 是否重置
     */
    public final TableField<ExamRecordRecord, Integer> IS_RESET = createField("f_is_reset", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否重置");

    /**
     * The column <code>exam-stu.t_exam_record.f_is_current</code>. 0:非当前，1：当前
     */
    public final TableField<ExamRecordRecord, Integer> IS_CURRENT = createField("f_is_current", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0:非当前，1：当前");

    /**
     * The column <code>exam-stu.t_exam_record.f_is_finished</code>. 是否完成
     */
    public final TableField<ExamRecordRecord, Integer> IS_FINISHED = createField("f_is_finished", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否完成");

    /**
     * The column <code>exam-stu.t_exam_record.f_exception_order</code>. 异常排序
     */
    public final TableField<ExamRecordRecord, Integer> EXCEPTION_ORDER = createField("f_exception_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "异常排序");

    /**
     * The column <code>exam-stu.t_exam_record.f_order_content</code>.
     */
    public final TableField<ExamRecordRecord, String> ORDER_CONTENT = createField("f_order_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_exam_record.f_exam_times</code>. 考试次数
     */
    public final TableField<ExamRecordRecord, Integer> EXAM_TIMES = createField("f_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试次数");

    /**
     * The column <code>exam-stu.t_exam_record.f_switch_times</code>. 切屏数
     */
    public final TableField<ExamRecordRecord, Integer> SWITCH_TIMES = createField("f_switch_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "切屏数");

    /**
     * The column <code>exam-stu.t_exam_record.f_personal_code</code>. 个人码
     */
    public final TableField<ExamRecordRecord, Integer> PERSONAL_CODE = createField("f_personal_code", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "个人码");

    /**
     * The column <code>exam-stu.t_exam_record.f_user_ip</code>. 用户ip
     */
    public final TableField<ExamRecordRecord, String> USER_IP = createField("f_user_ip", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ip");

    /**
     * The column <code>exam-stu.t_exam_record.f_no_answer_count</code>. 未答数
     */
    public final TableField<ExamRecordRecord, Integer> NO_ANSWER_COUNT = createField("f_no_answer_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "未答数");

    /**
     * The column <code>exam-stu.t_exam_record.f_answered_count</code>. 已答数
     */
    public final TableField<ExamRecordRecord, Integer> ANSWERED_COUNT = createField("f_answered_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "已答数");

    /**
     * The column <code>exam-stu.t_exam_record.f_client_version</code>. 终端型号
     */
    public final TableField<ExamRecordRecord, String> CLIENT_VERSION = createField("f_client_version", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "终端型号");

    /**
     * The column <code>exam-stu.t_exam_record.f_old_score</code>. 分数
     */
    public final TableField<ExamRecordRecord, Integer> OLD_SCORE = createField("f_old_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分数");

    /**
     * The column <code>exam-stu.t_exam_record.f_old_status</code>. 状态
     */
    public final TableField<ExamRecordRecord, Integer> OLD_STATUS = createField("f_old_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态");

    /**
     * The column <code>exam-stu.t_exam_record.f_right_count</code>. 正确题数
     */
    public final TableField<ExamRecordRecord, Integer> RIGHT_COUNT = createField("f_right_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "正确题数");

    /**
     * The column <code>exam-stu.t_exam_record.f_face_status</code>. 0 正常、1 人脸异常、2 标记正常、3标记异常
     */
    public final TableField<ExamRecordRecord, Integer> FACE_STATUS = createField("f_face_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0 正常、1 人脸异常、2 标记正常、3标记异常");

    /**
     * Create a <code>exam-stu.t_exam_record</code> table reference
     */
    public ExamRecord() {
        this("t_exam_record", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_exam_record</code> table reference
     */
    public ExamRecord(String alias) {
        this(alias, EXAM_RECORD);
    }

    private ExamRecord(String alias, Table<ExamRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamRecord(String alias, Table<ExamRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamRecordRecord>>asList(Keys.KEY_T_EXAM_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamRecord as(String alias) {
        return new ExamRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamRecord rename(String name) {
        return new ExamRecord(name, null);
    }
}
