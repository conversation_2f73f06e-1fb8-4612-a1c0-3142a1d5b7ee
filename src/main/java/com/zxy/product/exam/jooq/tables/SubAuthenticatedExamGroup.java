/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SubAuthenticatedExamGroupRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 子认证-考试组-考试内容关联关系表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedExamGroup extends TableImpl<SubAuthenticatedExamGroupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_sub_authenticated_exam_group</code>
     */
    public static final SubAuthenticatedExamGroup SUB_AUTHENTICATED_EXAM_GROUP = new SubAuthenticatedExamGroup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubAuthenticatedExamGroupRecord> getRecordType() {
        return SubAuthenticatedExamGroupRecord.class;
    }

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_id</code>. 关系表id
     */
    public final TableField<SubAuthenticatedExamGroupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "关系表id");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_create_time</code>. 创建时间
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_sub_authenticated_id</code>. 子认证id
     */
    public final TableField<SubAuthenticatedExamGroupRecord, String> SUB_AUTHENTICATED_ID = createField("f_sub_authenticated_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子认证id");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_exam_group_id</code>. 考试组id（学习t_sub_authenticated_content_configure表对应的f_content_id）
     */
    public final TableField<SubAuthenticatedExamGroupRecord, String> EXAM_GROUP_ID = createField("f_exam_group_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试组id（学习t_sub_authenticated_content_configure表对应的f_content_id）");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_business_id</code>. 考试id
     */
    public final TableField<SubAuthenticatedExamGroupRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_business_name</code>. 考试name
     */
    public final TableField<SubAuthenticatedExamGroupRecord, String> BUSINESS_NAME = createField("f_business_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试name");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_business_type</code>. 1:模拟考试 2：认证考试
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:模拟考试 2：认证考试");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_order</code>. 排序序号
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Integer> ORDER = createField("f_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序序号");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_exam_times</code>. 考试组允许考试次数
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Integer> EXAM_TIMES = createField("f_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试组允许考试次数");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_is_publish</code>. 子认证是否发布，1：已发布；0：未发布
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Integer> IS_PUBLISH = createField("f_is_publish", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "子认证是否发布，1：已发布；0：未发布");

    /**
     * The column <code>exam.t_sub_authenticated_exam_group.f_exam_registration_frequency</code>. 考试组报名次数
     */
    public final TableField<SubAuthenticatedExamGroupRecord, Integer> EXAM_REGISTRATION_FREQUENCY = createField("f_exam_registration_frequency", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试组报名次数");

    /**
     * Create a <code>exam.t_sub_authenticated_exam_group</code> table reference
     */
    public SubAuthenticatedExamGroup() {
        this("t_sub_authenticated_exam_group", null);
    }

    /**
     * Create an aliased <code>exam.t_sub_authenticated_exam_group</code> table reference
     */
    public SubAuthenticatedExamGroup(String alias) {
        this(alias, SUB_AUTHENTICATED_EXAM_GROUP);
    }

    private SubAuthenticatedExamGroup(String alias, Table<SubAuthenticatedExamGroupRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubAuthenticatedExamGroup(String alias, Table<SubAuthenticatedExamGroupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "子认证-考试组-考试内容关联关系表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubAuthenticatedExamGroupRecord> getPrimaryKey() {
        return Keys.KEY_T_SUB_AUTHENTICATED_EXAM_GROUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubAuthenticatedExamGroupRecord>> getKeys() {
        return Arrays.<UniqueKey<SubAuthenticatedExamGroupRecord>>asList(Keys.KEY_T_SUB_AUTHENTICATED_EXAM_GROUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedExamGroup as(String alias) {
        return new SubAuthenticatedExamGroup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubAuthenticatedExamGroup rename(String name) {
        return new SubAuthenticatedExamGroup(name, null);
    }
}
