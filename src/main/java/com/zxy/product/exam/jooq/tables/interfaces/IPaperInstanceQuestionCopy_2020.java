/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 试卷实例与试题副本关联_2020
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IPaperInstanceQuestionCopy_2020 extends Serializable {

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_paper_instance_id</code>. 所属试卷实例
     */
    public void setPaperInstanceId(String value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_paper_instance_id</code>. 所属试卷实例
     */
    public String getPaperInstanceId();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_question_copy_id</code>. 所属试题副本
     */
    public void setQuestionCopyId(String value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_question_copy_id</code>. 所属试题副本
     */
    public String getQuestionCopyId();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_score</code>.
     */
    public void setScore(Integer value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_score</code>.
     */
    public Integer getScore();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_sequence</code>.
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_sequence</code>.
     */
    public Integer getSequence();

    /**
     * Setter for <code>exam.t_paper_instance_question_copy_2020.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>exam.t_paper_instance_question_copy_2020.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IPaperInstanceQuestionCopy_2020
     */
    public void from(com.zxy.product.exam.jooq.tables.interfaces.IPaperInstanceQuestionCopy_2020 from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IPaperInstanceQuestionCopy_2020
     */
    public <E extends com.zxy.product.exam.jooq.tables.interfaces.IPaperInstanceQuestionCopy_2020> E into(E into);
}
