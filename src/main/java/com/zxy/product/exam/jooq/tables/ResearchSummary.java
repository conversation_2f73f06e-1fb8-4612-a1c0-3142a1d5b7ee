/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ResearchSummaryRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchSummary extends TableImpl<ResearchSummaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_research_summary</code>
     */
    public static final ResearchSummary RESEARCH_SUMMARY = new ResearchSummary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchSummaryRecord> getRecordType() {
        return ResearchSummaryRecord.class;
    }

    /**
     * The column <code>exam.t_research_summary.f_id</code>.
     */
    public final TableField<ResearchSummaryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_research_summary.f_create_time</code>.
     */
    public final TableField<ResearchSummaryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_research_summary.f_research_questionary_id</code>. 问卷ID
     */
    public final TableField<ResearchSummaryRecord, String> RESEARCH_QUESTIONARY_ID = createField("f_research_questionary_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问卷ID");

    /**
     * The column <code>exam.t_research_summary.f_summary_count_json</code>. 统计试题各个属性选择的数量
     */
    public final TableField<ResearchSummaryRecord, String> SUMMARY_COUNT_JSON = createField("f_summary_count_json", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "统计试题各个属性选择的数量");

    /**
     * Create a <code>exam.t_research_summary</code> table reference
     */
    public ResearchSummary() {
        this("t_research_summary", null);
    }

    /**
     * Create an aliased <code>exam.t_research_summary</code> table reference
     */
    public ResearchSummary(String alias) {
        this(alias, RESEARCH_SUMMARY);
    }

    private ResearchSummary(String alias, Table<ResearchSummaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchSummary(String alias, Table<ResearchSummaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchSummaryRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_SUMMARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchSummaryRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchSummaryRecord>>asList(Keys.KEY_T_RESEARCH_SUMMARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchSummary as(String alias) {
        return new ResearchSummary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchSummary rename(String name) {
        return new ResearchSummary(name, null);
    }
}
