/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ProfessionLevelRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 等级表，不再与子专业关联
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProfessionLevel extends TableImpl<ProfessionLevelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_profession_level</code>
     */
    public static final ProfessionLevel PROFESSION_LEVEL = new ProfessionLevel();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ProfessionLevelRecord> getRecordType() {
        return ProfessionLevelRecord.class;
    }

    /**
     * The column <code>exam.t_profession_level.f_id</code>. 主键
     */
    public final TableField<ProfessionLevelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_profession_level.f_create_time</code>.
     */
    public final TableField<ProfessionLevelRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_profession_level.f_sub_profession_id</code>. 子专业ID,现无用
     */
    public final TableField<ProfessionLevelRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业ID,现无用");

    /**
     * The column <code>exam.t_profession_level.f_level_code</code>. 等级编码
     */
    public final TableField<ProfessionLevelRecord, String> LEVEL_CODE = createField("f_level_code", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级编码");

    /**
     * The column <code>exam.t_profession_level.f_level_name</code>. 等级名称
     */
    public final TableField<ProfessionLevelRecord, String> LEVEL_NAME = createField("f_level_name", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "等级名称");

    /**
     * The column <code>exam.t_profession_level.f_level</code>. 等级 1,2,3,4,5
     */
    public final TableField<ProfessionLevelRecord, Integer> LEVEL = createField("f_level", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("1", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "等级 1,2,3,4,5");

    /**
     * The column <code>exam.t_profession_level.f_valid_date</code>. 有效期（年）， 0代表长期有效
     */
    public final TableField<ProfessionLevelRecord, Integer> VALID_DATE = createField("f_valid_date", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "有效期（年）， 0代表长期有效");

    /**
     * Create a <code>exam.t_profession_level</code> table reference
     */
    public ProfessionLevel() {
        this("t_profession_level", null);
    }

    /**
     * Create an aliased <code>exam.t_profession_level</code> table reference
     */
    public ProfessionLevel(String alias) {
        this(alias, PROFESSION_LEVEL);
    }

    private ProfessionLevel(String alias, Table<ProfessionLevelRecord> aliased) {
        this(alias, aliased, null);
    }

    private ProfessionLevel(String alias, Table<ProfessionLevelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "等级表，不再与子专业关联");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ProfessionLevelRecord> getPrimaryKey() {
        return Keys.KEY_T_PROFESSION_LEVEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ProfessionLevelRecord>> getKeys() {
        return Arrays.<UniqueKey<ProfessionLevelRecord>>asList(Keys.KEY_T_PROFESSION_LEVEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ProfessionLevel as(String alias) {
        return new ProfessionLevel(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ProfessionLevel rename(String name) {
        return new ProfessionLevel(name, null);
    }
}
