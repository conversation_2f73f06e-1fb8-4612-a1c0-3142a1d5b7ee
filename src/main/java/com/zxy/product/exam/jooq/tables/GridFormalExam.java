/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridFormalExamRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 网格长各级别对应的正式考试
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridFormalExam extends TableImpl<GridFormalExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_formal_exam</code>
     */
    public static final GridFormalExam GRID_FORMAL_EXAM = new GridFormalExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridFormalExamRecord> getRecordType() {
        return GridFormalExamRecord.class;
    }

    /**
     * The column <code>exam.t_grid_formal_exam.f_id</code>. 主键
     */
    public final TableField<GridFormalExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_grid_formal_exam.f_create_time</code>. 创建时间
     */
    public final TableField<GridFormalExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_formal_exam.f_grid_course_exam_id</code>. 网格长各级别对应的id
     */
    public final TableField<GridFormalExamRecord, String> GRID_COURSE_EXAM_ID = createField("f_grid_course_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "网格长各级别对应的id");

    /**
     * The column <code>exam.t_grid_formal_exam.f_formal_exam_id</code>. 正式考试id
     */
    public final TableField<GridFormalExamRecord, String> FORMAL_EXAM_ID = createField("f_formal_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "正式考试id");

    /**
     * The column <code>exam.t_grid_formal_exam.f_supplement</code>. 是否为补充考试，0否；1是
     */
    public final TableField<GridFormalExamRecord, Integer> SUPPLEMENT = createField("f_supplement", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为补充考试，0否；1是");

    /**
     * Create a <code>exam.t_grid_formal_exam</code> table reference
     */
    public GridFormalExam() {
        this("t_grid_formal_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_formal_exam</code> table reference
     */
    public GridFormalExam(String alias) {
        this(alias, GRID_FORMAL_EXAM);
    }

    private GridFormalExam(String alias, Table<GridFormalExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridFormalExam(String alias, Table<GridFormalExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长各级别对应的正式考试");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridFormalExamRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_FORMAL_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridFormalExamRecord>> getKeys() {
        return Arrays.<UniqueKey<GridFormalExamRecord>>asList(Keys.KEY_T_GRID_FORMAL_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridFormalExam as(String alias) {
        return new GridFormalExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridFormalExam rename(String name) {
        return new GridFormalExam(name, null);
    }
}
