/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamRegistRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamRegist extends TableImpl<ExamRegistRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_exam_regist</code>
     */
    public static final ExamRegist EXAM_REGIST = new ExamRegist();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamRegistRecord> getRecordType() {
        return ExamRegistRecord.class;
    }

    /**
     * The column <code>exam-stu.t_exam_regist.f_id</code>.
     */
    public final TableField<ExamRegistRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist.f_create_time</code>.
     */
    public final TableField<ExamRegistRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist.f_exam_id</code>.
     */
    public final TableField<ExamRegistRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist.f_member_id</code>.
     */
    public final TableField<ExamRegistRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_exam_regist.f_status</code>. 状态
     */
    public final TableField<ExamRegistRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态");

    /**
     * The column <code>exam-stu.t_exam_regist.f_top_score</code>. 最高分数
     */
    public final TableField<ExamRegistRecord, Integer> TOP_SCORE = createField("f_top_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "最高分数");

    /**
     * The column <code>exam-stu.t_exam_regist.f_type</code>. 注册类型
     */
    public final TableField<ExamRegistRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "注册类型");

    /**
     * The column <code>exam-stu.t_exam_regist.f_exam_times</code>. 考试次数
     */
    public final TableField<ExamRegistRecord, Integer> EXAM_TIMES = createField("f_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试次数");

    /**
     * The column <code>exam-stu.t_exam_regist.f_top_score_record_id</code>. 最高分的exam_record_id
     */
    public final TableField<ExamRegistRecord, String> TOP_SCORE_RECORD_ID = createField("f_top_score_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "最高分的exam_record_id");

    /**
     * The column <code>exam-stu.t_exam_regist.f_certificate_issue</code>. 是否已发放证书：0否 1是
     */
    public final TableField<ExamRegistRecord, Integer> CERTIFICATE_ISSUE = createField("f_certificate_issue", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否已发放证书：0否 1是");

    /**
     * The column <code>exam-stu.t_exam_regist.f_ticket</code>. 准考证
     */
    public final TableField<ExamRegistRecord, String> TICKET = createField("f_ticket", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "准考证");

    /**
     * The column <code>exam-stu.t_exam_regist.f_pass_status</code>. 及格状态，1：不及格，0：及格，2：已完成
     */
    public final TableField<ExamRegistRecord, Integer> PASS_STATUS = createField("f_pass_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "及格状态，1：不及格，0：及格，2：已完成");

    /**
     * The column <code>exam-stu.t_exam_regist.f_old_pass_status</code>. 通过状态
     */
    public final TableField<ExamRegistRecord, Integer> OLD_PASS_STATUS = createField("f_old_pass_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "通过状态");

    /**
     * The column <code>exam-stu.t_exam_regist.f_old_top_score</code>. 最高分
     */
    public final TableField<ExamRegistRecord, Integer> OLD_TOP_SCORE = createField("f_old_top_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "最高分");

    /**
     * Create a <code>exam-stu.t_exam_regist</code> table reference
     */
    public ExamRegist() {
        this("t_exam_regist", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_exam_regist</code> table reference
     */
    public ExamRegist(String alias) {
        this(alias, EXAM_REGIST);
    }

    private ExamRegist(String alias, Table<ExamRegistRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamRegist(String alias, Table<ExamRegistRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamRegistRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_REGIST_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamRegistRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamRegistRecord>>asList(Keys.KEY_T_EXAM_REGIST_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamRegist as(String alias) {
        return new ExamRegist(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamRegist rename(String name) {
        return new ExamRegist(name, null);
    }
}
