/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridCourseRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 网格长认证考试关联的学习
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridCourse extends TableImpl<GridCourseRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_course</code>
     */
    public static final GridCourse GRID_COURSE = new GridCourse();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridCourseRecord> getRecordType() {
        return GridCourseRecord.class;
    }

    /**
     * The column <code>exam.t_grid_course.f_id</code>. 主键
     */
    public final TableField<GridCourseRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_grid_course.f_create_time</code>. 创建时间
     */
    public final TableField<GridCourseRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_course.f_exam_id</code>. 考试id
     */
    public final TableField<GridCourseRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "考试id");

    /**
     * The column <code>exam.t_grid_course.f_course_id</code>. 学习id
     */
    public final TableField<GridCourseRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "学习id");

    /**
     * Create a <code>exam.t_grid_course</code> table reference
     */
    public GridCourse() {
        this("t_grid_course", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_course</code> table reference
     */
    public GridCourse(String alias) {
        this(alias, GRID_COURSE);
    }

    private GridCourse(String alias, Table<GridCourseRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridCourse(String alias, Table<GridCourseRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长认证考试关联的学习");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridCourseRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_COURSE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridCourseRecord>> getKeys() {
        return Arrays.<UniqueKey<GridCourseRecord>>asList(Keys.KEY_T_GRID_COURSE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridCourse as(String alias) {
        return new GridCourse(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridCourse rename(String name) {
        return new GridCourse(name, null);
    }
}
