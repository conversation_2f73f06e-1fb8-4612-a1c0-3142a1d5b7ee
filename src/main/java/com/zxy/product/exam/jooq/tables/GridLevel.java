/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridLevelRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
/**
 * 网格长认证考试等级表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridLevel extends TableImpl<GridLevelRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_level</code>
     */
    public static final GridLevel GRID_LEVEL = new GridLevel();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridLevelRecord> getRecordType() {
        return GridLevelRecord.class;
    }

    /**
     * The column <code>exam.t_grid_level.f_id</code>. 主键
     */
    public final TableField<GridLevelRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_grid_level.f_create_time</code>. 创建时间
     */
    public final TableField<GridLevelRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_level.f_level_code</code>. 等级编码
     */
    public final TableField<GridLevelRecord, String> LEVEL_CODE = createField("f_level_code", org.jooq.impl.SQLDataType.VARCHAR.length(20).nullable(false), this, "等级编码");

    /**
     * The column <code>exam.t_grid_level.f_level_name</code>. 等级名称
     */
    public final TableField<GridLevelRecord, String> LEVEL_NAME = createField("f_level_name", org.jooq.impl.SQLDataType.VARCHAR.length(20).nullable(false), this, "等级名称");

    /**
     * The column <code>exam.t_grid_level.f_level</code>. 等级 1,2,3,4,5
     */
    public final TableField<GridLevelRecord, Integer> LEVEL = createField("f_level", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "等级 1,2,3,4,5");

    /**
     * The column <code>exam.t_grid_level.f_valid_date</code>. 有效期（年）， 0代表长期有效
     */
    public final TableField<GridLevelRecord, Integer> VALID_DATE = createField("f_valid_date", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "有效期（年）， 0代表长期有效");

    /**
     * Create a <code>exam.t_grid_level</code> table reference
     */
    public GridLevel() {
        this("t_grid_level", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_level</code> table reference
     */
    public GridLevel(String alias) {
        this(alias, GRID_LEVEL);
    }

    private GridLevel(String alias, Table<GridLevelRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridLevel(String alias, Table<GridLevelRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长认证考试等级表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridLevelRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_LEVEL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridLevelRecord>> getKeys() {
        return Arrays.<UniqueKey<GridLevelRecord>>asList(Keys.KEY_T_GRID_LEVEL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridLevel as(String alias) {
        return new GridLevel(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridLevel rename(String name) {
        return new GridLevel(name, null);
    }
}
