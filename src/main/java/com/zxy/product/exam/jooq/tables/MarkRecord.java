/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.MarkRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MarkRecord extends TableImpl<MarkRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_mark_record</code>
     */
    public static final MarkRecord MARK_RECORD = new MarkRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MarkRecordRecord> getRecordType() {
        return MarkRecordRecord.class;
    }

    /**
     * The column <code>exam.t_mark_record.f_id</code>.
     */
    public final TableField<MarkRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_mark_record.f_mark_config_id</code>. 评卷配置id
     */
    public final TableField<MarkRecordRecord, String> MARK_CONFIG_ID = createField("f_mark_config_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评卷配置id");

    /**
     * The column <code>exam.t_mark_record.f_answer_record_id</code>. 答题记录id
     */
    public final TableField<MarkRecordRecord, String> ANSWER_RECORD_ID = createField("f_answer_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "答题记录id");

    /**
     * The column <code>exam.t_mark_record.f_create_time</code>.
     */
    public final TableField<MarkRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_mark_record.f_member_id</code>. 评分人
     */
    public final TableField<MarkRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "评分人");

    /**
     * Create a <code>exam.t_mark_record</code> table reference
     */
    public MarkRecord() {
        this("t_mark_record", null);
    }

    /**
     * Create an aliased <code>exam.t_mark_record</code> table reference
     */
    public MarkRecord(String alias) {
        this(alias, MARK_RECORD);
    }

    private MarkRecord(String alias, Table<MarkRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private MarkRecord(String alias, Table<MarkRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MarkRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_MARK_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MarkRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<MarkRecordRecord>>asList(Keys.KEY_T_MARK_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MarkRecord as(String alias) {
        return new MarkRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MarkRecord rename(String name) {
        return new MarkRecord(name, null);
    }
}
