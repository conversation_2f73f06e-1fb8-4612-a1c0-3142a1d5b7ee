/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.TodayQuestionRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 今日答题表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TodayQuestion extends TableImpl<TodayQuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_today_question</code>
     */
    public static final TodayQuestion TODAY_QUESTION = new TodayQuestion();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<TodayQuestionRecord> getRecordType() {
        return TodayQuestionRecord.class;
    }

    /**
     * The column <code>exam.t_today_question.f_id</code>.
     */
    public final TableField<TodayQuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_today_question.f_create_time</code>.
     */
    public final TableField<TodayQuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_today_question.f_question_id</code>. 试题id
     */
    public final TableField<TodayQuestionRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题id");

    /**
     * Create a <code>exam.t_today_question</code> table reference
     */
    public TodayQuestion() {
        this("t_today_question", null);
    }

    /**
     * Create an aliased <code>exam.t_today_question</code> table reference
     */
    public TodayQuestion(String alias) {
        this(alias, TODAY_QUESTION);
    }

    private TodayQuestion(String alias, Table<TodayQuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private TodayQuestion(String alias, Table<TodayQuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "今日答题表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<TodayQuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_TODAY_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<TodayQuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<TodayQuestionRecord>>asList(Keys.KEY_T_TODAY_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TodayQuestion as(String alias) {
        return new TodayQuestion(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public TodayQuestion rename(String name) {
        return new TodayQuestion(name, null);
    }
}
