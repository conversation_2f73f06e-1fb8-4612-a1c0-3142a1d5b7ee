/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ErrorReasonRecord;

import java.util.Arrays;
import java.util.List;


import javax.annotation.Generated;

import com.zxy.product.exam.jooq.tables.records.ErrorReasonRecord;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 试题报错原因表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ErrorReason extends TableImpl<ErrorReasonRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_error_reason</code>
     */
    public static final ErrorReason ERROR_REASON = new ErrorReason();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ErrorReasonRecord> getRecordType() {
        return ErrorReasonRecord.class;
    }

    /**
     * The column <code>exam.t_error_reason.f_id</code>. 主键
     */
    public final TableField<ErrorReasonRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_error_reason.f_error_reason</code>. 错误原因
     */
    public final TableField<ErrorReasonRecord, String> ERROR_REASON_ = createField("f_error_reason", org.jooq.impl.SQLDataType.VARCHAR.length(256).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "错误原因");

    /**
     * The column <code>exam.t_error_reason.f_member_id</code>. 对应人员表id
     */
    public final TableField<ErrorReasonRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "对应人员表id");

    /**
     * The column <code>exam.t_error_reason.f_exam_num</code>. 试题编号
     */
    public final TableField<ErrorReasonRecord, String> EXAM_NUM = createField("f_exam_num", org.jooq.impl.SQLDataType.VARCHAR.length(128).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题编号");

    /**
     * The column <code>exam.t_error_reason.f_create_time</code>. 创建时间
     */
    public final TableField<ErrorReasonRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>exam.t_error_reason</code> table reference
     */
    public ErrorReason() {
        this("t_error_reason", null);
    }

    /**
     * Create an aliased <code>exam.t_error_reason</code> table reference
     */
    public ErrorReason(String alias) {
        this(alias, ERROR_REASON);
    }

    private ErrorReason(String alias, Table<ErrorReasonRecord> aliased) {
        this(alias, aliased, null);
    }

    private ErrorReason(String alias, Table<ErrorReasonRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试题报错原因表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ErrorReasonRecord> getPrimaryKey() {
        return Keys.KEY_T_ERROR_REASON_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ErrorReasonRecord>> getKeys() {
        return Arrays.<UniqueKey<ErrorReasonRecord>>asList(Keys.KEY_T_ERROR_REASON_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ErrorReason as(String alias) {
        return new ErrorReason(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ErrorReason rename(String name) {
        return new ErrorReason(name, null);
    }
}
