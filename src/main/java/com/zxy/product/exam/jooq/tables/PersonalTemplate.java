/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PersonalTemplateRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 个人信息信息模板表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PersonalTemplate extends TableImpl<PersonalTemplateRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_personal_template</code>
     */
    public static final PersonalTemplate PERSONAL_TEMPLATE = new PersonalTemplate();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PersonalTemplateRecord> getRecordType() {
        return PersonalTemplateRecord.class;
    }

    /**
     * The column <code>exam-stu.t_personal_template.f_id</code>.
     */
    public final TableField<PersonalTemplateRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_personal_template.f_create_time</code>. 创建时间
     */
    public final TableField<PersonalTemplateRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_personal_template.f_member_id</code>. 员工id
     */
    public final TableField<PersonalTemplateRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工id");

    /**
     * The column <code>exam-stu.t_personal_template.f_profession_id</code>. 专业
     */
    public final TableField<PersonalTemplateRecord, String> PROFESSION_ID = createField("f_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专业");

    /**
     * The column <code>exam-stu.t_personal_template.f_sub_profession_id</code>. 子专业
     */
    public final TableField<PersonalTemplateRecord, String> SUB_PROFESSION_ID = createField("f_sub_profession_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子专业");

    /**
     * The column <code>exam-stu.t_personal_template.f_equipment_type_id</code>. 设备型号id
     */
    public final TableField<PersonalTemplateRecord, String> EQUIPMENT_TYPE_ID = createField("f_equipment_type_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "设备型号id");

    /**
     * The column <code>exam-stu.t_personal_template.f_work_depart</code>. 工作部们
     */
    public final TableField<PersonalTemplateRecord, String> WORK_DEPART = createField("f_work_depart", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "工作部们");

    /**
     * The column <code>exam-stu.t_personal_template.f_work_time</code>. 工作年限
     */
    public final TableField<PersonalTemplateRecord, String> WORK_TIME = createField("f_work_time", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "工作年限");

    /**
     * The column <code>exam-stu.t_personal_template.f_is_group_expert</code>. 是否集团专家 0:否，1:是
     */
    public final TableField<PersonalTemplateRecord, Integer> IS_GROUP_EXPERT = createField("f_is_group_expert", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否集团专家 0:否，1:是");

    /**
     * The column <code>exam-stu.t_personal_template.f_is_provin_expert</code>. 是否是省级专家 0:否 1:是
     */
    public final TableField<PersonalTemplateRecord, Integer> IS_PROVIN_EXPERT = createField("f_is_provin_expert", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否是省级专家 0:否 1:是");

    /**
     * The column <code>exam-stu.t_personal_template.f_other_exam_appraisal</code>.
     */
    public final TableField<PersonalTemplateRecord, String> OTHER_EXAM_APPRAISAL = createField("f_other_exam_appraisal", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_personal_template.f_award_situation</code>.
     */
    public final TableField<PersonalTemplateRecord, String> AWARD_SITUATION = createField("f_award_situation", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_personal_template.f_cross_condition</code>.
     */
    public final TableField<PersonalTemplateRecord, String> CROSS_CONDITION = createField("f_cross_condition", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_personal_template.f_apply_level</code>. 本次申报等级
     */
    public final TableField<PersonalTemplateRecord, String> APPLY_LEVEL = createField("f_apply_level", org.jooq.impl.SQLDataType.VARCHAR.length(20).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报等级");

    /**
     * The column <code>exam-stu.t_personal_template.f_apply_profession</code>. 本次申报专业
     */
    public final TableField<PersonalTemplateRecord, String> APPLY_PROFESSION = createField("f_apply_profession", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报专业");

    /**
     * The column <code>exam-stu.t_personal_template.f_apply_sub_profession</code>. 本次申报子专业
     */
    public final TableField<PersonalTemplateRecord, String> APPLY_SUB_PROFESSION = createField("f_apply_sub_profession", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报子专业");

    /**
     * The column <code>exam-stu.t_personal_template.f_apply_supplier</code>. 本次申报厂商
     */
    public final TableField<PersonalTemplateRecord, String> APPLY_SUPPLIER = createField("f_apply_supplier", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "本次申报厂商");

    /**
     * The column <code>exam-stu.t_personal_template.f_province</code>. 所在省
     */
    public final TableField<PersonalTemplateRecord, String> PROVINCE = createField("f_province", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所在省");

    /**
     * The column <code>exam-stu.t_personal_template.f_city</code>. 所在市
     */
    public final TableField<PersonalTemplateRecord, String> CITY = createField("f_city", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所在市");

    /**
     * Create a <code>exam-stu.t_personal_template</code> table reference
     */
    public PersonalTemplate() {
        this("t_personal_template", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_personal_template</code> table reference
     */
    public PersonalTemplate(String alias) {
        this(alias, PERSONAL_TEMPLATE);
    }

    private PersonalTemplate(String alias, Table<PersonalTemplateRecord> aliased) {
        this(alias, aliased, null);
    }

    private PersonalTemplate(String alias, Table<PersonalTemplateRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "个人信息信息模板表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PersonalTemplateRecord> getPrimaryKey() {
        return Keys.KEY_T_PERSONAL_TEMPLATE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PersonalTemplateRecord>> getKeys() {
        return Arrays.<UniqueKey<PersonalTemplateRecord>>asList(Keys.KEY_T_PERSONAL_TEMPLATE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PersonalTemplate as(String alias) {
        return new PersonalTemplate(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PersonalTemplate rename(String name) {
        return new PersonalTemplate(name, null);
    }
}
