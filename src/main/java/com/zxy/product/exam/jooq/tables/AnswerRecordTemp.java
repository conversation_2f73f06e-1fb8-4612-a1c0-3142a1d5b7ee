/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecordTempRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecordTemp extends TableImpl<AnswerRecordTempRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_answer_record_temp</code>
     */
    public static final AnswerRecordTemp ANSWER_RECORD_TEMP = new AnswerRecordTemp();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecordTempRecord> getRecordType() {
        return AnswerRecordTempRecord.class;
    }

    /**
     * The column <code>exam.t_answer_record_temp.f_id</code>.
     */
    public final TableField<AnswerRecordTempRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_answer_record_temp.f_create_time</code>.
     */
    public final TableField<AnswerRecordTempRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_answer_record_temp.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecordTempRecord, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_temp.f_question_id</code>.
     */
    public final TableField<AnswerRecordTempRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_temp.f_answer</code>.
     */
    public final TableField<AnswerRecordTempRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * Create a <code>exam.t_answer_record_temp</code> table reference
     */
    public AnswerRecordTemp() {
        this("t_answer_record_temp", null);
    }

    /**
     * Create an aliased <code>exam.t_answer_record_temp</code> table reference
     */
    public AnswerRecordTemp(String alias) {
        this(alias, ANSWER_RECORD_TEMP);
    }

    private AnswerRecordTemp(String alias, Table<AnswerRecordTempRecord> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecordTemp(String alias, Table<AnswerRecordTempRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecordTempRecord> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_TEMP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecordTempRecord>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecordTempRecord>>asList(Keys.KEY_T_ANSWER_RECORD_TEMP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordTemp as(String alias) {
        return new AnswerRecordTemp(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecordTemp rename(String name) {
        return new AnswerRecordTemp(name, null);
    }
}
