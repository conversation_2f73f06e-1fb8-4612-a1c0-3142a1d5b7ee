/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecordRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 答案记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecord extends TableImpl<AnswerRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_answer_record</code>
     */
    public static final AnswerRecord ANSWER_RECORD = new AnswerRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecordRecord> getRecordType() {
        return AnswerRecordRecord.class;
    }

    /**
     * The column <code>exam-stu.t_answer_record.f_id</code>.
     */
    public final TableField<AnswerRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_create_time</code>.
     */
    public final TableField<AnswerRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecordRecord, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_question_id</code>.
     */
    public final TableField<AnswerRecordRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_answer</code>.
     */
    public final TableField<AnswerRecordRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_is_right</code>.
     */
    public final TableField<AnswerRecordRecord, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_score</code>.
     */
    public final TableField<AnswerRecordRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record.f_old_score</code>. 旧分数
     */
    public final TableField<AnswerRecordRecord, Integer> OLD_SCORE = createField("f_old_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "旧分数");

    /**
     * The column <code>exam-stu.t_answer_record.f_old_is_right</code>. 是否正确
     */
    public final TableField<AnswerRecordRecord, Integer> OLD_IS_RIGHT = createField("f_old_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否正确");

    /**
     * Create a <code>exam-stu.t_answer_record</code> table reference
     */
    public AnswerRecord() {
        this("t_answer_record", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_answer_record</code> table reference
     */
    public AnswerRecord(String alias) {
        this(alias, ANSWER_RECORD);
    }

    private AnswerRecord(String alias, Table<AnswerRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecord(String alias, Table<AnswerRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "答案记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecordRecord>>asList(Keys.KEY_T_ANSWER_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecord as(String alias) {
        return new AnswerRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecord rename(String name) {
        return new AnswerRecord(name, null);
    }
}
