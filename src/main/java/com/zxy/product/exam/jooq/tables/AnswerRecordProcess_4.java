/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecordProcess_4Record;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 问题记录流水表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecordProcess_4 extends TableImpl<AnswerRecordProcess_4Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_answer_record_process_4</code>
     */
    public static final AnswerRecordProcess_4 ANSWER_RECORD_PROCESS_4 = new AnswerRecordProcess_4();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecordProcess_4Record> getRecordType() {
        return AnswerRecordProcess_4Record.class;
    }

    /**
     * The column <code>exam-stu.t_answer_record_process_4.id</code>.
     */
    public final TableField<AnswerRecordProcess_4Record, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_4.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecordProcess_4Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_4.f_question_id</code>.
     */
    public final TableField<AnswerRecordProcess_4Record, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_4.f_answer</code>.
     */
    public final TableField<AnswerRecordProcess_4Record, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_answer_record_process_4.f_create_time</code>.
     */
    public final TableField<AnswerRecordProcess_4Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * Create a <code>exam-stu.t_answer_record_process_4</code> table reference
     */
    public AnswerRecordProcess_4() {
        this("t_answer_record_process_4", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_answer_record_process_4</code> table reference
     */
    public AnswerRecordProcess_4(String alias) {
        this(alias, ANSWER_RECORD_PROCESS_4);
    }

    private AnswerRecordProcess_4(String alias, Table<AnswerRecordProcess_4Record> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecordProcess_4(String alias, Table<AnswerRecordProcess_4Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "问题记录流水表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Identity<AnswerRecordProcess_4Record, Long> getIdentity() {
        return Keys.IDENTITY_ANSWER_RECORD_PROCESS_4;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecordProcess_4Record> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecordProcess_4Record>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecordProcess_4Record>>asList(Keys.KEY_T_ANSWER_RECORD_PROCESS_4_PRIMARY, Keys.KEY_T_ANSWER_RECORD_PROCESS_4_UNIQ_QUESTION_ID_EXAM_RECORD_ID);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecordProcess_4 as(String alias) {
        return new AnswerRecordProcess_4(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecordProcess_4 rename(String name) {
        return new AnswerRecordProcess_4(name, null);
    }
}
