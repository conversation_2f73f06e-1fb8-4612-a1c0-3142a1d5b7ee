/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq;


import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamStu extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    //todo exam-stu schema下生成的该类中信息均在com.zxy.product.examstu.jooq.Exam中维护

    /**
     * The reference instance of <code>exam-process</code>
     */
    public static final ExamStu EXAM_STU_SCHEMA = new ExamStu();

    /**
     * No further instances allowed
     */
    private ExamStu() {
        super("exam-stu", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
        );
    }
}
