/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.DigitalIntelligenceResultRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;


/**
 * 数智测评结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceResult extends TableImpl<DigitalIntelligenceResultRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_digital_intelligence_result</code>
     */
    public static final DigitalIntelligenceResult DIGITAL_INTELLIGENCE_RESULT = new DigitalIntelligenceResult();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DigitalIntelligenceResultRecord> getRecordType() {
        return DigitalIntelligenceResultRecord.class;
    }

    /**
     * The column <code>exam.t_digital_intelligence_result.f_id</code>. 记录ID
     */
     public final TableField<DigitalIntelligenceResultRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR(40).nullable(false), this, "记录ID");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_name</code>. 用户名称
     */
    public final TableField<DigitalIntelligenceResultRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR(50).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户名称");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_member_id</code>. 用户id
     */
    public final TableField<DigitalIntelligenceResultRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR(40).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_ability_tags</code>. 能力标签，多个中间用 | 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> ABILITY_TAGS = createField("f_ability_tags", org.jooq.impl.SQLDataType.VARCHAR(128).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "能力标签，多个中间用 | 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_strength_enhancement</code>. 优势强化，多个中间用 | 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> STRENGTH_ENHANCEMENT = createField("f_strength_enhancement", org.jooq.impl.SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "优势强化，多个中间用 | 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_skill_improvement</code>. 短板提升，多个中间用 | 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> SKILL_IMPROVEMENT = createField("f_skill_improvement", org.jooq.impl.SQLDataType.VARCHAR(256).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "短板提升，多个中间用 | 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_short_term_plan</code>. 推荐发展路径--短期，多个中间用 | 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> SHORT_TERM_PLAN = createField("f_short_term_plan", org.jooq.impl.SQLDataType.VARCHAR(512).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "推荐发展路径--短期，多个中间用 | 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_medium_term_plan</code>. 推荐发展路径--长期，多个中间用 | 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> MEDIUM_TERM_PLAN = createField("f_medium_term_plan", org.jooq.impl.SQLDataType.VARCHAR(512).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "推荐发展路径--长期，多个中间用 | 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_recommended_courses_id</code>. 推荐课程，多个 id 中间用 , 隔开
     */
    public final TableField<DigitalIntelligenceResultRecord, String> RECOMMENDED_COURSES_ID = createField("f_recommended_courses_id", org.jooq.impl.SQLDataType.VARCHAR(512).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "推荐课程，多个 id 中间用 , 隔开");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_is_view</code>. 是否查看过结果:0 没有；1 已经查看
     */
    public final TableField<DigitalIntelligenceResultRecord, Integer> IS_VIEW = createField("f_is_view", org.jooq.impl.SQLDataType.INTEGER.defaultValue(DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否查看过结果:0 没有；1 已经查看");

    /**
     * The column <code>exam.t_digital_intelligence_result.f_create_time</code>. 创建时间
     */
    public final TableField<DigitalIntelligenceResultRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>exam.t_digital_intelligence_result</code> table reference
     */
    public DigitalIntelligenceResult() {
        this("t_digital_intelligence_result", null);
    }

    /**
     * Create an aliased <code>exam.t_digital_intelligence_result</code> table reference
     */
    public DigitalIntelligenceResult(String alias) {
        this(alias, DIGITAL_INTELLIGENCE_RESULT);
    }

    private DigitalIntelligenceResult(String alias, Table<DigitalIntelligenceResultRecord> aliased) {
        this(alias, aliased, null);
    }

    private DigitalIntelligenceResult(String alias, Table<DigitalIntelligenceResultRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "数智测评结果表");
    }

    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    @Override
    public UniqueKey<DigitalIntelligenceResultRecord> getPrimaryKey() {
        return Keys.KEY_T_DIGITAL_INTELLIGENCE_RESULT_PRIMARY;
    }

    @Override
    public List<UniqueKey<DigitalIntelligenceResultRecord>> getKeys() {
        return Arrays.<UniqueKey<DigitalIntelligenceResultRecord>>asList(Keys.KEY_T_DIGITAL_INTELLIGENCE_RESULT_PRIMARY);
    }

    @Override
    public DigitalIntelligenceResult as(String alias) {
        return new DigitalIntelligenceResult(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DigitalIntelligenceResult rename(String name) {
        return new DigitalIntelligenceResult(name, null);
    }
}
