/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridFileRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 网格长材料管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridFile extends TableImpl<GridFileRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_file</code>
     */
    public static final GridFile GRID_FILE = new GridFile();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridFileRecord> getRecordType() {
        return GridFileRecord.class;
    }

    /**
     * The column <code>exam.t_grid_file.f_id</code>.
     */
    public final TableField<GridFileRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_grid_file.f_create_time</code>. 创建时间
     */
    public final TableField<GridFileRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_file.f_member_id</code>. 学员id
     */
    public final TableField<GridFileRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "学员id");

    /**
     * The column <code>exam.t_grid_file.f_file_id</code>. 文件id
     */
    public final TableField<GridFileRecord, String> FILE_ID = createField("f_file_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "文件id");

    /**
     * The column <code>exam.t_grid_file.f_file_path</code>. 文件path
     */
    public final TableField<GridFileRecord, String> FILE_PATH = createField("f_file_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).nullable(false), this, "文件path");

    /**
     * The column <code>exam.t_grid_file.f_file_name</code>. 文件名称
     */
    public final TableField<GridFileRecord, String> FILE_NAME = createField("f_file_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "文件名称");

    /**
     * The column <code>exam.t_grid_file.f_type</code>. 资料类型：1:资料审核，2:举证材料
     */
    public final TableField<GridFileRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "资料类型：1:资料审核，2:举证材料");

    /**
     * The column <code>exam.t_grid_file.f_level_id</code>. 认证级别id
     */
    public final TableField<GridFileRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "认证级别id");

    /**
     * Create a <code>exam.t_grid_file</code> table reference
     */
    public GridFile() {
        this("t_grid_file", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_file</code> table reference
     */
    public GridFile(String alias) {
        this(alias, GRID_FILE);
    }

    private GridFile(String alias, Table<GridFileRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridFile(String alias, Table<GridFileRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长材料管理");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridFileRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_FILE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridFileRecord>> getKeys() {
        return Arrays.<UniqueKey<GridFileRecord>>asList(Keys.KEY_T_GRID_FILE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridFile as(String alias) {
        return new GridFile(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridFile rename(String name) {
        return new GridFile(name, null);
    }
}
