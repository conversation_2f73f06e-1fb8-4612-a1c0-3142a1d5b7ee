/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.InvigilatorGrantDetailRecord;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
/**
 * 监考范围详情表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class InvigilatorGrantDetail extends TableImpl<InvigilatorGrantDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_invigilator_grant_detail</code>
     */
    public static final InvigilatorGrantDetail INVIGILATOR_GRANT_DETAIL = new InvigilatorGrantDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InvigilatorGrantDetailRecord> getRecordType() {
        return InvigilatorGrantDetailRecord.class;
    }

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_id</code>. ID
     */
    public final TableField<InvigilatorGrantDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_invigilator_id</code>. 监考老师表记录id
     */
    public final TableField<InvigilatorGrantDetailRecord, String> INVIGILATOR_ID = createField("f_invigilator_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "监考老师表记录id");

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_exam_id</code>. 考试id
     */
    public final TableField<InvigilatorGrantDetailRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_member_id</code>. 用户id
     */
    public final TableField<InvigilatorGrantDetailRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_organization_id</code>. 组织id
     */
    public final TableField<InvigilatorGrantDetailRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织id");

    /**
     * The column <code>exam.t_invigilator_grant_detail.f_create_time</code>. 创建时间
     */
    public final TableField<InvigilatorGrantDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>exam.t_invigilator_grant_detail</code> table reference
     */
    public InvigilatorGrantDetail() {
        this("t_invigilator_grant_detail", null);
    }

    /**
     * Create an aliased <code>exam.t_invigilator_grant_detail</code> table reference
     */
    public InvigilatorGrantDetail(String alias) {
        this(alias, INVIGILATOR_GRANT_DETAIL);
    }

    private InvigilatorGrantDetail(String alias, Table<InvigilatorGrantDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private InvigilatorGrantDetail(String alias, Table<InvigilatorGrantDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "监考范围详情表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<InvigilatorGrantDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_INVIGILATOR_GRANT_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<InvigilatorGrantDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<InvigilatorGrantDetailRecord>>asList(Keys.KEY_T_INVIGILATOR_GRANT_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public InvigilatorGrantDetail as(String alias) {
        return new InvigilatorGrantDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public InvigilatorGrantDetail rename(String name) {
        return new InvigilatorGrantDetail(name, null);
    }
}
