/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamOnlineLogRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamOnlineLog extends TableImpl<ExamOnlineLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_exam_online_log</code>
     */
    public static final ExamOnlineLog EXAM_ONLINE_LOG = new ExamOnlineLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamOnlineLogRecord> getRecordType() {
        return ExamOnlineLogRecord.class;
    }

    /**
     * The column <code>exam-stu.t_exam_online_log.f_id</code>. 主键
     */
    public final TableField<ExamOnlineLogRecord, Long> ID = createField("f_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "主键");

    /**
     * The column <code>exam-stu.t_exam_online_log.f_business_id</code>. 业务Id（考试id）
     */
    public final TableField<ExamOnlineLogRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务Id（考试id）");

    /**
     * The column <code>exam-stu.t_exam_online_log.f_member_id</code>. 用户Id
     */
    public final TableField<ExamOnlineLogRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户Id");

    /**
     * The column <code>exam-stu.t_exam_online_log.f_expire_time</code>. 过期时间
     */
    public final TableField<ExamOnlineLogRecord, Long> EXPIRE_TIME = createField("f_expire_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "过期时间");

    /**
     * The column <code>exam-stu.t_exam_online_log.f_create_time</code>. 创建时间
     */
    public final TableField<ExamOnlineLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_exam_online_log.f_update_time</code>. 更新时间
     */
    public final TableField<ExamOnlineLogRecord, Timestamp> UPDATE_TIME = createField("f_update_time", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * Create a <code>exam-stu.t_exam_online_log</code> table reference
     */
    public ExamOnlineLog() {
        this("t_exam_online_log", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_exam_online_log</code> table reference
     */
    public ExamOnlineLog(String alias) {
        this(alias, EXAM_ONLINE_LOG);
    }

    private ExamOnlineLog(String alias, Table<ExamOnlineLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private ExamOnlineLog(String alias, Table<ExamOnlineLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Identity<ExamOnlineLogRecord, Long> getIdentity() {
        return Keys.IDENTITY_EXAM_ONLINE_LOG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamOnlineLogRecord> getPrimaryKey() {
        return Keys.KEY_T_EXAM_ONLINE_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamOnlineLogRecord>> getKeys() {
        return Arrays.<UniqueKey<ExamOnlineLogRecord>>asList(Keys.KEY_T_EXAM_ONLINE_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamOnlineLog as(String alias) {
        return new ExamOnlineLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamOnlineLog rename(String name) {
        return new ExamOnlineLog(name, null);
    }
}
