/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ResearchQuestionaryRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchQuestionary extends TableImpl<ResearchQuestionaryRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_research_questionary</code>
     */
    public static final ResearchQuestionary RESEARCH_QUESTIONARY = new ResearchQuestionary();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchQuestionaryRecord> getRecordType() {
        return ResearchQuestionaryRecord.class;
    }

    /**
     * The column <code>exam.t_research_questionary.f_id</code>. 主键
     */
    public final TableField<ResearchQuestionaryRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_research_questionary.f_cover_id</code>. 封面
     */
    public final TableField<ResearchQuestionaryRecord, String> COVER_ID = createField("f_cover_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面");

    /**
     * The column <code>exam.t_research_questionary.f_organization_id</code>. 所属组织
     */
    public final TableField<ResearchQuestionaryRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属组织");

    /**
     * The column <code>exam.t_research_questionary.f_publish_organization_id</code>. 发布部门
     */
    public final TableField<ResearchQuestionaryRecord, String> PUBLISH_ORGANIZATION_ID = createField("f_publish_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布部门");

    /**
     * The column <code>exam.t_research_questionary.f_publish_member_id</code>. 发布人
     */
    public final TableField<ResearchQuestionaryRecord, String> PUBLISH_MEMBER_ID = createField("f_publish_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布人");

    /**
     * The column <code>exam.t_research_questionary.f_start_time</code>. 开始时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始时间");

    /**
     * The column <code>exam.t_research_questionary.f_end_time</code>. 结束时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "结束时间");

    /**
     * The column <code>exam.t_research_questionary.f_is_anonymity</code>. 是否匿名 1：是 0： 否
     */
    public final TableField<ResearchQuestionaryRecord, Integer> IS_ANONYMITY = createField("f_is_anonymity", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否匿名 1：是 0： 否");

    /**
     * The column <code>exam.t_research_questionary.f_permit_view_count</code>. 是否允许查看统计结果 1:是 0： 否
     */
    public final TableField<ResearchQuestionaryRecord, Integer> PERMIT_VIEW_COUNT = createField("f_permit_view_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否允许查看统计结果 1:是 0： 否");

    /**
     * The column <code>exam.t_research_questionary.f_questionary_detail</code>. 问卷须知
     */
    public final TableField<ResearchQuestionaryRecord, String> QUESTIONARY_DETAIL = createField("f_questionary_detail", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问卷须知");

    /**
     * The column <code>exam.t_research_questionary.f_create_time</code>. 创建时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_research_questionary.f_name</code>. 名字
     */
    public final TableField<ResearchQuestionaryRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "名字");

    /**
     * The column <code>exam.t_research_questionary.f_type</code>. 类型 1：调研活动 2：调研问卷 3：评估问卷
     */
    public final TableField<ResearchQuestionaryRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型 1：调研活动 2：调研问卷 3：评估问卷");

    /**
     * The column <code>exam.t_research_questionary.f_status</code>. 1:未发布，2：未开始，3：进行中，4：已结束，5：已撤销
     */
    public final TableField<ResearchQuestionaryRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:未发布，2：未开始，3：进行中，4：已结束，5：已撤销");

    /**
     * The column <code>exam.t_research_questionary.f_publish_time</code>. 发布时间
     */
    public final TableField<ResearchQuestionaryRecord, Long> PUBLISH_TIME = createField("f_publish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "发布时间");

    /**
     * The column <code>exam.t_research_questionary.f_push_personal_center</code>. 是否推送个人中心
     */
    public final TableField<ResearchQuestionaryRecord, Integer> PUSH_PERSONAL_CENTER = createField("f_push_personal_center", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否推送个人中心");

    /**
     * The column <code>exam.t_research_questionary.f_answer_paper_rule</code>. 答卷方式 1：一页多题 2：一页一题
     */
    public final TableField<ResearchQuestionaryRecord, Integer> ANSWER_PAPER_RULE = createField("f_answer_paper_rule", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "答卷方式 1：一页多题 2：一页一题");

    /**
     * The column <code>exam.t_research_questionary.f_create_member</code>. 创建人
     */
    public final TableField<ResearchQuestionaryRecord, String> CREATE_MEMBER = createField("f_create_member", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>exam.t_research_questionary.f_refrence_amount</code>. 关联数据
     */
    public final TableField<ResearchQuestionaryRecord, Integer> REFRENCE_AMOUNT = createField("f_refrence_amount", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "关联数据");

    /**
     * The column <code>exam.t_research_questionary.f_source_type</code>. 来源， 1：活动，2：课程，3：专题, 4: 班级
     */
    public final TableField<ResearchQuestionaryRecord, Integer> SOURCE_TYPE = createField("f_source_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "来源， 1：活动，2：课程，3：专题, 4: 班级");

    /**
     * The column <code>exam.t_research_questionary.f_view_number</code>. 浏览数
     */
    public final TableField<ResearchQuestionaryRecord, Integer> VIEW_NUMBER = createField("f_view_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "浏览数");

    /**
     * The column <code>exam.t_research_questionary.f_question_number</code>. 问题数量,主要APP用
     */
    public final TableField<ResearchQuestionaryRecord, Integer> QUESTION_NUMBER = createField("f_question_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "问题数量,主要APP用");

    /**
     * The column <code>exam.t_research_questionary.f_source_id</code>. 源问卷id，对于选择的问卷，都会建立一个副本与业务数据关联
     */
    public final TableField<ResearchQuestionaryRecord, String> SOURCE_ID = createField("f_source_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "源问卷id，对于选择的问卷，都会建立一个副本与业务数据关联");

    /**
     * The column <code>exam.t_research_questionary.f_cover_id_path</code>. 封面路径
     */
    public final TableField<ResearchQuestionaryRecord, String> COVER_ID_PATH = createField("f_cover_id_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "封面路径");

    /**
     * The column <code>exam.t_research_questionary.f_share_num</code>. 分享次数
     */
    public final TableField<ResearchQuestionaryRecord, Integer> SHARE_NUM = createField("f_share_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分享次数");

    /**
     * The column <code>exam.t_research_questionary.f_join_number</code>. 已参加人数
     */
    public final TableField<ResearchQuestionaryRecord, Integer> JOIN_NUMBER = createField("f_join_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "已参加人数");

    /**
     * The column <code>exam.t_research_questionary.f_not_join_number</code>. 未参与人数（调研活动才有值，其余都是0）
     */
    public final TableField<ResearchQuestionaryRecord, String> NOT_JOIN_NUMBER = createField("f_not_join_number", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "未参与人数（调研活动才有值，其余都是0）");

    /**
     * Create a <code>exam.t_research_questionary</code> table reference
     */
    public ResearchQuestionary() {
        this("t_research_questionary", null);
    }

    /**
     * Create an aliased <code>exam.t_research_questionary</code> table reference
     */
    public ResearchQuestionary(String alias) {
        this(alias, RESEARCH_QUESTIONARY);
    }

    private ResearchQuestionary(String alias, Table<ResearchQuestionaryRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchQuestionary(String alias, Table<ResearchQuestionaryRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchQuestionaryRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_QUESTIONARY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchQuestionaryRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchQuestionaryRecord>>asList(Keys.KEY_T_RESEARCH_QUESTIONARY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchQuestionary as(String alias) {
        return new ResearchQuestionary(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchQuestionary rename(String name) {
        return new ResearchQuestionary(name, null);
    }
}
