/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperInstanceRecord;
import org.jooq.impl.TableImpl;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;
import javax.annotation.Generated;


/**
 * 试卷实例
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperInstance extends TableImpl<PaperInstanceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_instance</code>
     */
    public static final PaperInstance PAPER_INSTANCE = new PaperInstance();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperInstanceRecord> getRecordType() {
        return PaperInstanceRecord.class;
    }

    /**
     * The column <code>exam.t_paper_instance.f_id</code>. ID
     */
    public final TableField<PaperInstanceRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_paper_instance.f_create_time</code>. 创建时间
     */
    public final TableField<PaperInstanceRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_paper_instance.f_paper_class_id</code>. 所属试卷类
     */
    public final TableField<PaperInstanceRecord, String> PAPER_CLASS_ID = createField("f_paper_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属试卷类");

    /**
     * The column <code>exam.t_paper_instance.f_is_subjective</code>. 是否存在主观题(0：否 1：是)
     */
    public final TableField<PaperInstanceRecord, Integer> IS_SUBJECTIVE = createField("f_is_subjective", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否存在主观题(0：否 1：是)");

    /**
     * The column <code>exam.t_paper_instance.f_exam_id</code>. 考试id
     */
    public final TableField<PaperInstanceRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_paper_instance.f_remote_order_content</code>.
     */
    public final TableField<PaperInstanceRecord, String> REMOTE_ORDER_CONTENT = createField("f_remote_order_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_paper_instance.f_modify_date</code>. 修改时间
     */
    public final TableField<PaperInstanceRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_paper_instance</code> table reference
     */
    public PaperInstance() {
        this("t_paper_instance", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_instance</code> table reference
     */
    public PaperInstance(String alias) {
        this(alias, PAPER_INSTANCE);
    }

    private PaperInstance(String alias, Table<PaperInstanceRecord> aliased) {
        this(alias, aliased, null);
    }

    private PaperInstance(String alias, Table<PaperInstanceRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试卷实例");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperInstanceRecord> getPrimaryKey() {
        return Keys.KEY_T_PAPER_INSTANCE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperInstanceRecord>> getKeys() {
        return Arrays.<UniqueKey<PaperInstanceRecord>>asList(Keys.KEY_T_PAPER_INSTANCE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperInstance as(String alias) {
        return new PaperInstance(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperInstance rename(String name) {
        return new PaperInstance(name, null);
    }
}
