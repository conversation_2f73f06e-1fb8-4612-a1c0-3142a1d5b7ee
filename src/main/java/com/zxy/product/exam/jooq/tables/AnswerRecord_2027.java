/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.AnswerRecord_2027Record;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;
import java.sql.Timestamp;

/**
 * 答案记录表_2027
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AnswerRecord_2027 extends TableImpl<AnswerRecord_2027Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_answer_record_2027</code>
     */
    public static final AnswerRecord_2027 ANSWER_RECORD_2027 = new AnswerRecord_2027();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AnswerRecord_2027Record> getRecordType() {
        return AnswerRecord_2027Record.class;
    }

    /**
     * The column <code>exam.t_answer_record_2027.f_id</code>.
     */
    public final TableField<AnswerRecord_2027Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_create_time</code>.
     */
    public final TableField<AnswerRecord_2027Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_exam_record_id</code>.
     */
    public final TableField<AnswerRecord_2027Record, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_question_id</code>.
     */
    public final TableField<AnswerRecord_2027Record, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_answer</code>.
     */
    public final TableField<AnswerRecord_2027Record, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_is_right</code>.
     */
    public final TableField<AnswerRecord_2027Record, Integer> IS_RIGHT = createField("f_is_right", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_score</code>.
     */
    public final TableField<AnswerRecord_2027Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_answer_record_2027.f_modify_date</code>. 修改时间
     */
    public final TableField<AnswerRecord_2027Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_answer_record_2027</code> table reference
     */
    public AnswerRecord_2027() {
        this("t_answer_record_2027", null);
    }

    /**
     * Create an aliased <code>exam.t_answer_record_2027</code> table reference
     */
    public AnswerRecord_2027(String alias) {
        this(alias, ANSWER_RECORD_2027);
    }

    private AnswerRecord_2027(String alias, Table<AnswerRecord_2027Record> aliased) {
        this(alias, aliased, null);
    }

    private AnswerRecord_2027(String alias, Table<AnswerRecord_2027Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "答案记录表_2027");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AnswerRecord_2027Record> getPrimaryKey() {
        return Keys.KEY_T_ANSWER_RECORD_2027_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AnswerRecord_2027Record>> getKeys() {
        return Arrays.<UniqueKey<AnswerRecord_2027Record>>asList(Keys.KEY_T_ANSWER_RECORD_2027_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AnswerRecord_2027 as(String alias) {
        return new AnswerRecord_2027(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AnswerRecord_2027 rename(String name) {
        return new AnswerRecord_2027(name, null);
    }
}
