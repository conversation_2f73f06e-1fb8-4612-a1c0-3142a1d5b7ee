/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.RelevanceCourseExamRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 考试关联课程
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RelevanceCourseExam extends TableImpl<RelevanceCourseExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_relevance_course_exam</code>
     */
    public static final RelevanceCourseExam RELEVANCE_COURSE_EXAM = new RelevanceCourseExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<RelevanceCourseExamRecord> getRecordType() {
        return RelevanceCourseExamRecord.class;
    }

    /**
     * The column <code>exam.t_relevance_course_exam.f_id</code>. 主键
     */
    public final TableField<RelevanceCourseExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_relevance_course_exam.f_create_time</code>. 创建时间
     */
    public final TableField<RelevanceCourseExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_relevance_course_exam.f_course_id</code>. 课程/专题 id
     */
    public final TableField<RelevanceCourseExamRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "课程/专题 id");

    /**
     * The column <code>exam.t_relevance_course_exam.f_exam_id</code>. 考试id
     */
    public final TableField<RelevanceCourseExamRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "考试id");

    /**
     * Create a <code>exam.t_relevance_course_exam</code> table reference
     */
    public RelevanceCourseExam() {
        this("t_relevance_course_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_relevance_course_exam</code> table reference
     */
    public RelevanceCourseExam(String alias) {
        this(alias, RELEVANCE_COURSE_EXAM);
    }

    private RelevanceCourseExam(String alias, Table<RelevanceCourseExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private RelevanceCourseExam(String alias, Table<RelevanceCourseExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试关联课程");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<RelevanceCourseExamRecord> getPrimaryKey() {
        return Keys.KEY_T_RELEVANCE_COURSE_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<RelevanceCourseExamRecord>> getKeys() {
        return Arrays.<UniqueKey<RelevanceCourseExamRecord>>asList(Keys.KEY_T_RELEVANCE_COURSE_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public RelevanceCourseExam as(String alias) {
        return new RelevanceCourseExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public RelevanceCourseExam rename(String name) {
        return new RelevanceCourseExam(name, null);
    }
}
