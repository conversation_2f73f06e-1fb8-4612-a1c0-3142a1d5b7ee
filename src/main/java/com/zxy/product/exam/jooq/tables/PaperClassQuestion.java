/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PaperClassQuestionRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 试卷类与试题关联
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PaperClassQuestion extends TableImpl<PaperClassQuestionRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_paper_class_question</code>
     */
    public static final PaperClassQuestion PAPER_CLASS_QUESTION = new PaperClassQuestion();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PaperClassQuestionRecord> getRecordType() {
        return PaperClassQuestionRecord.class;
    }

    /**
     * The column <code>exam.t_paper_class_question.f_id</code>. ID
     */
    public final TableField<PaperClassQuestionRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_paper_class_question.f_paper_class_id</code>. 试卷类ID
     */
    public final TableField<PaperClassQuestionRecord, String> PAPER_CLASS_ID = createField("f_paper_class_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试卷类ID");

    /**
     * The column <code>exam.t_paper_class_question.f_question_id</code>. 试题id
     */
    public final TableField<PaperClassQuestionRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题id");

    /**
     * The column <code>exam.t_paper_class_question.f_score</code>. 状态(0：已发布 1：未发布)
     */
    public final TableField<PaperClassQuestionRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态(0：已发布 1：未发布)");

    /**
     * The column <code>exam.t_paper_class_question.f_sequence</code>.
     */
    public final TableField<PaperClassQuestionRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>exam.t_paper_class_question.f_create_time</code>. 创建时间
     */
    public final TableField<PaperClassQuestionRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_paper_class_question.f_is_from_selected</code>. 是否来自选择题目 0:否 1：是
     */
    public final TableField<PaperClassQuestionRecord, Integer> IS_FROM_SELECTED = createField("f_is_from_selected", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否来自选择题目 0:否 1：是");

    /**
     * The column <code>exam.t_paper_class_question.f_parent_id</code>. 阅读题的父id
     */
    public final TableField<PaperClassQuestionRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "阅读题的父id");

    /**
     * Create a <code>exam.t_paper_class_question</code> table reference
     */
    public PaperClassQuestion() {
        this("t_paper_class_question", null);
    }

    /**
     * Create an aliased <code>exam.t_paper_class_question</code> table reference
     */
    public PaperClassQuestion(String alias) {
        this(alias, PAPER_CLASS_QUESTION);
    }

    private PaperClassQuestion(String alias, Table<PaperClassQuestionRecord> aliased) {
        this(alias, aliased, null);
    }

    private PaperClassQuestion(String alias, Table<PaperClassQuestionRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试卷类与试题关联");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PaperClassQuestionRecord> getPrimaryKey() {
        return Keys.KEY_T_PAPER_CLASS_QUESTION_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PaperClassQuestionRecord>> getKeys() {
        return Arrays.<UniqueKey<PaperClassQuestionRecord>>asList(Keys.KEY_T_PAPER_CLASS_QUESTION_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PaperClassQuestion as(String alias) {
        return new PaperClassQuestion(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PaperClassQuestion rename(String name) {
        return new PaperClassQuestion(name, null);
    }
}
