/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ActivityRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 活动
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Activity extends TableImpl<ActivityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_activity</code>
     */
    public static final Activity ACTIVITY = new Activity();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ActivityRecord> getRecordType() {
        return ActivityRecord.class;
    }

    /**
     * The column <code>exam.t_activity.f_id</code>.
     */
    public final TableField<ActivityRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_activity.f_organization_id</code>. 组织id
     */
    public final TableField<ActivityRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织id");

    /**
     * The column <code>exam.t_activity.f_name</code>. 活动名称
     */
    public final TableField<ActivityRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动名称");

    /**
     * The column <code>exam.t_activity.f_start_time</code>. 开始时间
     */
    public final TableField<ActivityRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始时间");

    /**
     * The column <code>exam.t_activity.f_end_time</code>. 结束时间
     */
    public final TableField<ActivityRecord, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "结束时间");

    /**
     * The column <code>exam.t_activity.f_type</code>. 类型：1:班级 2 直播 3考试 4调研 5MOOC
     */
    public final TableField<ActivityRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型：1:班级 2 直播 3考试 4调研 5MOOC");

    /**
     * The column <code>exam.t_activity.f_target_id</code>. 目标id
     */
    public final TableField<ActivityRecord, String> TARGET_ID = createField("f_target_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "目标id");

    /**
     * The column <code>exam.t_activity.f_sequence</code>. 顺序
     */
    public final TableField<ActivityRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "顺序");

    /**
     * The column <code>exam.t_activity.f_recommend_time</code>. 推荐时间
     */
    public final TableField<ActivityRecord, Long> RECOMMEND_TIME = createField("f_recommend_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "推荐时间");

    /**
     * The column <code>exam.t_activity.f_is_recommend</code>. 是否是首页推荐；1是，0否
     */
    public final TableField<ActivityRecord, Integer> IS_RECOMMEND = createField("f_is_recommend", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否是首页推荐；1是，0否");

    /**
     * The column <code>exam.t_activity.f_client</code>. 适用终端，0pc&amp;app 1pc 2app
     */
    public final TableField<ActivityRecord, Integer> CLIENT = createField("f_client", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "适用终端，0pc&app 1pc 2app");

    /**
     * The column <code>exam.t_activity.f_status</code>. 活动状态，1未开始 2报名中 3进行中 4已结束
     */
    public final TableField<ActivityRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "活动状态，1未开始 2报名中 3进行中 4已结束");

    /**
     * The column <code>exam.t_activity.f_create_time</code>. 创建时间
     */
    public final TableField<ActivityRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_activity.f_root_organization_id</code>. 活动所属根组织
     */
    public final TableField<ActivityRecord, String> ROOT_ORGANIZATION_ID = createField("f_root_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动所属根组织");

    /**
     * The column <code>exam.t_activity.f_join_size</code>. 活动参与人数
     */
    public final TableField<ActivityRecord, Integer> JOIN_SIZE = createField("f_join_size", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "活动参与人数");

    /**
     * The column <code>exam.t_activity.f_description</code>. 活动描述
     */
    public final TableField<ActivityRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(5000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动描述");

    /**
     * The column <code>exam.t_activity.f_cover_path</code>. 活动封面路径
     */
    public final TableField<ActivityRecord, String> COVER_PATH = createField("f_cover_path", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "活动封面路径");

    /**
     * The column <code>exam.t_activity.f_is_open</code>. 是否开放，0不开放，1开放
     */
    public final TableField<ActivityRecord, Integer> IS_OPEN = createField("f_is_open", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "是否开放，0不开放，1开放");

    /**
     * Create a <code>exam.t_activity</code> table reference
     */
    public Activity() {
        this("t_activity", null);
    }

    /**
     * Create an aliased <code>exam.t_activity</code> table reference
     */
    public Activity(String alias) {
        this(alias, ACTIVITY);
    }

    private Activity(String alias, Table<ActivityRecord> aliased) {
        this(alias, aliased, null);
    }

    private Activity(String alias, Table<ActivityRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "活动");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ActivityRecord> getPrimaryKey() {
        return Keys.KEY_T_ACTIVITY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ActivityRecord>> getKeys() {
        return Arrays.<UniqueKey<ActivityRecord>>asList(Keys.KEY_T_ACTIVITY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Activity as(String alias) {
        return new Activity(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Activity rename(String name) {
        return new Activity(name, null);
    }
}
