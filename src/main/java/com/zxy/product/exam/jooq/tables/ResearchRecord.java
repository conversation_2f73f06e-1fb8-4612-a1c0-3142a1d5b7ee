/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ResearchRecordRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResearchRecord extends TableImpl<ResearchRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_research_record</code>
     */
    public static final ResearchRecord RESEARCH_RECORD = new ResearchRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ResearchRecordRecord> getRecordType() {
        return ResearchRecordRecord.class;
    }

    /**
     * The column <code>exam.t_research_record.f_id</code>. 主键
     */
    public final TableField<ResearchRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>exam.t_research_record.f_create_time</code>. 创建时间
     */
    public final TableField<ResearchRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_research_record.f_member_id</code>. 参与人
     */
    public final TableField<ResearchRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "参与人");

    /**
     * The column <code>exam.t_research_record.f_status</code>. 状态 0：未参与 1：已参与
     */
    public final TableField<ResearchRecordRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态 0：未参与 1：已参与");

    /**
     * The column <code>exam.t_research_record.f_research_questionary_id</code>. 调研ID
     */
    public final TableField<ResearchRecordRecord, String> RESEARCH_QUESTIONARY_ID = createField("f_research_questionary_id", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "调研ID");

    /**
     * The column <code>exam.t_research_record.f_submit_time</code>. 提交时间
     */
    public final TableField<ResearchRecordRecord, Long> SUBMIT_TIME = createField("f_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提交时间");

    /**
     * The column <code>exam.t_research_record.f_start_time</code>. 开始时间
     */
    public final TableField<ResearchRecordRecord, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始时间");

    /**
     * The column <code>exam.t_research_record.f_score</code>. 得分
     */
    public final TableField<ResearchRecordRecord, Long> SCORE = createField("f_score", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "得分");

    /**
     * The column <code>exam.t_research_record.f_client_type</code>. 终端类型 1:pc,2:移动，3：微信
     */
    public final TableField<ResearchRecordRecord, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "终端类型 1:pc,2:移动，3：微信");

    /**
     * The column <code>exam.t_research_record.f_business_id</code>. 业务Id  包括课程 专题 班级
     */
    public final TableField<ResearchRecordRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务Id  包括课程 专题 班级");

    /**
     * The column <code>exam.t_research_record.f_is_view_detail</code>. 是否访问过详情页，0否 1是
     */
    public final TableField<ResearchRecordRecord, Integer> IS_VIEW_DETAIL = createField("f_is_view_detail", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否访问过详情页，0否 1是");

    /**
     * The column <code>exam.t_research_record.f_is_summary</code>. 是否已计算统计结果，0否 1是
     */
    public final TableField<ResearchRecordRecord, Integer> IS_SUMMARY = createField("f_is_summary", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否已计算统计结果，0否 1是");

    /**
     * The column <code>exam.t_research_record.f_modify_date</code>. 修改时间
     */
    public final TableField<ResearchRecordRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam.t_research_record</code> table reference
     */
    public ResearchRecord() {
        this("t_research_record", null);
    }

    /**
     * Create an aliased <code>exam.t_research_record</code> table reference
     */
    public ResearchRecord(String alias) {
        this(alias, RESEARCH_RECORD);
    }

    private ResearchRecord(String alias, Table<ResearchRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ResearchRecord(String alias, Table<ResearchRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ResearchRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_RESEARCH_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ResearchRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ResearchRecordRecord>>asList(Keys.KEY_T_RESEARCH_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResearchRecord as(String alias) {
        return new ResearchRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ResearchRecord rename(String name) {
        return new ResearchRecord(name, null);
    }
}
