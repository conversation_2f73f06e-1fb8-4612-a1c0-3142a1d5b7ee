/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.GridAuditRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 网格长审核管理
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GridAudit extends TableImpl<GridAuditRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_grid_audit</code>
     */
    public static final GridAudit GRID_AUDIT = new GridAudit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<GridAuditRecord> getRecordType() {
        return GridAuditRecord.class;
    }

    /**
     * The column <code>exam.t_grid_audit.f_id</code>.
     */
    public final TableField<GridAuditRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_grid_audit.f_create_time</code>. 创建时间
     */
    public final TableField<GridAuditRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "创建时间");

    /**
     * The column <code>exam.t_grid_audit.f_member_id</code>. 学员id
     */
    public final TableField<GridAuditRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "学员id");

    /**
     * The column <code>exam.t_grid_audit.f_professional_knowledge</code>. 专业知识，0否1是
     */
    public final TableField<GridAuditRecord, Integer> PROFESSIONAL_KNOWLEDGE = createField("f_professional_knowledge", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "专业知识，0否1是");

    /**
     * The column <code>exam.t_grid_audit.f_key_capabilities</code>. 关键能力，0否1是
     */
    public final TableField<GridAuditRecord, Integer> KEY_CAPABILITIES = createField("f_key_capabilities", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "关键能力，0否1是");

    /**
     * The column <code>exam.t_grid_audit.f_organizational_feedback</code>. 组织回馈，0否1是
     */
    public final TableField<GridAuditRecord, Integer> ORGANIZATIONAL_FEEDBACK = createField("f_organizational_feedback", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "组织回馈，0否1是");

    /**
     * The column <code>exam.t_grid_audit.f_level_id</code>. 认证级别id
     */
    public final TableField<GridAuditRecord, String> LEVEL_ID = createField("f_level_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "认证级别id");

    /**
     * The column <code>exam.t_grid_audit.f_pass</code>. 通过，0否1是
     */
    public final TableField<GridAuditRecord, Integer> PASS = createField("f_pass", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "通过，0否1是");

    /**
     * The column <code>exam.t_grid_audit.f_auth_time</code>. 认证时间
     */
    public final TableField<GridAuditRecord, Long> AUTH_TIME = createField("f_auth_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "认证时间");

    /**
     * The column <code>exam.t_grid_audit.f_num</code>. 证书编号
     */
    public final TableField<GridAuditRecord, String> NUM = createField("f_num", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证书编号");

    /**
     * The column <code>exam.t_grid_audit.f_updated</code>. 被修改，0否1是
     */
    public final TableField<GridAuditRecord, Integer> UPDATED = createField("f_updated", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "被修改，0否1是");

    /**
     * Create a <code>exam.t_grid_audit</code> table reference
     */
    public GridAudit() {
        this("t_grid_audit", null);
    }

    /**
     * Create an aliased <code>exam.t_grid_audit</code> table reference
     */
    public GridAudit(String alias) {
        this(alias, GRID_AUDIT);
    }

    private GridAudit(String alias, Table<GridAuditRecord> aliased) {
        this(alias, aliased, null);
    }

    private GridAudit(String alias, Table<GridAuditRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "网格长审核管理");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<GridAuditRecord> getPrimaryKey() {
        return Keys.KEY_T_GRID_AUDIT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<GridAuditRecord>> getKeys() {
        return Arrays.<UniqueKey<GridAuditRecord>>asList(Keys.KEY_T_GRID_AUDIT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public GridAudit as(String alias) {
        return new GridAudit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public GridAudit rename(String name) {
        return new GridAudit(name, null);
    }
}
