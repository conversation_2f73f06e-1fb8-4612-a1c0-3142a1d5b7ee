/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.SignupRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Signup extends TableImpl<SignupRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_signup</code>
     */
    public static final Signup SIGNUP = new Signup();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SignupRecord> getRecordType() {
        return SignupRecord.class;
    }

    /**
     * The column <code>exam-stu.t_signup.f_id</code>.
     */
    public final TableField<SignupRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_signup.f_create_time</code>.
     */
    public final TableField<SignupRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam-stu.t_signup.f_exam_id</code>.
     */
    public final TableField<SignupRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_signup.f_member_id</code>.
     */
    public final TableField<SignupRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_signup.f_organization_id</code>.
     */
    public final TableField<SignupRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam-stu.t_signup.f_status</code>. 审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名
     */
    public final TableField<SignupRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "审核状态 1：待审核，2：已通过，3：被拒绝 4: 取消报名");

    /**
     * The column <code>exam-stu.t_signup.f_audit_member_id</code>. 报名审核人
     */
    public final TableField<SignupRecord, String> AUDIT_MEMBER_ID = createField("f_audit_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "报名审核人");

    /**
     * The column <code>exam-stu.t_signup.f_modify_date</code>. 修改时间
     */
    public final TableField<SignupRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam-stu.t_signup</code> table reference
     */
    public Signup() {
        this("t_signup", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_signup</code> table reference
     */
    public Signup(String alias) {
        this(alias, SIGNUP);
    }

    private Signup(String alias, Table<SignupRecord> aliased) {
        this(alias, aliased, null);
    }

    private Signup(String alias, Table<SignupRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SignupRecord> getPrimaryKey() {
        return Keys.KEY_T_SIGNUP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SignupRecord>> getKeys() {
        return Arrays.<UniqueKey<SignupRecord>>asList(Keys.KEY_T_SIGNUP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Signup as(String alias) {
        return new Signup(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Signup rename(String name) {
        return new Signup(name, null);
    }
}
