/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.CloudExamRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;

/**
 * 云改专区考试
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CloudExam extends TableImpl<CloudExamRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_cloud_exam</code>
     */
    public static final CloudExam CLOUD_EXAM = new CloudExam();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CloudExamRecord> getRecordType() {
        return CloudExamRecord.class;
    }

    /**
     * The column <code>exam.t_cloud_exam.f_id</code>.
     */
    public final TableField<CloudExamRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_cloud_exam.f_create_time</code>.
     */
    public final TableField<CloudExamRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_cloud_exam.f_exam_id</code>.
     */
    public final TableField<CloudExamRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_cloud_exam.f_num</code>. 考试序号
     */
    public final TableField<CloudExamRecord, String> NUM = createField("f_num", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试序号");

    /**
     * Create a <code>exam.t_cloud_exam</code> table reference
     */
    public CloudExam() {
        this("t_cloud_exam", null);
    }

    /**
     * Create an aliased <code>exam.t_cloud_exam</code> table reference
     */
    public CloudExam(String alias) {
        this(alias, CLOUD_EXAM);
    }

    private CloudExam(String alias, Table<CloudExamRecord> aliased) {
        this(alias, aliased, null);
    }

    private CloudExam(String alias, Table<CloudExamRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "云改专区考试");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CloudExamRecord> getPrimaryKey() {
        return Keys.KEY_T_CLOUD_EXAM_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CloudExamRecord>> getKeys() {
        return Arrays.<UniqueKey<CloudExamRecord>>asList(Keys.KEY_T_CLOUD_EXAM_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CloudExam as(String alias) {
        return new CloudExam(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CloudExam rename(String name) {
        return new CloudExam(name, null);
    }
}
