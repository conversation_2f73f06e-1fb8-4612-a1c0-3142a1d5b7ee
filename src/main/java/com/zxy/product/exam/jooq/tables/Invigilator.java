/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.InvigilatorRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 考试监考老师表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Invigilator extends TableImpl<InvigilatorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_invigilator</code>
     */
    public static final Invigilator INVIGILATOR = new Invigilator();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<InvigilatorRecord> getRecordType() {
        return InvigilatorRecord.class;
    }

    /**
     * The column <code>exam.t_invigilator.f_id</code>. ID
     */
    public final TableField<InvigilatorRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_invigilator.f_exam_id</code>. 考试id
     */
    public final TableField<InvigilatorRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_invigilator.f_member_id</code>. 用户id
     */
    public final TableField<InvigilatorRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>exam.t_invigilator.f_create_time</code>. 创建时间
     */
    public final TableField<InvigilatorRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_invigilator.f_invigilator_duty</code>. 监考职责 {r1:1,r2:0,r3:1,r4:0,r5:0,r6:1,r7:0} 0:不勾选，1：勾选
     */
    public final TableField<InvigilatorRecord, String> INVIGILATOR_DUTY = createField("f_invigilator_duty", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "监考职责 {r1:1,r2:0,r3:1,r4:0,r5:0,r6:1,r7:0} 0:不勾选，1：勾选");

    /**
     * The column <code>exam.t_invigilator.f_grant_all</code>. 是否监考整场考试：0否 1是
     */
    public final TableField<InvigilatorRecord, Integer> GRANT_ALL = createField("f_grant_all", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否监考整场考试：0否 1是");

    /**
     * Create a <code>exam.t_invigilator</code> table reference
     */
    public Invigilator() {
        this("t_invigilator", null);
    }

    /**
     * Create an aliased <code>exam.t_invigilator</code> table reference
     */
    public Invigilator(String alias) {
        this(alias, INVIGILATOR);
    }

    private Invigilator(String alias, Table<InvigilatorRecord> aliased) {
        this(alias, aliased, null);
    }

    private Invigilator(String alias, Table<InvigilatorRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试监考老师表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<InvigilatorRecord> getPrimaryKey() {
        return Keys.KEY_T_INVIGILATOR_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<InvigilatorRecord>> getKeys() {
        return Arrays.<UniqueKey<InvigilatorRecord>>asList(Keys.KEY_T_INVIGILATOR_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Invigilator as(String alias) {
        return new Invigilator(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Invigilator rename(String name) {
        return new Invigilator(name, null);
    }
}
