/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq;


import com.zxy.product.exam.jooq.tables.Exam;
import com.zxy.product.exam.jooq.tables.*;

import javax.annotation.Generated;


/**
 * Convenience access to all tables in exam
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    //todo exam 和 exam-stu 表信息均在此类中按照shema分块进行维护 （Exam）
    //todo 以下是exam schema中的表信息

    /**
     * 子认证-考试组-考试内容关联关系表
     */
    public static final SubAuthenticatedExamGroup SUB_AUTHENTICATED_EXAM_GROUP = SubAuthenticatedExamGroup.SUB_AUTHENTICATED_EXAM_GROUP;

    /**
     * 子认证-考试证书取消证书记录表
     */
    public static final SubAuthenticatedCancelCertificateRecord SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD = SubAuthenticatedCancelCertificateRecord.SUB_AUTHENTICATED_CANCEL_CERTIFICATE_RECORD;

    /**
     * 历史考试归档表
     */
    public static final ArchivedExam ARCHIVED_EXAM = ArchivedExam.ARCHIVED_EXAM;

    /**
     * 活动
     */
    public static final Activity ACTIVITY = Activity.ACTIVITY;

    /**
     * 通知公告
     */
    public static final Announcement ANNOUNCEMENT = Announcement.ANNOUNCEMENT;

    /**
     * The table <code>exam.t_answer_record_temp</code>.
     */
    public static final AnswerRecordTemp ANSWER_RECORD_TEMP = AnswerRecordTemp.ANSWER_RECORD_TEMP;

    /**
     * 受众项
     */
    public static final AudienceItem AUDIENCE_ITEM = AudienceItem.AUDIENCE_ITEM;

    /**
     * 受众人表
     */
    public static final AudienceMember AUDIENCE_MEMBER = AudienceMember.AUDIENCE_MEMBER;

    /**
     * 资源门户受众对象
     */
    public static final AudienceObject AUDIENCE_OBJECT = AudienceObject.AUDIENCE_OBJECT;

    /**
     * 调研评估关联表
     */
    public static final BusinessResearch BUSINESS_RESEARCH = BusinessResearch.BUSINESS_RESEARCH;

    /**
     * 资源话题表
     */
    public static final BusinessTopic BUSINESS_TOPIC = BusinessTopic.BUSINESS_TOPIC;

    /**
     * 证书迁移记录表
     */
    public static final CertificateHistory CERTIFICATE_HISTORY = CertificateHistory.CERTIFICATE_HISTORY;

    /**
     * 证书发放记录表
     */
    public static final CertificateRecord CERTIFICATE_RECORD = CertificateRecord.CERTIFICATE_RECORD;

    /**
     * 云改专区通知公告
     */
    public static final CloudAnnouncement CLOUD_ANNOUNCEMENT = CloudAnnouncement.CLOUD_ANNOUNCEMENT;

    /**
     * 云改专区考试
     */
    public static final CloudExam CLOUD_EXAM = CloudExam.CLOUD_EXAM;

    /**
     * 移动云考试等级表
     */
    public static final CloudLevel CLOUD_LEVEL = CloudLevel.CLOUD_LEVEL;

    /**
     * 移动云考试专业表
     */
    public static final CloudProfession CLOUD_PROFESSION = CloudProfession.CLOUD_PROFESSION;

    /**
     * The table <code>exam.t_dimension</code>.
     */
    public static final Dimension DIMENSION = Dimension.DIMENSION;

    /**
     * The table <code>exam.t_dimension_question</code>.
     */
    public static final DimensionQuestion DIMENSION_QUESTION = DimensionQuestion.DIMENSION_QUESTION;

    /**
     * 认证考试设备表
     */
    public static final EquipmentType EQUIPMENT_TYPE = EquipmentType.EQUIPMENT_TYPE;

    /**
     * 试题报错原因表
     */
    public static final ErrorReason ERROR_REASON = ErrorReason.ERROR_REASON;

    /**
     * The table <code>exam.t_exam</code>.
     */
    public static final com.zxy.product.exam.jooq.tables.Exam EXAM = Exam.EXAM;

    /**
     * The table <code>exam.t_exam_notice</code>.
     */
    public static final ExamNotice EXAM_NOTICE = ExamNotice.EXAM_NOTICE;

    /**
     * 业务关联话题
     */
    public static final ExamTopic EXAM_TOPIC = ExamTopic.EXAM_TOPIC;

    /**
     * The table <code>exam.t_grant_detail</code>.
     */
    public static final GrantDetail GRANT_DETAIL = GrantDetail.GRANT_DETAIL;

	/**
     * 网格长审核管理
     */
    public static final GridAudit GRID_AUDIT = GridAudit.GRID_AUDIT;

	/**
     * 网格长认证考试关联的学习
     */
    public static final GridCourse GRID_COURSE = GridCourse.GRID_COURSE;

    /**
     * 网格长各级别对应的考试和学习
     */
    public static final GridCourseExam GRID_COURSE_EXAM = GridCourseExam.GRID_COURSE_EXAM;

	/**
     * 网格长材料管理
     */
    public static final GridFile GRID_FILE = GridFile.GRID_FILE;

	/**
     * 网格长各级别对应的正式考试
     */
    public static final GridFormalExam GRID_FORMAL_EXAM = GridFormalExam.GRID_FORMAL_EXAM;

    /**
     * 网格长认证考试等级表
     */
    public static final GridLevel GRID_LEVEL = GridLevel.GRID_LEVEL;

    /**
     * 考试监考老师表
     */
    public static final Invigilator INVIGILATOR = Invigilator.INVIGILATOR;

    /**
     * 监考范围表
     */
    public static final InvigilatorGrant INVIGILATOR_GRANT = InvigilatorGrant.INVIGILATOR_GRANT;

    /**
     * 监考范围详情表
     */
    public static final InvigilatorGrantDetail INVIGILATOR_GRANT_DETAIL = InvigilatorGrantDetail.INVIGILATOR_GRANT_DETAIL;

    /**
     * The table <code>exam.t_mark_config</code>.
     */
    public static final MarkConfig MARK_CONFIG = MarkConfig.MARK_CONFIG;

    /**
     * The table <code>exam.t_mark_record</code>.
     */
    public static final MarkRecord MARK_RECORD = MarkRecord.MARK_RECORD;

    /**
     * The table <code>exam.t_member</code>.
     */
    public static final Member MEMBER = Member.MEMBER;

    /**
     * 增量提交答题记录表
     */
    public static final ModifyAnswerRecord MODIFY_ANSWER_RECORD = ModifyAnswerRecord.MODIFY_ANSWER_RECORD;

    /**
     * 原试题表中存在易错率的数据
     */
    public static final OldQuestion OLD_QUESTION = OldQuestion.OLD_QUESTION;

    /**
     * The table <code>exam.t_organization</code>.
     */
    public static final Organization ORGANIZATION = Organization.ORGANIZATION;

    /**
     * The table <code>exam.t_organization_detail</code>.
     */
    public static final OrganizationDetail ORGANIZATION_DETAIL = OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 试卷类
     */
    public static final PaperClass PAPER_CLASS = PaperClass.PAPER_CLASS;

    /**
     * 试卷类与试题关联
     */
    public static final PaperClassQuestion PAPER_CLASS_QUESTION = PaperClassQuestion.PAPER_CLASS_QUESTION;

    /**
     * 组卷策略
     */
    public static final PaperClassTactic PAPER_CLASS_TACTIC = PaperClassTactic.PAPER_CLASS_TACTIC;

    /**
     * 试卷实例
     */
    public static final PaperInstance PAPER_INSTANCE = PaperInstance.PAPER_INSTANCE;

    /**
     * 试卷实例与试题副本关联
     */
    public static final PaperInstanceQuestionCopy PAPER_INSTANCE_QUESTION_COPY = PaperInstanceQuestionCopy.PAPER_INSTANCE_QUESTION_COPY;

    /**
     * 试卷实例与试题副本关联_2017
     */
    public static final PaperInstanceQuestionCopy_2017 PAPER_INSTANCE_QUESTION_COPY_2017 = PaperInstanceQuestionCopy_2017.PAPER_INSTANCE_QUESTION_COPY_2017;

    /**
     * 试卷实例与试题副本关联_2018
     */
    public static final PaperInstanceQuestionCopy_2018 PAPER_INSTANCE_QUESTION_COPY_2018 = PaperInstanceQuestionCopy_2018.PAPER_INSTANCE_QUESTION_COPY_2018;

    /**
     * 试卷实例与试题副本关联_2019
     */
    public static final PaperInstanceQuestionCopy_2019 PAPER_INSTANCE_QUESTION_COPY_2019 = PaperInstanceQuestionCopy_2019.PAPER_INSTANCE_QUESTION_COPY_2019;

    /**
     * 试卷实例与试题副本关联_2020
     */
    public static final PaperInstanceQuestionCopy_2020 PAPER_INSTANCE_QUESTION_COPY_2020 = PaperInstanceQuestionCopy_2020.PAPER_INSTANCE_QUESTION_COPY_2020;

    /**
     * 试卷实例与试题副本关联_2021
     */
    public static final PaperInstanceQuestionCopy_2021 PAPER_INSTANCE_QUESTION_COPY_2021 = PaperInstanceQuestionCopy_2021.PAPER_INSTANCE_QUESTION_COPY_2021;

    /**
     * 试卷实例与试题副本关联_2022
     */
    public static final PaperInstanceQuestionCopy_2022 PAPER_INSTANCE_QUESTION_COPY_2022 = PaperInstanceQuestionCopy_2022.PAPER_INSTANCE_QUESTION_COPY_2022;

    /**
     * 试卷实例与试题副本关联_2023
     */
    public static final PaperInstanceQuestionCopy_2023 PAPER_INSTANCE_QUESTION_COPY_2023 = PaperInstanceQuestionCopy_2023.PAPER_INSTANCE_QUESTION_COPY_2023;

    /**
     * 试卷实例与试题副本关联_2024
     */
    public static final PaperInstanceQuestionCopy_2024 PAPER_INSTANCE_QUESTION_COPY_2024 = PaperInstanceQuestionCopy_2024.PAPER_INSTANCE_QUESTION_COPY_2024;

    /**
     * 试卷实例与试题副本关联_2025
     */
    public static final PaperInstanceQuestionCopy_2025 PAPER_INSTANCE_QUESTION_COPY_2025 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2025.PAPER_INSTANCE_QUESTION_COPY_2025;

    /**
     * 试卷实例与试题副本关联_2026
     */
    public static final PaperInstanceQuestionCopy_2026 PAPER_INSTANCE_QUESTION_COPY_2026 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2026.PAPER_INSTANCE_QUESTION_COPY_2026;

    /**
     * 试卷实例与试题副本关联_2027
     */
    public static final PaperInstanceQuestionCopy_2027 PAPER_INSTANCE_QUESTION_COPY_2027 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2027.PAPER_INSTANCE_QUESTION_COPY_2027;

    /**
     * 试卷实例与试题副本关联_2028
     */
    public static final PaperInstanceQuestionCopy_2028 PAPER_INSTANCE_QUESTION_COPY_2028 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2028.PAPER_INSTANCE_QUESTION_COPY_2028;

    /**
     * 试卷实例与试题副本关联_2029
     */
    public static final PaperInstanceQuestionCopy_2029 PAPER_INSTANCE_QUESTION_COPY_2029 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2029.PAPER_INSTANCE_QUESTION_COPY_2029;

    /**
     * 试卷实例与试题副本关联_2030
     */
    public static final PaperInstanceQuestionCopy_2030 PAPER_INSTANCE_QUESTION_COPY_2030 = com.zxy.product.exam.jooq.tables.PaperInstanceQuestionCopy_2030.PAPER_INSTANCE_QUESTION_COPY_2030;

    /**
     * PCCW HR接口调用结果
     */
    public static final PccwResult PCCW_RESULT = PccwResult.PCCW_RESULT;

    /**
     * 个性化学习专区对应试题目录
     */
    public static final PersonalDepot PERSONAL_DEPOT = PersonalDepot.PERSONAL_DEPOT;

    /**
     * 认证考试专业表
     */
    public static final Profession PROFESSION = Profession.PROFESSION;

    /**
     * 等级表，不再与子专业关联
     */
    public static final ProfessionLevel PROFESSION_LEVEL = ProfessionLevel.PROFESSION_LEVEL;

    /**
     * The table <code>exam.t_question</code>.
     */
    public static final Question QUESTION = Question.QUESTION;

    /**
     * The table <code>exam.t_question_attr</code>.
     */
    public static final QuestionAttr QUESTION_ATTR = QuestionAttr.QUESTION_ATTR;

    /**
     * The table <code>exam.t_question_attr_copy</code>.
     */
    public static final QuestionAttrCopy QUESTION_ATTR_COPY = QuestionAttrCopy.QUESTION_ATTR_COPY;

    /**
     * 试题副本
     */
    public static final QuestionCopy QUESTION_COPY = QuestionCopy.QUESTION_COPY;

    /**
     * The table <code>exam.t_question_count</code>.
     */
    public static final QuestionCount QUESTION_COUNT = QuestionCount.QUESTION_COUNT;

    /**
     * The table <code>exam.t_question_depot</code>.
     */
    public static final QuestionDepot QUESTION_DEPOT = QuestionDepot.QUESTION_DEPOT;

    /**
     * 试题易错率统计
     */
    public static final QuestionErrorRate QUESTION_ERROR_RATE = QuestionErrorRate.QUESTION_ERROR_RATE;

    /**
     * The table <code>exam.t_question_recovery</code>.
     */
    public static final QuestionRecovery QUESTION_RECOVERY = QuestionRecovery.QUESTION_RECOVERY;

    /**
     * The table <code>exam.t_research_answer_record</code>.
     */
    public static final ResearchAnswerRecord RESEARCH_ANSWER_RECORD = ResearchAnswerRecord.RESEARCH_ANSWER_RECORD;

    /**
     * The table <code>exam.t_research_history_result</code>.
     */
    public static final ResearchHistoryResult RESEARCH_HISTORY_RESULT = ResearchHistoryResult.RESEARCH_HISTORY_RESULT;

    /**
     * The table <code>exam.t_research_questionary</code>.
     */
    public static final ResearchQuestionary RESEARCH_QUESTIONARY = ResearchQuestionary.RESEARCH_QUESTIONARY;

    /**
     * The table <code>exam.t_research_record</code>.
     */
    public static final ResearchRecord RESEARCH_RECORD = ResearchRecord.RESEARCH_RECORD;

    /**
     * The table <code>exam.t_research_summary</code>.
     */
    public static final ResearchSummary RESEARCH_SUMMARY = ResearchSummary.RESEARCH_SUMMARY;

    /**
     * t_sync_info同步试卷临时信息存储
     */
    public static final SyncInfo SYNC_INFO = SyncInfo.SYNC_INFO;

    /**
     * 今日答题表
     */
    public static final TodayQuestion TODAY_QUESTION = TodayQuestion.TODAY_QUESTION;

    /**
     * 考试调研-任务关联表（学习计划）
     */
    public static final ExamStudyPlanConfig EXAM_STUDY_PLAN_CONFIG = ExamStudyPlanConfig.EXAM_STUDY_PLAN_CONFIG;

    /**
     * The table <code>exam.t_delete_data_exam</code>.
     */
    public static final DeleteDataExam DELETE_DATA_EXAM = DeleteDataExam.DELETE_DATA_EXAM;

    /**
     * 学习风格测评表
     */
    public static final LearningStyleEvaluation LEARNING_STYLE_EVALUATION = LearningStyleEvaluation.LEARNING_STYLE_EVALUATION;

    /**
     * 考试试卷附件表
     */
    public static final ExamPaperAttachment EXAM_PAPER_ATTACHMENT = ExamPaperAttachment.EXAM_PAPER_ATTACHMENT;

    /**
     * 考试关联课程
     */
    public static final RelevanceCourseExam RELEVANCE_COURSE_EXAM = RelevanceCourseExam.RELEVANCE_COURSE_EXAM;


    //todo 以下是exam-stu schema中的表信息
    /**
     * 问题记录流水表
     */
    public static final AnswerRecordProcess_0 ANSWER_RECORD_PROCESS_0 = AnswerRecordProcess_0.ANSWER_RECORD_PROCESS_0;

    public static final AnswerRecordProcess_1 ANSWER_RECORD_PROCESS_1 = AnswerRecordProcess_1.ANSWER_RECORD_PROCESS_1;

    public static final AnswerRecordProcess_2 ANSWER_RECORD_PROCESS_2 = AnswerRecordProcess_2.ANSWER_RECORD_PROCESS_2;

    public static final AnswerRecordProcess_3 ANSWER_RECORD_PROCESS_3 = AnswerRecordProcess_3.ANSWER_RECORD_PROCESS_3;

    public static final AnswerRecordProcess_4 ANSWER_RECORD_PROCESS_4 = AnswerRecordProcess_4.ANSWER_RECORD_PROCESS_4;

    public static final AnswerRecordProcess_5 ANSWER_RECORD_PROCESS_5 = AnswerRecordProcess_5.ANSWER_RECORD_PROCESS_5;

    public static final AnswerRecordProcess_6 ANSWER_RECORD_PROCESS_6 = AnswerRecordProcess_6.ANSWER_RECORD_PROCESS_6;

    public static final AnswerRecordProcess_7 ANSWER_RECORD_PROCESS_7 = AnswerRecordProcess_7.ANSWER_RECORD_PROCESS_7;

    public static final AnswerRecordProcess_8 ANSWER_RECORD_PROCESS_8 = AnswerRecordProcess_8.ANSWER_RECORD_PROCESS_8;

    public static final AnswerRecordProcess_9 ANSWER_RECORD_PROCESS_9 = AnswerRecordProcess_9.ANSWER_RECORD_PROCESS_9;

    public static final AnswerRecordProcess ANSWER_RECORD_PROCESS = AnswerRecordProcess.ANSWER_RECORD_PROCESS;

    /**
     * The table <code>exam.t_answer_record</code>.
     */
    public static final AnswerRecord ANSWER_RECORD = AnswerRecord.ANSWER_RECORD;

    /**
     * 答案记录表_2019
     */
    public static final AnswerRecord_2019 ANSWER_RECORD_2019 = AnswerRecord_2019.ANSWER_RECORD_2019;

    /**
     * 答案记录表_2020
     */
    public static final AnswerRecord_2020 ANSWER_RECORD_2020 = AnswerRecord_2020.ANSWER_RECORD_2020;

    /**
     * 答案记录表_2021
     */
    public static final AnswerRecord_2021 ANSWER_RECORD_2021 = AnswerRecord_2021.ANSWER_RECORD_2021;

    /**
     * 答案记录表_2022
     */
    public static final AnswerRecord_2022 ANSWER_RECORD_2022 = AnswerRecord_2022.ANSWER_RECORD_2022;

    /**
     * 答案记录表_2023
     */
    public static final AnswerRecord_2023 ANSWER_RECORD_2023 = AnswerRecord_2023.ANSWER_RECORD_2023;

    /**
     * 答案记录表_2024
     */
    public static final AnswerRecord_2024 ANSWER_RECORD_2024 = AnswerRecord_2024.ANSWER_RECORD_2024;

    /**
     * 答案记录表_2025
     */
    public static final AnswerRecord_2025 ANSWER_RECORD_2025 = com.zxy.product.exam.jooq.tables.AnswerRecord_2025.ANSWER_RECORD_2025;

    /**
     * 答案记录表_2026
     */
    public static final AnswerRecord_2026 ANSWER_RECORD_2026 = com.zxy.product.exam.jooq.tables.AnswerRecord_2026.ANSWER_RECORD_2026;

    /**
     * 答案记录表_2027
     */
    public static final AnswerRecord_2027 ANSWER_RECORD_2027 = com.zxy.product.exam.jooq.tables.AnswerRecord_2027.ANSWER_RECORD_2027;

    /**
     * 答案记录表_2028
     */
    public static final AnswerRecord_2028 ANSWER_RECORD_2028 = com.zxy.product.exam.jooq.tables.AnswerRecord_2028.ANSWER_RECORD_2028;

    /**
     * 答案记录表_2029
     */
    public static final AnswerRecord_2029 ANSWER_RECORD_2029 = com.zxy.product.exam.jooq.tables.AnswerRecord_2029.ANSWER_RECORD_2029;

    /**
     * 答案记录表_2030
     */
    public static final AnswerRecord_2030 ANSWER_RECORD_2030 = com.zxy.product.exam.jooq.tables.AnswerRecord_2030.ANSWER_RECORD_2030;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace EXAM_RECORD_FACE = ExamRecordFace.EXAM_RECORD_FACE;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2017 EXAM_RECORD_FACE_2017 = ExamRecordFace_2017.EXAM_RECORD_FACE_2017;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2018 EXAM_RECORD_FACE_2018 = ExamRecordFace_2018.EXAM_RECORD_FACE_2018;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2019 EXAM_RECORD_FACE_2019 = ExamRecordFace_2019.EXAM_RECORD_FACE_2019;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2020 EXAM_RECORD_FACE_2020 = ExamRecordFace_2020.EXAM_RECORD_FACE_2020;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2021 EXAM_RECORD_FACE_2021 = ExamRecordFace_2021.EXAM_RECORD_FACE_2021;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2022 EXAM_RECORD_FACE_2022 = ExamRecordFace_2022.EXAM_RECORD_FACE_2022;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2023 EXAM_RECORD_FACE_2023 = ExamRecordFace_2023.EXAM_RECORD_FACE_2023;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2024 EXAM_RECORD_FACE_2024 = ExamRecordFace_2024.EXAM_RECORD_FACE_2024;


    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2025 EXAM_RECORD_FACE_2025 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2025.EXAM_RECORD_FACE_2025;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2026 EXAM_RECORD_FACE_2026 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2026.EXAM_RECORD_FACE_2026;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2027 EXAM_RECORD_FACE_2027 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2027.EXAM_RECORD_FACE_2027;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2028 EXAM_RECORD_FACE_2028 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2028.EXAM_RECORD_FACE_2028;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2029 EXAM_RECORD_FACE_2029 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2029.EXAM_RECORD_FACE_2029;

    /**
     * 人脸监考记录表
     */
    public static final ExamRecordFace_2030 EXAM_RECORD_FACE_2030 = com.zxy.product.exam.jooq.tables.ExamRecordFace_2030.EXAM_RECORD_FACE_2030;

    /**
     * 考试注册表
     */
    public static final ExamRegist EXAM_REGIST = ExamRegist.EXAM_REGIST;

    /**
     * 考试注册表_2017
     */
    public static final ExamRegist_2017 EXAM_REGIST_2017 = ExamRegist_2017.EXAM_REGIST_2017;

    /**
     * 考试注册表_2018
     */
    public static final ExamRegist_2018 EXAM_REGIST_2018 = ExamRegist_2018.EXAM_REGIST_2018;

    /**
     * 考试注册表_2019
     */
    public static final ExamRegist_2019 EXAM_REGIST_2019 = ExamRegist_2019.EXAM_REGIST_2019;

    /**
     * 考试注册表_2020
     */
    public static final ExamRegist_2020 EXAM_REGIST_2020 = ExamRegist_2020.EXAM_REGIST_2020;

    /**
     * 考试注册表_2021
     */
    public static final ExamRegist_2021 EXAM_REGIST_2021 = ExamRegist_2021.EXAM_REGIST_2021;

    /**
     * 考试注册表_2022
     */
    public static final ExamRegist_2022 EXAM_REGIST_2022 = ExamRegist_2022.EXAM_REGIST_2022;

    /**
     * 考试注册表_2023
     */
    public static final ExamRegist_2023 EXAM_REGIST_2023 = ExamRegist_2023.EXAM_REGIST_2023;

    /**
     * 考试注册表_2024
     */
    public static final ExamRegist_2024 EXAM_REGIST_2024 = ExamRegist_2024.EXAM_REGIST_2024;


    /**
     * 考试注册表_2025
     */
    public static final ExamRegist_2025 EXAM_REGIST_2025 = com.zxy.product.exam.jooq.tables.ExamRegist_2025.EXAM_REGIST_2025;

    /**
     * 考试注册表_2026
     */
    public static final ExamRegist_2026 EXAM_REGIST_2026 = com.zxy.product.exam.jooq.tables.ExamRegist_2026.EXAM_REGIST_2026;

    /**
     * 考试注册表_2027
     */
    public static final ExamRegist_2027 EXAM_REGIST_2027 = com.zxy.product.exam.jooq.tables.ExamRegist_2027.EXAM_REGIST_2027;

    /**
     * 考试注册表_2028
     */
    public static final ExamRegist_2028 EXAM_REGIST_2028 = com.zxy.product.exam.jooq.tables.ExamRegist_2028.EXAM_REGIST_2028;

    /**
     * 考试注册表_2029
     */
    public static final ExamRegist_2029 EXAM_REGIST_2029 = com.zxy.product.exam.jooq.tables.ExamRegist_2029.EXAM_REGIST_2029;

    /**
     * 考试注册表_2030
     */
    public static final ExamRegist_2030 EXAM_REGIST_2030 = com.zxy.product.exam.jooq.tables.ExamRegist_2030.EXAM_REGIST_2030;


    /**
     * The table <code>exam.t_exam_record</code>.
     */
    public static final ExamRecord EXAM_RECORD = ExamRecord.EXAM_RECORD;

    /**
     * 考试记录表_2017
     */
    public static final ExamRecord_2017 EXAM_RECORD_2017 = ExamRecord_2017.EXAM_RECORD_2017;

    /**
     * 考试记录表_2018
     */
    public static final ExamRecord_2018 EXAM_RECORD_2018 = ExamRecord_2018.EXAM_RECORD_2018;

    /**
     * 考试记录表_2019
     */
    public static final ExamRecord_2019 EXAM_RECORD_2019 = ExamRecord_2019.EXAM_RECORD_2019;

    /**
     * 考试记录表_2020
     */
    public static final ExamRecord_2020 EXAM_RECORD_2020 = ExamRecord_2020.EXAM_RECORD_2020;

    /**
     * 考试记录表_2021
     */
    public static final ExamRecord_2021 EXAM_RECORD_2021 = ExamRecord_2021.EXAM_RECORD_2021;

    /**
     * 考试记录表_2022
     */
    public static final ExamRecord_2022 EXAM_RECORD_2022 = ExamRecord_2022.EXAM_RECORD_2022;

    /**
     * 考试记录表_2023
     */
    public static final ExamRecord_2023 EXAM_RECORD_2023 = ExamRecord_2023.EXAM_RECORD_2023;

    /**
     * 考试记录表_2024
     */
    public static final ExamRecord_2024 EXAM_RECORD_2024 = ExamRecord_2024.EXAM_RECORD_2024;

    /**
     * 考试记录表_2025
     */
    public static final ExamRecord_2025 EXAM_RECORD_2025 = com.zxy.product.exam.jooq.tables.ExamRecord_2025.EXAM_RECORD_2025;

    /**
     * 考试记录表_2026
     */
    public static final ExamRecord_2026 EXAM_RECORD_2026 = com.zxy.product.exam.jooq.tables.ExamRecord_2026.EXAM_RECORD_2026;

    /**
     * 考试记录表_2027
     */
    public static final ExamRecord_2027 EXAM_RECORD_2027 = com.zxy.product.exam.jooq.tables.ExamRecord_2027.EXAM_RECORD_2027;

    /**
     * 考试记录表_2028
     */
    public static final ExamRecord_2028 EXAM_RECORD_2028 = com.zxy.product.exam.jooq.tables.ExamRecord_2028.EXAM_RECORD_2028;

    /**
     * 考试记录表_2029
     */
    public static final ExamRecord_2029 EXAM_RECORD_2029 = com.zxy.product.exam.jooq.tables.ExamRecord_2029.EXAM_RECORD_2029;

    /**
     * 考试记录表_2030
     */
    public static final ExamRecord_2030 EXAM_RECORD_2030 = com.zxy.product.exam.jooq.tables.ExamRecord_2030.EXAM_RECORD_2030;

    /**
     * 个人信息信息模板表
     */
    public static final PersonalTemplate PERSONAL_TEMPLATE = PersonalTemplate.PERSONAL_TEMPLATE;

    /**
     * The table <code>exam.t_signup</code>.
     */
    public static final Signup SIGNUP = Signup.SIGNUP;

    /**
     * 考试报名记录表
     */
    public static final SignupRecord SIGNUP_RECORD = SignupRecord.SIGNUP_RECORD;

    /**
     * 报名认证信息表
     */
    public static final SignUpAuth SIGN_UP_AUTH = SignUpAuth.SIGN_UP_AUTH;

    /**
     * 移动云考试报名表
     */
    public static final CloudSignup CLOUD_SIGNUP = CloudSignup.CLOUD_SIGNUP;

    /**
     * 网格长考试报名表
     */
    public static final GridSignup GRID_SIGNUP = GridSignup.GRID_SIGNUP;

    /**
     * The table <code>exam.t_to_do</code>.
     */
    public static final ToDo TO_DO = ToDo.TO_DO;

    /**
     * The table <code>exam-stu.t_exam_online_log</code>.
     */
    public static final ExamOnlineLog EXAM_ONLINE_LOG = com.zxy.product.exam.jooq.tables.ExamOnlineLog.EXAM_ONLINE_LOG;

    /**
     * 数智测评结果表
     */
    public static final DigitalIntelligenceResult DIGITAL_INTELLIGENCE_RESULT = DigitalIntelligenceResult.DIGITAL_INTELLIGENCE_RESULT;
}
