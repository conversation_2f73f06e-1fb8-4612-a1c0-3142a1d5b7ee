/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.ExamStu;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ExamRecord_2019Record;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 考试记录表_2019
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ExamRecord_2019 extends TableImpl<ExamRecord_2019Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam-stu.t_exam_record_2019</code>
     */
    public static final ExamRecord_2019 EXAM_RECORD_2019 = new ExamRecord_2019();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ExamRecord_2019Record> getRecordType() {
        return ExamRecord_2019Record.class;
    }

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_id</code>.
     */
    public final TableField<ExamRecord_2019Record, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_member_id</code>. 员工id
     */
    public final TableField<ExamRecord_2019Record, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "员工id");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_organization_id</code>. 所属组织id
     */
    public final TableField<ExamRecord_2019Record, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属组织id");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_start_time</code>. 进入考试时间
     */
    public final TableField<ExamRecord_2019Record, Long> START_TIME = createField("f_start_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "进入考试时间");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_end_time</code>. 交卷截止时间
     */
    public final TableField<ExamRecord_2019Record, Long> END_TIME = createField("f_end_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "交卷截止时间");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_last_submit_time</code>. 最后提交时间
     */
    public final TableField<ExamRecord_2019Record, Long> LAST_SUBMIT_TIME = createField("f_last_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最后提交时间");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_score</code>. 成绩
     */
    public final TableField<ExamRecord_2019Record, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "成绩");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_client_type</code>. 客户端类型 1:pc,2:app
     */
    public final TableField<ExamRecord_2019Record, Integer> CLIENT_TYPE = createField("f_client_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "客户端类型 1:pc,2:app");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_status</code>. 状态 1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成
     */
    public final TableField<ExamRecord_2019Record, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态 1：未开始，2：进行中，4：交卷异常，5:待评卷，6：及格，7：不及格 8已完成");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_exam_id</code>. 关联考试id
     */
    public final TableField<ExamRecord_2019Record, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "关联考试id");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_paper_instance_id</code>. 试卷实例id
     */
    public final TableField<ExamRecord_2019Record, String> PAPER_INSTANCE_ID = createField("f_paper_instance_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试卷实例id");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_exam_number</code>. 第几次考试
     */
    public final TableField<ExamRecord_2019Record, Integer> EXAM_NUMBER = createField("f_exam_number", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "第几次考试");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_create_time</code>. 创建时间
     */
    public final TableField<ExamRecord_2019Record, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_submit_time</code>. 交卷时间
     */
    public final TableField<ExamRecord_2019Record, Long> SUBMIT_TIME = createField("f_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "交卷时间");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_duration</code>. 考试时长(分钟)
     */
    public final TableField<ExamRecord_2019Record, Long> DURATION = createField("f_duration", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "考试时长(分钟)");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_is_reset</code>. 是否重置
     */
    public final TableField<ExamRecord_2019Record, Integer> IS_RESET = createField("f_is_reset", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否重置");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_is_current</code>. 0:非当前，1：当前
     */
    public final TableField<ExamRecord_2019Record, Integer> IS_CURRENT = createField("f_is_current", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0:非当前，1：当前");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_is_finished</code>. 是否完成
     */
    public final TableField<ExamRecord_2019Record, Integer> IS_FINISHED = createField("f_is_finished", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否完成");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_exception_order</code>. 异常排序
     */
    public final TableField<ExamRecord_2019Record, Integer> EXCEPTION_ORDER = createField("f_exception_order", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "异常排序");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_order_content</code>.
     */
    public final TableField<ExamRecord_2019Record, String> ORDER_CONTENT = createField("f_order_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_exam_times</code>. 考试次数
     */
    public final TableField<ExamRecord_2019Record, Integer> EXAM_TIMES = createField("f_exam_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "考试次数");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_switch_times</code>. 切屏数
     */
    public final TableField<ExamRecord_2019Record, Integer> SWITCH_TIMES = createField("f_switch_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "切屏数");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_personal_code</code>. 个人码
     */
    public final TableField<ExamRecord_2019Record, Integer> PERSONAL_CODE = createField("f_personal_code", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "个人码");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_user_ip</code>. 用户ip
     */
    public final TableField<ExamRecord_2019Record, String> USER_IP = createField("f_user_ip", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ip");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_no_answer_count</code>. 未答数
     */
    public final TableField<ExamRecord_2019Record, Integer> NO_ANSWER_COUNT = createField("f_no_answer_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "未答数");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_answered_count</code>. 已答数
     */
    public final TableField<ExamRecord_2019Record, Integer> ANSWERED_COUNT = createField("f_answered_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "已答数");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_client_version</code>. 终端型号
     */
    public final TableField<ExamRecord_2019Record, String> CLIENT_VERSION = createField("f_client_version", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "终端型号");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_right_count</code>. 正确题数
     */
    public final TableField<ExamRecord_2019Record, Integer> RIGHT_COUNT = createField("f_right_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "正确题数");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_face_status</code>. 0 正常、1 人脸异常、2 标记正常、3标记异常
     */
    public final TableField<ExamRecord_2019Record, Integer> FACE_STATUS = createField("f_face_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0 正常、1 人脸异常、2 标记正常、3标记异常");

    /**
     * The column <code>exam-stu.t_exam_record_2019.f_modify_date</code>. 修改时间
     */
    public final TableField<ExamRecord_2019Record, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>exam-stu.t_exam_record_2019</code> table reference
     */
    public ExamRecord_2019() {
        this("t_exam_record_2019", null);
    }

    /**
     * Create an aliased <code>exam-stu.t_exam_record_2019</code> table reference
     */
    public ExamRecord_2019(String alias) {
        this(alias, EXAM_RECORD_2019);
    }

    private ExamRecord_2019(String alias, Table<ExamRecord_2019Record> aliased) {
        this(alias, aliased, null);
    }

    private ExamRecord_2019(String alias, Table<ExamRecord_2019Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "考试记录表_2019");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ExamStu.EXAM_STU_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ExamRecord_2019Record> getPrimaryKey() {
        return Keys.KEY_T_EXAM_RECORD_2019_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ExamRecord_2019Record>> getKeys() {
        return Arrays.<UniqueKey<ExamRecord_2019Record>>asList(Keys.KEY_T_EXAM_RECORD_2019_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ExamRecord_2019 as(String alias) {
        return new ExamRecord_2019(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ExamRecord_2019 rename(String name) {
        return new ExamRecord_2019(name, null);
    }
}
