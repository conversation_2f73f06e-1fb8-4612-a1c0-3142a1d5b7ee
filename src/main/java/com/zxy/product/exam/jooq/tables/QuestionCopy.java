/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.QuestionCopyRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 试题副本
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class QuestionCopy extends TableImpl<QuestionCopyRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_question_copy</code>
     */
    public static final QuestionCopy QUESTION_COPY = new QuestionCopy();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<QuestionCopyRecord> getRecordType() {
        return QuestionCopyRecord.class;
    }

    /**
     * The column <code>exam.t_question_copy.f_id</code>.
     */
    public final TableField<QuestionCopyRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_question_copy.f_question_id</code>.
     */
    public final TableField<QuestionCopyRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>exam.t_question_copy.f_create_time</code>.
     */
    public final TableField<QuestionCopyRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>exam.t_question_copy.f_type</code>. 类型
     */
    public final TableField<QuestionCopyRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "类型");

    /**
     * The column <code>exam.t_question_copy.f_content</code>.
     */
    public final TableField<QuestionCopyRecord, String> CONTENT = createField("f_content", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>exam.t_question_copy.f_is_subjective</code>. 是否主观题
     */
    public final TableField<QuestionCopyRecord, Integer> IS_SUBJECTIVE = createField("f_is_subjective", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否主观题");

    /**
     * The column <code>exam.t_question_copy.f_parent_id</code>. 父题
     */
    public final TableField<QuestionCopyRecord, String> PARENT_ID = createField("f_parent_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "父题");

    /**
     * The column <code>exam.t_question_copy.f_question_depot_id</code>. 题库
     */
    public final TableField<QuestionCopyRecord, String> QUESTION_DEPOT_ID = createField("f_question_depot_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "题库");

    /**
     * The column <code>exam.t_question_copy.f_difficulty</code>. 难度
     */
    public final TableField<QuestionCopyRecord, Integer> DIFFICULTY = createField("f_difficulty", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "难度");

    /**
     * The column <code>exam.t_question_copy.f_organization_id</code>. 所属部门
     */
    public final TableField<QuestionCopyRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "所属部门");

    /**
     * The column <code>exam.t_question_copy.f_score</code>. 分数
     */
    public final TableField<QuestionCopyRecord, Integer> SCORE = createField("f_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分数");

    /**
     * The column <code>exam.t_question_copy.f_error_rate</code>. 易错率
     */
    public final TableField<QuestionCopyRecord, Integer> ERROR_RATE = createField("f_error_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "易错率");

    /**
     * The column <code>exam.t_question_copy.f_mark_amount</code>. 收藏
     */
    public final TableField<QuestionCopyRecord, Integer> MARK_AMOUNT = createField("f_mark_amount", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "收藏");

    /**
     * The column <code>exam.t_question_copy.f_recovery_count</code>. 纠错
     */
    public final TableField<QuestionCopyRecord, Integer> RECOVERY_COUNT = createField("f_recovery_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "纠错");

    /**
     * The column <code>exam.t_question_copy.f_exam_id</code>.
     */
    public final TableField<QuestionCopyRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * Create a <code>exam.t_question_copy</code> table reference
     */
    public QuestionCopy() {
        this("t_question_copy", null);
    }

    /**
     * Create an aliased <code>exam.t_question_copy</code> table reference
     */
    public QuestionCopy(String alias) {
        this(alias, QUESTION_COPY);
    }

    private QuestionCopy(String alias, Table<QuestionCopyRecord> aliased) {
        this(alias, aliased, null);
    }

    private QuestionCopy(String alias, Table<QuestionCopyRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "试题副本");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<QuestionCopyRecord> getPrimaryKey() {
        return Keys.KEY_T_QUESTION_COPY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<QuestionCopyRecord>> getKeys() {
        return Arrays.<UniqueKey<QuestionCopyRecord>>asList(Keys.KEY_T_QUESTION_COPY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public QuestionCopy as(String alias) {
        return new QuestionCopy(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public QuestionCopy rename(String name) {
        return new QuestionCopy(name, null);
    }
}
