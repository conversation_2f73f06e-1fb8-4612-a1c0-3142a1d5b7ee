/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.PersonalDepotRecord;
import org.jooq.*;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;
import com.zxy.product.exam.jooq.Exam;

/**
 * 个性化学习专区对应试题目录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PersonalDepot extends TableImpl<PersonalDepotRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_personal_depot</code>
     */
    public static final PersonalDepot PERSONAL_DEPOT = new PersonalDepot();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PersonalDepotRecord> getRecordType() {
        return PersonalDepotRecord.class;
    }

    /**
     * The column <code>exam.t_personal_depot.f_id</code>. ID
     */
    public final TableField<PersonalDepotRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>exam.t_personal_depot.f_create_time</code>. 创建时间
     */
    public final TableField<PersonalDepotRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_personal_depot.f_exam_id</code>. 考试id
     */
    public final TableField<PersonalDepotRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_personal_depot.f_question_dport_id</code>. 试题目录id
     */
    public final TableField<PersonalDepotRecord, String> QUESTION_DPORT_ID = createField("f_question_dport_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "试题目录id");

    /**
     * The column <code>exam.t_personal_depot.f_type</code>. 学习专区(0:新动能, 1:chbn, 2:智慧中台)
     */
    public final TableField<PersonalDepotRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习专区(0:新动能, 1:chbn, 2:智慧中台)");

    /**
     * Create a <code>exam.t_personal_depot</code> table reference
     */
    public PersonalDepot() {
        this("t_personal_depot", null);
    }

    /**
     * Create an aliased <code>exam.t_personal_depot</code> table reference
     */
    public PersonalDepot(String alias) {
        this(alias, PERSONAL_DEPOT);
    }

    private PersonalDepot(String alias, Table<PersonalDepotRecord> aliased) {
        this(alias, aliased, null);
    }

    private PersonalDepot(String alias, Table<PersonalDepotRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "个性化学习专区对应试题目录");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<PersonalDepotRecord> getPrimaryKey() {
        return Keys.KEY_T_PERSONAL_DEPOT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<PersonalDepotRecord>> getKeys() {
        return Arrays.<UniqueKey<PersonalDepotRecord>>asList(Keys.KEY_T_PERSONAL_DEPOT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public PersonalDepot as(String alias) {
        return new PersonalDepot(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public PersonalDepot rename(String name) {
        return new PersonalDepot(name, null);
    }
}
