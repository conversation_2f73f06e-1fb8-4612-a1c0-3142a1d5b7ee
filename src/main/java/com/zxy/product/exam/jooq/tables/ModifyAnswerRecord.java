/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.exam.jooq.tables;


import com.zxy.product.exam.jooq.Exam;
import com.zxy.product.exam.jooq.Keys;
import com.zxy.product.exam.jooq.tables.records.ModifyAnswerRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 增量提交答题记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ModifyAnswerRecord extends TableImpl<ModifyAnswerRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>exam.t_modify_answer_record</code>
     */
    public static final ModifyAnswerRecord MODIFY_ANSWER_RECORD = new ModifyAnswerRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ModifyAnswerRecordRecord> getRecordType() {
        return ModifyAnswerRecordRecord.class;
    }

    /**
     * The column <code>exam.t_modify_answer_record.f_id</code>.
     */
    public final TableField<ModifyAnswerRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>exam.t_modify_answer_record.f_create_time</code>. 创建时间
     */
    public final TableField<ModifyAnswerRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>exam.t_modify_answer_record.f_exam_id</code>. 考试id
     */
    public final TableField<ModifyAnswerRecordRecord, String> EXAM_ID = createField("f_exam_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试id");

    /**
     * The column <code>exam.t_modify_answer_record.f_member_id</code>. 人员id
     */
    public final TableField<ModifyAnswerRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "人员id");

    /**
     * The column <code>exam.t_modify_answer_record.f_exam_record_id</code>. 考试纪律id
     */
    public final TableField<ModifyAnswerRecordRecord, String> EXAM_RECORD_ID = createField("f_exam_record_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "考试纪律id");

    /**
     * The column <code>exam.t_modify_answer_record.f_modify_json</code>. 增量提交记录
     */
    public final TableField<ModifyAnswerRecordRecord, String> MODIFY_JSON = createField("f_modify_json", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "增量提交记录");

    /**
     * The column <code>exam.t_modify_answer_record.f_full_answer_json</code>. 全量json
     */
    public final TableField<ModifyAnswerRecordRecord, String> FULL_ANSWER_JSON = createField("f_full_answer_json", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "全量json");

    /**
     * The column <code>exam.t_modify_answer_record.f_submit_time</code>. 提交时间
     */
    public final TableField<ModifyAnswerRecordRecord, Long> SUBMIT_TIME = createField("f_submit_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "提交时间");

    /**
     * The column <code>exam.t_modify_answer_record.f_type</code>. 1:增量，2：全量
     */
    public final TableField<ModifyAnswerRecordRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1:增量，2：全量");

    /**
     * The column <code>exam.t_modify_answer_record.f_answered_count</code>. 已答次数
     */
    public final TableField<ModifyAnswerRecordRecord, Integer> ANSWERED_COUNT = createField("f_answered_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "已答次数");

    /**
     * Create a <code>exam.t_modify_answer_record</code> table reference
     */
    public ModifyAnswerRecord() {
        this("t_modify_answer_record", null);
    }

    /**
     * Create an aliased <code>exam.t_modify_answer_record</code> table reference
     */
    public ModifyAnswerRecord(String alias) {
        this(alias, MODIFY_ANSWER_RECORD);
    }

    private ModifyAnswerRecord(String alias, Table<ModifyAnswerRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ModifyAnswerRecord(String alias, Table<ModifyAnswerRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "增量提交答题记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return Exam.EXAM_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ModifyAnswerRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_MODIFY_ANSWER_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ModifyAnswerRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ModifyAnswerRecordRecord>>asList(Keys.KEY_T_MODIFY_ANSWER_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ModifyAnswerRecord as(String alias) {
        return new ModifyAnswerRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ModifyAnswerRecord rename(String name) {
        return new ModifyAnswerRecord(name, null);
    }
}
