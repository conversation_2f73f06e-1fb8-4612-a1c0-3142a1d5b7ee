package com.zxy.product.exam.content;

import java.io.Serializable;
import java.util.List;

/**
 * 接口调用返回码实体类
 */
public class MessageCode  implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -7999006306263106587L;

    private int code;
    private String message;
    private MsgData data;

    private List<MsgData> datas;

    public static final int CODE_ERROR_EXCEPTION = 501;
    public static final String MSG_ERROR_EXCEPTION = "系统异常，请稍后重试";

    public static final int CODE_ERROR_NO_EXAM = 502;
    public static final String MSG_ERROR_NO_EXAM = "未找到对应考试";

    public static final int CODE_ERROR_MULTIPLE_EXAM = 503;
    public static final String MSG_EERROR_MULTIPLE_EXAM = "一份试卷关联多场考试";

    public static final int CODE_ERROR_VALIDATE_MISTAKE = 504;
    public static final String MSG_ERROR_VALIDATE_MISTAKE = "专业、子专业、等级或者设备型号未找到";

    public static final int CODE_ERROR_NO_KNOWLEDGE = 505;
    public static final String MSG_ERROR_NO_KNOWLEDGE = "试题没有知识点或技能元素";

    public static final int CODE_SUCESS_SYNC = 200;
    public static final String MSG_SUCESS_SYNC = "成功";




    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MsgData getData() {
        return data;
    }

    public void setData(MsgData data) {
        this.data = data;
    }

    public List<MsgData> getDatas() {
        return datas;
    }

    public void setDatas(List<MsgData> datas) {
        this.datas = datas;
    }
}
