package com.zxy.product.exam.content;

public enum LearningStyleEnmu {
//    视觉型—>听觉型—>读写型—>动觉型
    VIEWING("viewing",4),
    HEARING("hearing",3),
    RW("rw",2),
    ACTION("action",1),
    ;
    private String name;
    private Integer weight;

    LearningStyleEnmu(String name, Integer weight) {
        this.name = name;
        this.weight = weight;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public static LearningStyleEnmu getByName(String name){
        for (LearningStyleEnmu learningStyleEnmu : LearningStyleEnmu.values()) {
            if (learningStyleEnmu.getName().equals(name)) {
                return learningStyleEnmu;
            }
        }
        return null;
    }
}
