stages:
  - build

variables:
  PROJECT_NAME: exam
  PROJECT_VERSION: cmu-9.6.0
  VERSION: $CI_BUILD_REF_NAME
  POST_ADDRESS: cmudev9

build:
  stage: build
  script:
    - mvn clean compile -U
  except:
    - develop
    - /^release-.*$/
    - master
#
build-develop:
  stage: build
  only:
    - develop
  script:
    - mvn -N deploy
    - cd "${PROJECT_NAME}-api" && mvn -U clean deploy && cd ..
    - mvn -U clean package

build-release:
  stage: build
  only:
    - /^release-.*$/
  script:
    - mvn -N deploy
    - cd "${PROJECT_NAME}-api" && mvn -U clean deploy && cd ..
    - mvn -U clean package

build-pre-master:
  stage: build
  only:
    - pre-master
  script:
    - mvn -N deploy
    - cd "${PROJECT_NAME}-api" && mvn -U clean deploy && cd ..
    - mvn -U clean package    

build-master:
  stage: build
  only:
    - master
  script:
    - mvn -N deploy
    - cd "${PROJECT_NAME}-api" && mvn -U clean deploy && cd ..
    - mvn -U clean package
