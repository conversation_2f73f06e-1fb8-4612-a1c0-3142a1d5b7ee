package com.zxy.product.exam.async.task.szfn.consumer;

import com.zxy.product.exam.async.util.SzfnSftpUtil;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.QuestionCopy;
import com.zxy.product.exam.entity.szfn.UserExamProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

/**
 * 数智赋能考试档案数据消费者
 * 等待所有生产者完成后统一上传数据
 */
@Component
@Scope("prototype")
public class SzfnExamProfileConsumer implements Runnable {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SzfnExamProfileConsumer.class);
    
    // 常量定义
    private static final int PROGRESS_LOG_INTERVAL = 100;
    private static final int MAX_SEQUENCE_FOR_PROFILE = 15;
    
    // 题目类型常量
    private static final int QUESTION_TYPE_SINGLE_CHOICE = 1;
    private static final int QUESTION_TYPE_MULTIPLE_CHOICE = 2;
    private static final int QUESTION_TYPE_FILL_BLANK = 4;
    
    // 档案类型常量
    private static final int PROFILE_TYPE_BASIC_INFO = 0;
    private static final int PROFILE_TYPE_POSITION_INFO = 1;
    private static final int PROFILE_TYPE_SKILL_CERT = 2;
    private static final int PROFILE_TYPE_SUPPLEMENT = 3;
    
    private LinkedBlockingQueue<ExamRecord> queue;
    private ExamRecord endMarker;
    
    @Autowired
    private SzfnSftpUtil szfnSftpUtil;

    @Value("${szfn.exam.subject.id:}")
    private String szfnSubjectId;
    
    /**
     * 初始化消费者参数
     */
    public void init(LinkedBlockingQueue<ExamRecord> queue, ExamRecord endMarker) {
        this.queue = queue;
        this.endMarker = endMarker;
    }
    
    @Override
    public void run() {
        try {
            LOGGER.info("数智赋能考试档案消费者开始执行，等待所有生产者完成");

            // 在任务开始时生成时间参数
            Date currentTime = new Date();
            String dataDate = new SimpleDateFormat("yyyyMMdd").format(currentTime);

            // 获取当前小时并调整：如果是奇数就向下取最近的偶数（1-0；3-2）
            int currentHour = Integer.parseInt(new SimpleDateFormat("HH").format(currentTime));
            int adjustedHour = currentHour % 2 == 0 ? currentHour : currentHour - 1;
            String hour = String.format("%02d", adjustedHour);

            long timestamp = currentTime.getTime();

            LOGGER.info("任务开始时间参数 - dataDate: {}, hour: {}, timestamp: {}", dataDate, hour, timestamp);

            List<UserExamProfile> allProfiles = processExamRecords();

            LOGGER.info("数据转换完成，共转换到 {} 条考试档案数据，开始上传", allProfiles.size());

            uploadData(allProfiles, dataDate, hour, timestamp);

            LOGGER.info("数智赋能考试档案消费者完成，共处理 {} 条数据", allProfiles.size());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("数智赋能考试档案消费者被中断", e);
        } catch (Exception e) {
            LOGGER.error("数智赋能考试档案消费者执行异常", e);
        }
    }
    
    /**
     * 处理考试记录队列
     */
    private List<UserExamProfile> processExamRecords() throws InterruptedException {
        List<UserExamProfile> allProfiles = new ArrayList<>();

        while (!Thread.currentThread().isInterrupted()) {
            try {
                ExamRecord examRecord = queue.take();

                if (examRecord == endMarker) {
                    LOGGER.info("接收到结束标识对象，所有生产者已完成，开始上传数据");
                    break;
                }

                try {
                    UserExamProfile profile = convertExamRecordToProfile(examRecord);
                    if (profile != null) {
                        allProfiles.add(profile);
                    }
                } catch (Exception e) {
                    LOGGER.error("转换考试记录失败，examRecordId: {}", examRecord.getId(), e);
                }

                logProgressIfNeeded(allProfiles.size());

            } catch (InterruptedException e) {
                LOGGER.warn("消费者线程被中断，当前已处理 {} 条记录", allProfiles.size());
                Thread.currentThread().interrupt();
                throw e;
            }
        }

        return allProfiles;
    }
    
    /**
     * 定期输出进度日志
     */
    private void logProgressIfNeeded(int processedCount) {
        if (processedCount % PROGRESS_LOG_INTERVAL == 0) {
            LOGGER.info("已转换 {} 条考试档案数据", processedCount);
        }
    }
    
    /**
     * 上传UserExamProfile数据到SFTP
     * @param userExamProfiles 用户考试档案数据
     * @param dataDate 数据日期
     * @param hour 小时
     * @param timestamp 时间戳
     */
    private void uploadData(List<UserExamProfile> userExamProfiles, String dataDate, String hour, long timestamp) {
        if (userExamProfiles.isEmpty()) {
            LOGGER.info("没有数据需要上传");
            return;
        }

        try {
            LOGGER.info("开始上传 {} 条考试档案数据到SFTP，使用时间参数 - dataDate: {}, hour: {}, timestamp: {}",
                       userExamProfiles.size(), dataDate, hour, timestamp);

            boolean success = szfnSftpUtil.uploadFile(userExamProfiles, dataDate, hour, timestamp);

            if (success) {
                LOGGER.info("成功上传 {} 条考试档案数据到SFTP", userExamProfiles.size());
            } else {
                LOGGER.error("上传 {} 条考试档案数据到SFTP失败", userExamProfiles.size());
            }

        } catch (Exception e) {
            LOGGER.error("上传考试档案数据到SFTP异常", e);
        }
    }
    
    /**
     * 将单个ExamRecord转换为UserExamProfile
     */
    private UserExamProfile convertExamRecordToProfile(ExamRecord examRecord) {
        if (examRecord == null || examRecord.getQuestionCopyList() == null) {
            return null;
        }
        
        UserExamProfile profile = new UserExamProfile();
        profile.setSubjectId(szfnSubjectId);
        profile.setUserId(examRecord.getMemberId());

        List<UserExamProfile.AnswerItem> answerItems = examRecord.getQuestionCopyList()
                .stream()
                .map(this::convertQuestionCopyToAnswerItem)
                .filter(Objects::nonNull)
                .sorted(this::compareAnswerItems)
                .collect(Collectors.toList());
        
        profile.getAnswerItems().addAll(answerItems);
        return profile;
    }
    
    /**
     * 将QuestionCopy转换为AnswerItem
     */
    private UserExamProfile.AnswerItem convertQuestionCopyToAnswerItem(QuestionCopy questionCopy) {
        if (questionCopy == null) {
            return null;
        }
        
        UserExamProfile.AnswerItem answerItem = new UserExamProfile.AnswerItem();
        answerItem.setSequence(questionCopy.getSequence());
        answerItem.setScore(getScoreBySequence(questionCopy));
        answerItem.setType(getTypeBySequence(questionCopy.getSequence()));
        answerItem.setAnswer(getAnswerFromQuestionAttrs(questionCopy));
        
        return answerItem;
    }
    
    /**
     * 比较AnswerItem的排序
     */
    private int compareAnswerItems(UserExamProfile.AnswerItem item1, UserExamProfile.AnswerItem item2) {
        if (item1.getSequence() == null && item2.getSequence() == null) {
            return 0;
        }
        if (item1.getSequence() == null) {
            return 1;
        }
        if (item2.getSequence() == null) {
            return -1;
        }
        return item1.getSequence().compareTo(item2.getSequence());
    }

    /**
     * 从题目属性中获取答案
     */
    private String getAnswerFromQuestionAttrs(QuestionCopy questionCopy) {
        if (!isValidQuestionForAnswer(questionCopy)) {
            return null;
        }
        
        String originalAnswer = questionCopy.getAnswerRecord().getAnswer();
        Integer questionType = questionCopy.getType();
        
        if (questionType == QUESTION_TYPE_FILL_BLANK) {
            return originalAnswer;
        }
        
        if (questionCopy.getQuestionAttrCopys() == null || questionCopy.getQuestionAttrCopys().isEmpty()) {
            return originalAnswer;
        }
        
        return processAnswerByType(questionCopy, originalAnswer, questionType);
    }
    
    /**
     * 验证题目是否有效用于获取答案
     */
    private boolean isValidQuestionForAnswer(QuestionCopy questionCopy) {
        return questionCopy.getSequence() != null 
                && questionCopy.getSequence() <= MAX_SEQUENCE_FOR_PROFILE
                && questionCopy.getAnswerRecord() != null 
                && questionCopy.getAnswerRecord().getAnswer() != null;
    }
    
    /**
     * 根据题目类型处理答案
     */
    private String processAnswerByType(QuestionCopy questionCopy, String originalAnswer, Integer questionType) {
        if (questionType == QUESTION_TYPE_SINGLE_CHOICE) {
            return processSingleChoiceAnswer(questionCopy, originalAnswer);
        } else if (questionType == QUESTION_TYPE_MULTIPLE_CHOICE) {
            return processMultipleChoiceAnswer(questionCopy, originalAnswer);
        }
        return originalAnswer;
    }
    
    /**
     * 处理单选题答案
     */
    private String processSingleChoiceAnswer(QuestionCopy questionCopy, String originalAnswer) {
        return questionCopy.getQuestionAttrCopys().stream()
                .filter(attr -> originalAnswer.equals(attr.getName()))
                .findFirst()
                .map(attr -> attr.getValue())
                .orElse(originalAnswer);
    }
    
    /**
     * 处理多选题答案
     */
    private String processMultipleChoiceAnswer(QuestionCopy questionCopy, String originalAnswer) {
        String[] answerNames = originalAnswer.split(",");
        List<String> answerValues = new ArrayList<>();
        
        for (String answerName : answerNames) {
            String value = questionCopy.getQuestionAttrCopys().stream()
                    .filter(attr -> answerName.trim().equals(attr.getName()))
                    .findFirst()
                    .map(attr -> attr.getValue())
                    .orElse(answerName.trim());
            answerValues.add(value);
        }
        
        return String.join("|", answerValues);
    }

    /**
     * 根据sequence确定题目类型
     */
    private Integer getTypeBySequence(Integer sequence) {
        if (sequence == null) {
            return null;
        }
        
        if (sequence >= 0 && sequence <= 6) {
            return PROFILE_TYPE_BASIC_INFO;
        } else if (sequence > 6 && sequence <= 10) {
            return PROFILE_TYPE_POSITION_INFO;
        } else if (sequence > 10 && sequence <= 13) {
            return PROFILE_TYPE_SKILL_CERT;
        } else if (sequence > 13 && sequence <= 15) {
            return PROFILE_TYPE_SUPPLEMENT;
        } else {
            return ((sequence - 16) / 10) + 4;
        }
    }

    /**
     * 根据sequence处理分数
     */
    private Integer getScoreBySequence(QuestionCopy questionCopy) {
        if (questionCopy.getSequence() == null || questionCopy.getSequence() <= MAX_SEQUENCE_FOR_PROFILE) {
            return null;
        }
        
        return questionCopy.getAnswerRecord() != null ? questionCopy.getAnswerRecord().getScore() : null;
    }

}
